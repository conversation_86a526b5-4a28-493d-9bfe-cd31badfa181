import { CustomAuthError } from "../error/CustomAuthError.js";
/**
 * Base class for all auth flow errors.
 */
export declare abstract class AuthFlowErrorBase {
    errorData: CustomAuthError;
    constructor(errorData: CustomAuthError);
    protected isUserNotFoundError(): boolean;
    protected isUserInvalidError(): boolean;
    protected isUnsupportedChallengeTypeError(): boolean;
    protected isPasswordIncorrectError(): boolean;
    protected isInvalidCodeError(): boolean;
    protected isRedirectError(): boolean;
    protected isInvalidNewPasswordError(): boolean;
    protected isUserAlreadyExistsError(): boolean;
    protected isAttributeRequiredError(): boolean;
    protected isAttributeValidationFailedError(): boolean;
    protected isNoCachedAccountFoundError(): boolean;
    protected isTokenExpiredError(): boolean;
}
export declare abstract class AuthActionErrorBase extends AuthFlowErrorBase {
    /**
     * Checks if the error is due to the expired continuation token.
     * @returns {boolean} True if the error is due to the expired continuation token, false otherwise.
     */
    isTokenExpired(): boolean;
}
//# sourceMappingURL=AuthFlowErrorBase.d.ts.map