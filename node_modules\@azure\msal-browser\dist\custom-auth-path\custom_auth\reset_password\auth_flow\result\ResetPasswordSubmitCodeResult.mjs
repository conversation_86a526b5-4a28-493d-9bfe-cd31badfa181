/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { ResetPasswordSubmitCodeError } from '../error_type/ResetPasswordError.mjs';
import { ResetPasswordFailedState } from '../state/ResetPasswordFailedState.mjs';
import { ResetPasswordPasswordRequiredState } from '../state/ResetPasswordPasswordRequiredState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a reset password operation that requires a code.
 */
class ResetPasswordSubmitCodeResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of ResetPasswordSubmitCodeResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of ResetPasswordSubmitCodeResult with an error.
     * @param error The error that occurred.
     * @returns {ResetPasswordSubmitCodeResult} A new instance of ResetPasswordSubmitCodeResult with the error set.
     */
    static createWithError(error) {
        const result = new ResetPasswordSubmitCodeResult(new ResetPasswordFailedState());
        result.error = new ResetPasswordSubmitCodeError(ResetPasswordSubmitCodeResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof ResetPasswordFailedState;
    }
    /**
     * Checks if the result is in a password required state.
     */
    isPasswordRequired() {
        return this.state instanceof ResetPasswordPasswordRequiredState;
    }
}

export { ResetPasswordSubmitCodeResult };
//# sourceMappingURL=ResetPasswordSubmitCodeResult.mjs.map
