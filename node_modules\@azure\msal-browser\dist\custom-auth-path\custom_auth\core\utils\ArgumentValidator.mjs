/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { InvalidArgumentError } from '../error/InvalidArgumentError.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
function ensureArgumentIsNotNullOrUndefined(argName, argValue, correlationId) {
    if (argValue === null || argValue === undefined) {
        throw new InvalidArgumentError(argName, correlationId);
    }
}
function ensureArgumentIsNotEmptyString(argName, argValue, correlationId) {
    if (!argValue || argValue.trim() === "") {
        throw new InvalidArgumentError(argName, correlationId);
    }
}

export { ensureArgumentIsNotEmptyString, ensureArgumentIsNotNullOrUndefined };
//# sourceMappingURL=ArgumentValidator.mjs.map
