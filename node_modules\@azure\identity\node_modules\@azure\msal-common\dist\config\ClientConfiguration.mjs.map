{"version": 3, "file": "ClientConfiguration.mjs", "sources": ["../../src/config/ClientConfiguration.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.methodNotImplemented"], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AA4KU,MAAA,sBAAsB,GAA4B;AAC3D,IAAA,yBAAyB,EAAE,gCAAgC;AAC3D,IAAA,oBAAoB,EAAE,KAAK;EAC7B;AAEF,MAAM,6BAA6B,GAA4B;IAC3D,cAAc,EAAE,MAAK;;KAEpB;AACD,IAAA,iBAAiB,EAAE,KAAK;IACxB,QAAQ,EAAE,QAAQ,CAAC,IAAI;IACvB,aAAa,EAAE,SAAS,CAAC,YAAY;CACxC,CAAC;AAEF,MAAM,qBAAqB,GAA2B;AAClD,IAAA,yBAAyB,EAAE,KAAK;CACnC,CAAC;AAEF,MAAM,8BAA8B,GAAmB;AACnD,IAAA,MAAM,mBAAmB,GAAA;AACrB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;AACD,IAAA,MAAM,oBAAoB,GAAA;AACtB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;CACJ,CAAC;AAEF,MAAM,oBAAoB,GAAgB;IACtC,GAAG,EAAE,SAAS,CAAC,GAAG;AAClB,IAAA,OAAO,EAAE,OAAO;IAChB,GAAG,EAAE,SAAS,CAAC,YAAY;IAC3B,EAAE,EAAE,SAAS,CAAC,YAAY;CAC7B,CAAC;AAEF,MAAM,0BAA0B,GAAsB;IAClD,YAAY,EAAE,SAAS,CAAC,YAAY;AACpC,IAAA,eAAe,EAAE,SAAS;CAC7B,CAAC;AAEF,MAAM,2BAA2B,GAAsB;IACnD,kBAAkB,EAAE,kBAAkB,CAAC,IAAI;AAC3C,IAAA,MAAM,EAAE,CAAA,EAAG,SAAS,CAAC,qBAAqB,CAAE,CAAA;CAC/C,CAAC;AAEF,MAAM,yBAAyB,GAA+B;AAC1D,IAAA,WAAW,EAAE;AACT,QAAA,OAAO,EAAE,EAAE;AACX,QAAA,UAAU,EAAE,EAAE;AACjB,KAAA;CACJ,CAAC;AAEF;;;;;;AAMG;AACG,SAAU,wBAAwB,CAAC,EACrC,WAAW,EAAE,eAAe,EAC5B,aAAa,EAAE,iBAAiB,EAChC,aAAa,EAAE,gBAAgB,EAC/B,YAAY,EAAE,gBAAgB,EAC9B,gBAAgB,EAAE,qBAAqB,EACvC,gBAAgB,EAAE,qBAAqB,EACvC,eAAe,EAAE,oBAAoB,EACrC,iBAAiB,EAAE,iBAAiB,EACpC,WAAW,EAAE,WAAW,EACxB,SAAS,EAAE,SAAS,EACpB,sBAAsB,EAAE,sBAAsB,EAC9C,iBAAiB,EAAE,iBAAiB,EACpC,iBAAiB,EAAE,iBAAiB,GAClB,EAAA;AAClB,IAAA,MAAM,aAAa,GAAG;AAClB,QAAA,GAAG,6BAA6B;AAChC,QAAA,GAAG,gBAAgB;KACtB,CAAC;IAEF,OAAO;AACH,QAAA,WAAW,EAAE,gBAAgB,CAAC,eAAe,CAAC;AAC9C,QAAA,aAAa,EAAE,EAAE,GAAG,sBAAsB,EAAE,GAAG,iBAAiB,EAAE;AAClE,QAAA,aAAa,EAAE,aAAa;AAC5B,QAAA,YAAY,EAAE,EAAE,GAAG,qBAAqB,EAAE,GAAG,gBAAgB,EAAE;AAC/D,QAAA,gBAAgB,EACZ,qBAAqB;AACrB,YAAA,IAAI,mBAAmB,CACnB,eAAe,CAAC,QAAQ,EACxB,6BAA6B,EAC7B,IAAI,MAAM,CAAC,aAAa,CAAC,EACzB,IAAI,qBAAqB,EAAE,CAC9B;QACL,gBAAgB,EACZ,qBAAqB,IAAI,8BAA8B;QAC3D,eAAe,EAAE,oBAAoB,IAAI,6BAA6B;QACtE,iBAAiB,EAAE,iBAAiB,IAAI,0BAA0B;AAClE,QAAA,WAAW,EAAE,EAAE,GAAG,oBAAoB,EAAE,GAAG,WAAW,EAAE;AACxD,QAAA,SAAS,EAAE,EAAE,GAAG,yBAAyB,EAAE,GAAG,SAAS,EAAE;QACzD,sBAAsB,EAAE,sBAAsB,IAAI,IAAI;QACtD,iBAAiB,EAAE,iBAAiB,IAAI,IAAI;QAC5C,iBAAiB,EAAE,iBAAiB,IAAI,IAAI;KAC/C,CAAC;AACN,CAAC;AAED;;;AAGG;AACH,SAAS,gBAAgB,CAAC,WAAwB,EAAA;IAC9C,OAAO;AACH,QAAA,kBAAkB,EAAE,EAAE;AACtB,QAAA,iBAAiB,EAAE,2BAA2B;AAC9C,QAAA,0BAA0B,EAAE,KAAK;AACjC,QAAA,aAAa,EAAE,KAAK;AACpB,QAAA,sBAAsB,EAAE,KAAK;AAC7B,QAAA,GAAG,WAAW;KACjB,CAAC;AACN,CAAC;AAED;;;AAGG;AACG,SAAU,kBAAkB,CAAC,MAA2B,EAAA;AAC1D,IAAA,QACI,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EACzE;AACN;;;;"}