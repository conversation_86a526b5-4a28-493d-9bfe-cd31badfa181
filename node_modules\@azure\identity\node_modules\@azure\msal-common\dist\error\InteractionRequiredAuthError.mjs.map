{"version": 3, "file": "InteractionRequiredAuthError.mjs", "sources": ["../../src/error/InteractionRequiredAuthError.ts"], "sourcesContent": [null], "names": ["InteractionRequiredAuthErrorCodes.interactionRequired", "InteractionRequiredAuthErrorCodes.consentRequired", "InteractionRequiredAuthErrorCodes.loginRequired", "InteractionRequiredAuthErrorCodes.badToken", "InteractionRequiredAuthErrorCodes.uxNotAllowed", "InteractionRequiredAuthErrorCodes.noTokensFound", "InteractionRequiredAuthErrorCodes.nativeAccountUnavailable", "InteractionRequiredAuthErrorCodes.refreshTokenExpired"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAOH;;AAEG;AACU,MAAA,qCAAqC,GAAG;AACjD,IAAAA,mBAAqD;AACrD,IAAAC,eAAiD;AACjD,IAAAC,aAA+C;AAC/C,IAAAC,QAA0C;AAC1C,IAAAC,YAA8C;EAChD;AAEW,MAAA,sCAAsC,GAAG;IAClD,cAAc;IACd,mBAAmB;IACnB,cAAc;IACd,uBAAuB;IACvB,kBAAkB;IAClB,WAAW;EACb;AAEF,MAAM,oCAAoC,GAAG;AACzC,IAAA,CAACC,aAA+C,GAC5C,sDAAsD;AAC1D,IAAA,CAACC,wBAA0D,GACvD,qJAAqJ;AACzJ,IAAA,CAACC,mBAAqD,GAClD,4BAA4B;AAChC,IAAA,CAACJ,QAA0C,GACvC,+HAA+H;AACnI,IAAA,CAACC,YAA8C,GAC3C,gIAAgI;CACvI,CAAC;AAEF;;;AAGG;AACU,MAAA,mCAAmC,GAAG;AAC/C,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,aAA+C;AACrD,QAAA,IAAI,EAAE,oCAAoC,CACtCA,aAA+C,CAClD;AACJ,KAAA;AACD,IAAA,0BAA0B,EAAE;QACxB,IAAI,EAAEC,wBAA0D;AAChE,QAAA,IAAI,EAAE,oCAAoC,CACtCA,wBAA0D,CAC7D;AACJ,KAAA;AACD,IAAA,SAAS,EAAE;QACP,IAAI,EAAEH,QAA0C;AAChD,QAAA,IAAI,EAAE,oCAAoC,CACtCA,QAA0C,CAC7C;AACJ,KAAA;EACH;AAEF;;AAEG;AACG,MAAO,4BAA6B,SAAQ,SAAS,CAAA;AA2BvD,IAAA,WAAA,CACI,SAAkB,EAClB,YAAqB,EACrB,QAAiB,EACjB,SAAkB,EAClB,OAAgB,EAChB,aAAsB,EACtB,MAAe,EACf,OAAgB,EAAA;AAEhB,QAAA,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;QACzC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,4BAA4B,CAAC,SAAS,CAAC,CAAC;QAEpE,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC,YAAY,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,SAAS,CAAC,YAAY,CAAC;QAC7D,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,SAAS,CAAC,YAAY,CAAC;AAC/C,QAAA,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;AAC3C,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;AACJ,CAAA;AAED;;;;;AAKG;SACa,0BAA0B,CACtC,SAAkB,EAClB,WAAoB,EACpB,QAAiB,EAAA;AAEjB,IAAA,MAAM,8BAA8B,GAChC,CAAC,CAAC,SAAS;QACX,qCAAqC,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AAClE,IAAA,MAAM,6BAA6B,GAC/B,CAAC,CAAC,QAAQ;QACV,sCAAsC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAClE,IAAA,MAAM,8BAA8B,GAChC,CAAC,CAAC,WAAW;AACb,QAAA,qCAAqC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAI;YACvD,OAAO,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AACjD,SAAC,CAAC,CAAC;AAEP,IAAA,QACI,8BAA8B;QAC9B,8BAA8B;AAC9B,QAAA,6BAA6B,EAC/B;AACN,CAAC;AAED;;AAEG;AACG,SAAU,kCAAkC,CAC9C,SAAiB,EAAA;IAEjB,OAAO,IAAI,4BAA4B,CACnC,SAAS,EACT,oCAAoC,CAAC,SAAS,CAAC,CAClD,CAAC;AACN;;;;"}