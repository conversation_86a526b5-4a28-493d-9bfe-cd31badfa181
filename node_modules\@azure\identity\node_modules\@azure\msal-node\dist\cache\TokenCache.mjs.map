{"version": 3, "file": "TokenCache.mjs", "sources": ["../../src/cache/TokenCache.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAGG;AA2BH,MAAM,sBAAsB,GAAc;AACtC,IAAA,OAAO,EAAE,EAAE;AACX,IAAA,OAAO,EAAE,EAAE;AACX,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,WAAW,EAAE,EAAE;CAClB,CAAC;AAEF;;;AAGG;MACU,UAAU,CAAA;AAOnB,IAAA,WAAA,CACI,OAAoB,EACpB,MAAc,EACd,WAA0B,EAAA;AAE1B,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtE,QAAA,IAAI,WAAW,EAAE;AACb,YAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;AAEG;IACH,UAAU,GAAA;QACN,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AAED;;AAEG;IACH,SAAS,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;AACjD,QAAA,IAAI,UAAU,GAAG,UAAU,CAAC,iBAAiB,CACzC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAmB,CACnD,CAAC;;QAGF,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACtD,YAAA,UAAU,GAAG,IAAI,CAAC,UAAU,CACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAC9B,UAAU,CACb,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;AACnD,SAAA;AACD,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAE7B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,KAAa,EAAA;AACrB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,MAAM,iBAAiB,GAAG,YAAY,CAAC,mBAAmB,CACtD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CACvD,CAAC;AACF,YAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AACpD,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACzD,SAAA;KACJ;AAED;;AAEG;IACH,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;KAClC;AAED;;AAEG;IACH,gBAAgB,GAAA;QACZ,MAAM,6BAA6B,GAAG,WAAW,CAAC,qBAAqB,CACnE,IAAI,CAAC,aAAa,CACrB,CAAC;QACF,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;KAC3E;AAED;;AAEG;IACH,MAAM,cAAc,CAChB,aAAA,GAAwB,IAAI,cAAc,EAAE,CAAC,aAAa,EAAE,EAAA;AAE5D,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC3C,QAAA,IAAI,YAAY,CAAC;QACjB,IAAI;YACA,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,YAAY,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAClD,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAC1D,aAAA;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;AACzD,SAAA;AAAS,gBAAA;AACN,YAAA,IAAI,IAAI,CAAC,WAAW,IAAI,YAAY,EAAE;gBAClC,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACzD,aAAA;AACJ,SAAA;KACJ;AAED;;;;;AAKG;IACH,MAAM,kBAAkB,CACpB,aAAqB,EAAA;AAErB,QAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAChD,QAAA,IAAI,aAAa,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,EAAE;YACpD,QACI,WAAW,CAAC,MAAM,CACd,CAAC,UAAU,KAAK,UAAU,CAAC,aAAa,KAAK,aAAa,CAC7D,CAAC,CAAC,CAAC,IAAI,IAAI,EACd;AACL,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED;;;;;AAKG;IACH,MAAM,mBAAmB,CACrB,cAAsB,EAAA;AAEtB,QAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAChD,QAAA,IAAI,cAAc,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,EAAE;YACrD,QACI,WAAW,CAAC,MAAM,CACd,CAAC,UAAU,KAAK,UAAU,CAAC,cAAc,KAAK,cAAc,CAC/D,CAAC,CAAC,CAAC,IAAI,IAAI,EACd;AACL,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,MAAM,aAAa,CACf,OAAoB,EACpB,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAC1C,QAAA,IAAI,YAAY,CAAC;QACjB,IAAI;YACA,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,YAAY,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACjD,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAC1D,aAAA;YACD,IAAI,CAAC,OAAO,CAAC,aAAa,CACtB,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAC9C,aAAa,IAAI,IAAI,aAAa,EAAE,CAAC,YAAY,EAAE,CACtD,CAAC;AACL,SAAA;AAAS,gBAAA;AACN,YAAA,IAAI,IAAI,CAAC,WAAW,IAAI,YAAY,EAAE;gBAClC,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACzD,aAAA;AACJ,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,MAAM,cAAc,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACnB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,6DAA6D,CAChE,CAAC;YACF,OAAO;AACV,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,YAAY,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AACvD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC9C,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;KACzD;AAED;;AAEG;IACK,iBAAiB,GAAA;AACrB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;KAC/B;AAED;;;;AAIG;IACK,UAAU,CACd,QAAmB,EACnB,YAAuB,EAAA;AAEvB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;KAC7D;AAED;;;;AAIG;IACK,YAAY,CAAC,QAAgB,EAAE,QAAgB,EAAA;QACnD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAc,KAAI;AAC7C,YAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;;AAGlC,YAAA,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;gBAClC,IAAI,QAAQ,KAAK,IAAI,EAAE;AACnB,oBAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AAC/B,iBAAA;AACJ,aAAA;AAAM,iBAAA;;AAEH,gBAAA,MAAM,eAAe,GAAG,QAAQ,KAAK,IAAI,CAAC;AAC1C,gBAAA,MAAM,gBAAgB,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC;gBACtD,MAAM,kBAAkB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM,0BAA0B,GAC5B,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,WAAW;AACvC,oBAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;AAE9B,gBAAA,IACI,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;AAClB,oBAAA,0BAA0B,EAC5B;oBACE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;AACjD,iBAAA;AAAM,qBAAA;AACH,oBAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AAC/B,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,QAAqB,CAAC;KAChC;AAED;;;;;AAKG;IACK,aAAa,CAAC,QAAmB,EAAE,QAAmB,EAAA;AAC1D,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACrD,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO;AAC7B,cAAE,IAAI,CAAC,iBAAiB,CAClB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,OAAO,CACnB;AACH,cAAE,QAAQ,CAAC,OAAO,CAAC;AACvB,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW;AACrC,cAAE,IAAI,CAAC,iBAAiB,CAClB,QAAQ,CAAC,WAAW,EACpB,QAAQ,CAAC,WAAW,CACvB;AACH,cAAE,QAAQ,CAAC,WAAW,CAAC;AAC3B,QAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY;AACvC,cAAE,IAAI,CAAC,iBAAiB,CAClB,QAAQ,CAAC,YAAY,EACrB,QAAQ,CAAC,YAAY,CACxB;AACH,cAAE,QAAQ,CAAC,YAAY,CAAC;AAC5B,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO;AAC7B,cAAE,IAAI,CAAC,iBAAiB,CAClB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,OAAO,CACnB;AACH,cAAE,QAAQ,CAAC,OAAO,CAAC;AACvB,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW;AACpC,cAAE,IAAI,CAAC,iBAAiB,CAClB,QAAQ,CAAC,WAAW,EACpB,QAAQ,CAAC,WAAW,CACvB;AACH,cAAE,QAAQ,CAAC,WAAW,CAAC;QAE3B,OAAO;AACH,YAAA,GAAG,QAAQ;AACX,YAAA,OAAO,EAAE,QAAQ;AACjB,YAAA,WAAW,EAAE,YAAY;AACzB,YAAA,YAAY,EAAE,aAAa;AAC3B,YAAA,OAAO,EAAE,QAAQ;AACjB,YAAA,WAAW,EAAE,WAAW;SAC3B,CAAC;KACL;AAED;;;;AAIG;IACK,iBAAiB,CACrB,QAA2B,EAC3B,QAA4B,EAAA;AAE5B,QAAA,MAAM,UAAU,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;YACrC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AAC/C,gBAAA,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;AAC7B,aAAA;AACL,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,UAAU,CAAC;KACrB;AAED;;;AAGG;AACK,IAAA,eAAe,CAAC,aAAwB,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,OAAO;AACH,YAAA,OAAO,EAAE;gBACL,GAAG,sBAAsB,CAAC,OAAO;gBACjC,GAAG,aAAa,CAAC,OAAO;AAC3B,aAAA;AACD,YAAA,OAAO,EAAE;gBACL,GAAG,sBAAsB,CAAC,OAAO;gBACjC,GAAG,aAAa,CAAC,OAAO;AAC3B,aAAA;AACD,YAAA,WAAW,EAAE;gBACT,GAAG,sBAAsB,CAAC,WAAW;gBACrC,GAAG,aAAa,CAAC,WAAW;AAC/B,aAAA;AACD,YAAA,YAAY,EAAE;gBACV,GAAG,sBAAsB,CAAC,YAAY;gBACtC,GAAG,aAAa,CAAC,YAAY;AAChC,aAAA;AACD,YAAA,WAAW,EAAE;gBACT,GAAG,sBAAsB,CAAC,WAAW;gBACrC,GAAG,aAAa,CAAC,WAAW;AAC/B,aAAA;SACJ,CAAC;KACL;AACJ;;;;"}