{"version": 3, "file": "PerformanceClient.d.ts", "sourceRoot": "", "sources": ["../../../../src/telemetry/performance/PerformanceClient.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EACH,0BAA0B,EAC1B,kBAAkB,EAClB,2BAA2B,EAC3B,gBAAgB,EACnB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAEH,gBAAgB,EAEhB,uBAAuB,EACvB,iBAAiB,EACjB,8BAA8B,EAEjC,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AAOvE,MAAM,WAAW,aAAa;IAC1B,IAAI,EAAE,iBAAiB,CAAC;IACxB,IAAI,EAAE,MAAM,CAAC;CAChB;AAED;;;;;GAKG;AACH,wBAAgB,YAAY,CACxB,KAAK,EAAE,gBAAgB,EACvB,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAClC,KAAK,CAAC,EAAE,8BAA8B,EAAE,GACzC,IAAI,CAQN;AAED;;;;;;;GAOG;AACH,wBAAgB,UAAU,CACtB,KAAK,EAAE,gBAAgB,EACvB,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAClC,KAAK,CAAC,EAAE,8BAA8B,EAAE,EACxC,KAAK,CAAC,EAAE,OAAO,GAChB,uBAAuB,GAAG,SAAS,CAmErC;AAED;;;;;;GAMG;AACH,wBAAgB,QAAQ,CACpB,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,gBAAgB,EACvB,YAAY,GAAE,MAAU,GACzB,IAAI,CAsCN;AAED;;;;;GAKG;AACH,wBAAgB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,EAAE,CA0C1E;AAED;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAoBrD;AAED,8BAAsB,iBAAkB,YAAW,kBAAkB;IACjE,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;IAC9B,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC;IACjC,SAAS,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IACrD,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC;IAE9D;;;;OAIG;IACH,SAAS,CAAC,qBAAqB,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAE/D;;;;;OAKG;IACH,SAAS,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAElE;;;;;OAKG;IACH,SAAS,CAAC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAElE,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAEjC;;;;OAIG;IACH,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,8BAA8B,EAAE,CAAC,CAAC;IAEpE;;;;OAIG;IACH,SAAS,CAAC,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAE7C;;;;;;;;;;;;;OAaG;gBAEC,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,MAAM,EACnB,cAAc,EAAE,MAAM,EACtB,oBAAoB,EAAE,oBAAoB,EAC1C,SAAS,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EACvB,aAAa,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAuBvC;;;;;OAKG;IACH,QAAQ,CAAC,UAAU,IAAI,MAAM;IAE7B;;;;;;;;OAQG;IACH,2BAA2B,CACvB,WAAW,EAAE,MAAM,EAAE,wDAAwD;IAC7E,aAAa,EAAE,MAAM,GACtB,uBAAuB;IAI1B;;;;;;;OAOG;IACH,QAAQ,CAAC,eAAe,CACpB,SAAS,EAAE,iBAAiB,EAC5B,aAAa,CAAC,EAAE,MAAM,GACvB,IAAI;IAEP;;;;;;OAMG;IACH,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAmBxE;;;;;;;OAOG;IACH,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,MAAM;IAyBtE;;;;;;;;OAQG;IACH,mBAAmB,CACf,SAAS,EAAE,MAAM,EACjB,aAAa,CAAC,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,MAAM,EAClB,iBAAiB,CAAC,EAAE,OAAO,GAC5B,IAAI;IA4CP;;;;;;OAMG;IACH,gBAAgB,CACZ,WAAW,EAAE,MAAM,EACnB,aAAa,CAAC,EAAE,MAAM,GACvB,0BAA0B;IAsE7B;;;;;;;;;OASG;IACH,cAAc,CACV,KAAK,EAAE,gBAAgB,EACvB,KAAK,CAAC,EAAE,OAAO,GAChB,gBAAgB,GAAG,IAAI;IA6F1B;;;;OAIG;IACH,SAAS,CACL,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG,SAAS,CAAA;KAAE,EACzC,aAAa,EAAE,MAAM,GACtB,IAAI;IAgBP;;;;OAIG;IACH,eAAe,CACX,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,EAC7C,aAAa,EAAE,MAAM,GACtB,IAAI;IAoBP;;;;;;;;OAQG;IACH,SAAS,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAuBlE,OAAO,CAAC,YAAY;IA6BpB;;;;OAIG;IACH,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI;IA0BhD;;;;;OAKG;IACH,sBAAsB,CAAC,QAAQ,EAAE,2BAA2B,GAAG,MAAM;IAmBrE;;;;;OAKG;IACH,yBAAyB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO;IAgBtD;;;;;OAKG;IACH,UAAU,CAAC,MAAM,EAAE,gBAAgB,EAAE,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAiBnE;;;OAGG;IACH,OAAO,CAAC,sBAAsB;IAQ9B;;;;OAIG;IACH,OAAO,CAAC,aAAa;CAKxB"}