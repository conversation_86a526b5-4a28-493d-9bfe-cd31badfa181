{"version": 3, "file": "AuthToken.mjs", "sources": ["../../src/account/AuthToken.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.tokenParsingError", "ClientAuthErrorCodes.nullOrEmptyToken", "ClientAuthErrorCodes.maxAgeTranspired"], "mappings": ";;;;;AAAA;;;AAGG;AAQH;;;;AAIG;AACa,SAAA,kBAAkB,CAC9B,YAAoB,EACpB,YAAuC,EAAA;AAEvC,IAAA,MAAM,UAAU,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;;IAG/C,IAAI;;AAEA,QAAA,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AAC/C,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAgB,CAAC;AACnD,KAAA;AAAC,IAAA,OAAO,GAAG,EAAE;AACV,QAAA,MAAM,qBAAqB,CAACA,iBAAsC,CAAC,CAAC;AACvE,KAAA;AACL,CAAC;AAED;;;;AAIG;AACG,SAAU,aAAa,CAAC,SAAiB,EAAA;IAC3C,IAAI,CAAC,SAAS,EAAE;AACZ,QAAA,MAAM,qBAAqB,CAACC,gBAAqC,CAAC,CAAC;AACtE,KAAA;IACD,MAAM,eAAe,GAAG,sCAAsC,CAAC;IAC/D,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAChD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,QAAA,MAAM,qBAAqB,CAACD,iBAAsC,CAAC,CAAC;AACvE,KAAA;AACD;;;;;;AAMG;AAEH,IAAA,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AAED;;AAEG;AACa,SAAA,WAAW,CAAC,QAAgB,EAAE,MAAc,EAAA;AACxD;;;;AAIG;AACH,IAAA,MAAM,cAAc,GAAG,MAAM,CAAC;AAC9B,IAAA,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,GAAG,QAAQ,GAAG,MAAM,EAAE;AACjE,QAAA,MAAM,qBAAqB,CAACE,gBAAqC,CAAC,CAAC;AACtE,KAAA;AACL;;;;"}