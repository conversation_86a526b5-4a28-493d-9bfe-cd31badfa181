{"version": 3, "file": "UrlUtils.mjs", "sources": ["../../src/utils/UrlUtils.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.hashNotDeserialized"], "mappings": ";;;;;AAAA;;;AAGG;AASH;;;AAGG;AACG,SAAU,uBAAuB,CAAC,cAAsB,EAAA;AAC1D,IAAA,IAAI,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACjC,QAAA,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtC,KAAA;AAAM,SAAA,IACH,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC;AAC9B,QAAA,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAChC;AACE,QAAA,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtC,KAAA;AAED,IAAA,OAAO,cAAc,CAAC;AAC1B,CAAC;AAED;;AAEG;AACG,SAAU,uBAAuB,CACnC,cAAsB,EAAA;;IAGtB,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACpD,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;IACD,IAAI;;AAEA,QAAA,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;;AAEnE,QAAA,MAAM,gBAAgB,GAAsB,MAAM,CAAC,WAAW,CAC1D,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAC1C,CAAC;;QAGF,IACI,gBAAgB,CAAC,IAAI;AACrB,YAAA,gBAAgB,CAAC,OAAO;AACxB,YAAA,gBAAgB,CAAC,KAAK;AACtB,YAAA,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,KAAK,EACxB;AACE,YAAA,OAAO,gBAAgB,CAAC;AAC3B,SAAA;AACJ,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,qBAAqB,CAACA,mBAAwC,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;AAEG;AACG,SAAU,gBAAgB,CAC5B,UAA+B,EAC/B,iBAA6B,GAAA,IAAI,EACjC,oBAAiC,EAAA;AAEjC,IAAA,MAAM,mBAAmB,GAAkB,IAAI,KAAK,EAAU,CAAC;IAE/D,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;AAC9B,QAAA,IACI,CAAC,iBAAiB;YAClB,oBAAoB;YACpB,GAAG,IAAI,oBAAoB,EAC7B;YACE,mBAAmB,CAAC,IAAI,CAAC,CAAA,EAAG,GAAG,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AAC/C,SAAA;AAAM,aAAA;AACH,YAAA,mBAAmB,CAAC,IAAI,CAAC,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,kBAAkB,CAAC,KAAK,CAAC,CAAE,CAAA,CAAC,CAAC;AACnE,SAAA;AACL,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzC;;;;"}