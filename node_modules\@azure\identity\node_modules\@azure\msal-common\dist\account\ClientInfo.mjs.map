{"version": 3, "file": "ClientInfo.mjs", "sources": ["../../src/account/ClientInfo.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.clientInfoEmptyError", "ClientAuthErrorCodes.clientInfoDecodingError"], "mappings": ";;;;;;AAAA;;;AAGG;AAgBH;;;;AAIG;AACa,SAAA,eAAe,CAC3B,aAAqB,EACrB,YAAuC,EAAA;IAEvC,IAAI,CAAC,aAAa,EAAE;AAChB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;AAC1E,KAAA;IAED,IAAI;AACA,QAAA,MAAM,iBAAiB,GAAW,YAAY,CAAC,aAAa,CAAC,CAAC;AAC9D,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAe,CAAC;AACtD,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,qBAAqB,CACvBC,uBAA4C,CAC/C,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;AAGG;AACG,SAAU,gCAAgC,CAC5C,aAAqB,EAAA;IAErB,IAAI,CAAC,aAAa,EAAE;AAChB,QAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,KAAA;AACD,IAAA,MAAM,eAAe,GAAa,aAAa,CAAC,KAAK,CACjD,UAAU,CAAC,qBAAqB,EAChC,CAAC,CACJ,CAAC;IACF,OAAO;AACH,QAAA,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,EACA,eAAe,CAAC,MAAM,GAAG,CAAC;cACpB,SAAS,CAAC,YAAY;AACxB,cAAE,eAAe,CAAC,CAAC,CAAC;KAC/B,CAAC;AACN;;;;"}