{"version": 3, "file": "ClientCredentialClient.mjs", "sources": ["../../src/client/ClientCredentialClient.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAwCH;;;AAGG;AACG,MAAO,sBAAuB,SAAQ,UAAU,CAAA;IAGlD,WACI,CAAA,aAAkC,EAClC,gBAAoC,EAAA;QAEpC,KAAK,CAAC,aAAa,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC5C;AAED;;;AAGG;IACI,MAAM,YAAY,CACrB,OAAsC,EAAA;AAEtC,QAAA,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE;YACrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,MAAM,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,GAChD,MAAM,IAAI,CAAC,6BAA6B,CACpC,OAAO,EACP,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AAEN,QAAA,IAAI,0BAA0B,EAAE;;AAE5B,YAAA,IAAI,gBAAgB,KAAK,YAAY,CAAC,qBAAqB,EAAE;AACzD,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,8JAA8J,CACjK,CAAC;;gBAGF,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,gBAAA,MAAM,IAAI,CAAC,mBAAmB,CAC1B,OAAO,EACP,IAAI,CAAC,SAAS,EACd,kBAAkB,CACrB,CAAC;AACL,aAAA;;AAGD,YAAA,OAAO,0BAA0B,CAAC;AACrC,SAAA;AAAM,aAAA;YACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5D,SAAA;KACJ;AAED;;AAEG;AACI,IAAA,MAAM,6BAA6B,CACtC,OAAsC,EACtC,MAA0D,EAC1D,WAAoB,EACpB,SAAoB,EACpB,YAA0B,EAC1B,sBAAsD,EAAA;QAEtD,MAAM,mBAAmB,GAAG,MAA6B,CAAC;QAC1D,MAAM,4BAA4B,GAC9B,MAA0C,CAAC;AAE/C,QAAA,IAAI,gBAAgB,GAAiB,YAAY,CAAC,cAAc,CAAC;;AAGjE,QAAA,IAAI,YAAY,CAAC;QACjB,IACI,mBAAmB,CAAC,iBAAiB;YACrC,mBAAmB,CAAC,iBAAiB,EACvC;YACE,YAAY,GAAG,IAAI,iBAAiB,CAChC,mBAAmB,CAAC,iBAAiB,EACrC,KAAK,CACR,CAAC;YACF,MAAM,mBAAmB,CAAC,iBAAiB,CAAC,iBAAiB,CACzD,YAAY,CACf,CAAC;AACL,SAAA;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CACnD,SAAS,EACT,4BAA4B,CAAC,iBAAiB,EAAE,EAAE;YAC9C,mBAAmB,CAAC,WAAW,CAAC,QAAQ,EAC5C,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,EAClC,YAAY,EACZ,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,IACI,mBAAmB,CAAC,iBAAiB;AACrC,YAAA,mBAAmB,CAAC,iBAAiB;AACrC,YAAA,YAAY,EACd;YACE,MAAM,mBAAmB,CAAC,iBAAiB,CAAC,gBAAgB,CACxD,YAAY,CACf,CAAC;AACL,SAAA;;QAGD,IAAI,CAAC,iBAAiB,EAAE;AACpB,YAAA,sBAAsB,EAAE,eAAe,CACnC,YAAY,CAAC,sBAAsB,CACtC,CAAC;AACF,YAAA,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;AACtD,SAAA;;AAGD,QAAA,IACI,SAAS,CAAC,cAAc,CACpB,iBAAiB,CAAC,SAAS,EAC3B,mBAAmB,CAAC,aAAa,EAAE,yBAAyB;AACxD,YAAA,gCAAgC,CACvC,EACH;AACE,YAAA,sBAAsB,EAAE,eAAe,CACnC,YAAY,CAAC,2BAA2B,CAC3C,CAAC;AACF,YAAA,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,2BAA2B,CAAC,CAAC;AAC3D,SAAA;;QAGD,IACI,iBAAiB,CAAC,SAAS;AAC3B,YAAA,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EACrE;AACE,YAAA,gBAAgB,GAAG,YAAY,CAAC,qBAAqB,CAAC;AACtD,YAAA,sBAAsB,EAAE,eAAe,CACnC,YAAY,CAAC,qBAAqB,CACrC,CAAC;AACL,SAAA;QAED,OAAO;AACH,YAAA,MAAM,eAAe,CAAC,4BAA4B,CAC9C,WAAW,EACX,SAAS,EACT;AACI,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,WAAW,EAAE,iBAAiB;AAC9B,gBAAA,YAAY,EAAE,IAAI;AAClB,gBAAA,WAAW,EAAE,IAAI;aACpB,EACD,IAAI,EACJ,OAAO,CACV;YACD,gBAAgB;SACnB,CAAC;KACL;AAED;;AAEG;IACK,wBAAwB,CAC5B,SAAoB,EACpB,EAAU,EACV,QAAkB,EAClB,YAA0B,EAC1B,aAAqB,EAAA;AAErB,QAAA,MAAM,iBAAiB,GAAqB;YACxC,aAAa,EAAE,SAAS,CAAC,YAAY;AACrC,YAAA,WAAW,EACP,SAAS,CAAC,+BAA+B,CAAC,eAAe;YAC7D,cAAc,EAAE,cAAc,CAAC,YAAY;AAC3C,YAAA,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,SAAS,CAAC,MAAM;YACvB,MAAM,EAAE,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;SAC1D,CAAC;QAEF,MAAM,YAAY,GAAG,YAAY,CAAC,uBAAuB,CACrD,iBAAiB,EACjB,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAM,aAAA,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,sBAAsB,CAC9C,CAAC;AACL,SAAA;AACD,QAAA,OAAO,YAAY,CAAC,CAAC,CAAsB,CAAC;KAC/C;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,OAAsC,EACtC,SAAoB,EACpB,kBAA4B,EAAA;AAE5B,QAAA,IAAI,mBAAqD,CAAC;AAC1D,QAAA,IAAI,YAAoB,CAAC;QAEzB,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;AAE1D,YAAA,MAAM,2BAA2B,GAAG;gBAChC,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM;gBAClD,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;aACzB,CAAC;AAEF,YAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACtD,2BAA2B,CAC9B,CAAC;AAEF,YAAA,mBAAmB,GAAG;gBAClB,YAAY,EAAE,sBAAsB,CAAC,WAAW;gBAChD,UAAU,EAAE,sBAAsB,CAAC,gBAAgB;gBACnD,UAAU,EAAE,sBAAsB,CAAC,gBAAgB;gBACnD,UAAU,EAAE,oBAAoB,CAAC,MAAM;aAC1C,CAAC;AACL,SAAA;AAAM,aAAA;YACH,MAAM,qBAAqB,GACvB,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AAC7C,YAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AAC/D,YAAA,MAAM,OAAO,GACT,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACrC,YAAA,MAAM,UAAU,GAAsB;AAClC,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;gBAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;gBAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;gBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;aACzB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,qCAAqC,GAAG,SAAS,CAAC,aAAa,CAClE,CAAC;AAEF,YAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACtC,YAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAClD,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,YAAA,mBAAmB,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,YAAA,mBAAmB,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAChD,SAAA;AAED,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AAEF,QAAA,eAAe,CAAC,qBAAqB,CACjC,mBAAmB,EACnB,kBAAkB,CACrB,CAAC;AAEF,QAAA,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,yBAAyB,CACjE,mBAAmB,EACnB,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,CACV,CAAC;AAEF,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAsC,EAAA;AAEtC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C,QAAA,uBAAuB,CAAC,WAAW,CAC/B,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACnC,CAAC;QAEF,uBAAuB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAErE,uBAAuB,CAAC,YAAY,CAChC,UAAU,EACV,SAAS,CAAC,wBAAwB,CACrC,CAAC;QAEF,uBAAuB,CAAC,cAAc,CAClC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAC1B,CAAC;AACF,QAAA,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AAEF,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AACL,SAAA;AAED,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAChD,QAAA,uBAAuB,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AAEpE,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;AAC5C,YAAA,uBAAuB,CAAC,eAAe,CACnC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,eAAe,GACjB,OAAO,CAAC,eAAe;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AAElD,QAAA,IAAI,eAAe,EAAE;YACjB,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;YACF,uBAAuB,CAAC,sBAAsB,CAC1C,UAAU,EACV,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,uBAAuB,CAAC,SAAS,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;KAChD;AACJ;;;;"}