{"version": 3, "file": "ApiResponseTypes.d.ts", "sourceRoot": "", "sources": ["../../../../../../../src/custom_auth/core/network_client/custom_auth_api/types/ApiResponseTypes.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,UAAU,kBAAmB,SAAQ,eAAe;IAChD,kBAAkB,CAAC,EAAE,MAAM,CAAC;CAC/B;AAED,UAAU,gBAAiB,SAAQ,kBAAkB;IACjD,cAAc,CAAC,EAAE,MAAM,CAAC;CAC3B;AAED,UAAU,iBAAkB,SAAQ,eAAe;IAC/C,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC,WAAW,CAAC,EAAE,MAAM,CAAC;CACxB;AAGD,MAAM,MAAM,sBAAsB,GAAG,gBAAgB,CAAC;AAEtD,MAAM,MAAM,uBAAuB,GAAG,iBAAiB,CAAC;AAExD,MAAM,WAAW,mBAAoB,SAAQ,eAAe;IACxD,UAAU,EAAE,QAAQ,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,CAAC;IACrB,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;CAC3B;AAGD,MAAM,MAAM,mBAAmB,GAAG,gBAAgB,CAAC;AAEnD,MAAM,WAAW,uBAAwB,SAAQ,iBAAiB;IAC9D,QAAQ,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,MAAM,sBAAsB,GAAG,gBAAgB,CAAC;AAGtD,MAAM,MAAM,0BAA0B,GAAG,gBAAgB,CAAC;AAE1D,MAAM,MAAM,8BAA8B,GAAG,iBAAiB,CAAC;AAE/D,MAAM,WAAW,6BAA8B,SAAQ,kBAAkB;IACrE,UAAU,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,2BAA4B,SAAQ,kBAAkB;IACnE,aAAa,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,WAAW,mCACb,SAAQ,kBAAkB;IAC1B,MAAM,EAAE,MAAM,CAAC;CAClB"}