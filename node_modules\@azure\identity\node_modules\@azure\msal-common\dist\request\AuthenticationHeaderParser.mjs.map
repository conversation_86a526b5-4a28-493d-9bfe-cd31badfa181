{"version": 3, "file": "AuthenticationHeaderParser.mjs", "sources": ["../../src/request/AuthenticationHeaderParser.ts"], "sourcesContent": [null], "names": ["ClientConfigurationErrorCodes.invalidAuthenticationHeader", "ClientConfigurationErrorCodes.missingNonceAuthenticationHeader"], "mappings": ";;;;;;AAAA;;;AAGG;AAgBH;;;AAGG;MACU,0BAA0B,CAAA;AAGnC,IAAA,WAAA,CAAY,OAA+B,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;AAED;;;AAGG;IACH,WAAW,GAAA;;QAEP,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AACxE,QAAA,IAAI,kBAAkB,EAAE;YACpB,MAAM,4BAA4B,GAC9B,IAAI,CAAC,eAAe,CAChB,kBAAkB,CACrB,CAAC;YACN,IAAI,4BAA4B,CAAC,SAAS,EAAE;gBACxC,OAAO,4BAA4B,CAAC,SAAS,CAAC;AACjD,aAAA;AACD,YAAA,MAAM,8BAA8B,CAChCA,2BAAyD,CAC5D,CAAC;AACL,SAAA;;QAGD,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAClE,QAAA,IAAI,eAAe,EAAE;YACjB,MAAM,yBAAyB,GAC3B,IAAI,CAAC,eAAe,CAChB,eAAe,CAClB,CAAC;YACN,IAAI,yBAAyB,CAAC,KAAK,EAAE;gBACjC,OAAO,yBAAyB,CAAC,KAAK,CAAC;AAC1C,aAAA;AACD,YAAA,MAAM,8BAA8B,CAChCA,2BAAyD,CAC5D,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,8BAA8B,CAChCC,gCAA8D,CACjE,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,eAAe,CAAI,MAAc,EAAA;QACrC,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5C,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,EAAO,CAAC;AAE7B,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,SAAiB,KAAI;AACrC,YAAA,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;AAE1C,YAAA,YAAY,CAAC,GAAG,CAAC,GAAG,QAAQ,CACxB,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,YAAY,CAAC,CAClD,CAAC;AACN,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,YAAY,CAAC;KACvB;AACJ;;;;"}