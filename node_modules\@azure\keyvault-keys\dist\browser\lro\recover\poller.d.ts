import type { RecoverDeletedKeyPollOperationState } from "./operation.js";
import type { KeyVaultKey } from "../../keysModels.js";
import type { KeyVaultKeyPollerOptions } from "../keyVaultKeyPoller.js";
import { KeyVaultKeyPoller } from "../keyVaultKeyPoller.js";
/**
 * Class that deletes a poller that waits until a key finishes being deleted
 */
export declare class RecoverDeletedKeyPoller extends KeyVaultKeyPoller<RecoverDeletedKeyPollOperationState, KeyVaultKey> {
    constructor(options: KeyVaultKeyPollerOptions);
}
//# sourceMappingURL=poller.d.ts.map