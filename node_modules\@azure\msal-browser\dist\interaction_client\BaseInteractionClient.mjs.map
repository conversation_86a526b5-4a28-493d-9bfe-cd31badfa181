{"version": 3, "file": "BaseInteractionClient.mjs", "sources": ["../../src/interaction_client/BaseInteractionClient.ts"], "sourcesContent": [null], "names": ["BrowserUtils.getCurrentUri"], "mappings": ";;;;;;;;AAAA;;;AAGG;MAsCmB,qBAAqB,CAAA;AAYvC,IAAA,WAAA,CACI,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,iBAAqC,EACrC,oBAA2C,EAC3C,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AACtD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;AACjD,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,aAAa,EAAE,CAAC;AACtD,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CACtB,gBAAgB,CAAC,QAAQ,EACzB,OAAO,EACP,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAUS,IAAA,MAAM,kBAAkB,CAC9B,aAAqB,EACrB,OAA4B,EAAA;AAE5B,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IACI,aAAa,CAAC,kBAAkB,CAC5B,OAAO,EACP,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,aAAa,CAAC,EACnD,KAAK,CACR,EACH;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBACtD,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC7D,aAAA;;YAED,IAAI;AACA,gBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAC7B,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAC9C,aAAa,CAChB,CAAC;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8EAA8E,CACjF,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,KAAK,EAAE;AACZ,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,0EAA0E,CAC7E,CAAC;AACL,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kEAAkE,EAClE,IAAI,CAAC,aAAa,CACrB,CAAC;;AAEF,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;;AAEzC,gBAAA,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AAC5C,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,4EAA4E,CAC/E,CAAC;AACL,aAAA;AACJ,SAAA;KACJ;AAED;;;;;;AAMG;AACH,IAAA,cAAc,CAAC,kBAA2B,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;QACvE,OAAO,SAAS,CAAC,cAAc,CAC3B,WAAW,EACXA,aAA0B,EAAE,CAC/B,CAAC;KACL;AAED;;;;;AAKG;IACO,gCAAgC,CACtC,KAAa,EACb,YAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;AAC/D,QAAA,MAAM,gBAAgB,GAA2B;AAC7C,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACnC,aAAa,EAAE,IAAI,CAAC,aAAa;AACjC,YAAA,KAAK,EAAE,KAAK;YACZ,YAAY,EAAE,YAAY,IAAI,KAAK;YACnC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACvD,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;SAC1D,CAAC;QAEF,OAAO,IAAI,sBAAsB,CAC7B,gBAAgB,EAChB,IAAI,CAAC,cAAc,CACtB,CAAC;KACL;AAED;;;;;;;;AAQG;IACO,MAAM,sBAAsB,CAAC,MAKtC,EAAA;AACG,QAAA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;AAC3B,QAAA,MAAM,eAAe,GACjB,MAAM,CAAC,2BAA2B;AAClC,YAAA,MAAM,CAAC,2BAA2B,CAAC,cAAc,CAAC,gBAAgB,CAAC;AAC/D,cAAE,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,CAAC;cACpD,SAAS,CAAC;AAEpB,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,QAAA,MAAM,gBAAgB,GAAqB;AACvC,YAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;AAC3C,YAAA,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;AACzC,YAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;AACnD,YAAA,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAC/D,YAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;AACrD,YAAA,0BAA0B,EACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B;SAClD,CAAC;;AAGF,QAAA,MAAM,iBAAiB,GACnB,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1D,QAAA,MAAM,qBAAqB,GAAG,eAAe,EAAE,MAAM;cAC/C,eAAe,KAAK,MAAM;cAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;AAErC,QAAA,MAAM,aAAa,GACf,OAAO,IAAI,qBAAqB;cAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAC9B,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAC7C,OAAO,CAAC,WAAW,CACtB;cACD,iBAAiB,CAAC;;QAG5B,MAAM,cAAc,GAAG,SAAS,CAAC,iBAAiB,CAC9C,aAAa,EACb,MAAM,CAAC,wBAAwB;AAC3B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CACzC,CAAC;QACF,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,gBAAgB,CAAC,wBAAwB,EACzC,iBAAiB,CAAC,wCAAwC,EAC1D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CACG,cAAc,EACd,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAChC,IAAI,CAAC,cAAc,EACnB,gBAAgB,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAC9D,YAAA,MAAM,8BAA8B,CAChC,6BAA6B,CAAC,iBAAiB,CAClD,CAAC;AACL,SAAA;AAED,QAAA,OAAO,mBAAmB,CAAC;KAC9B;AACJ;;;;"}