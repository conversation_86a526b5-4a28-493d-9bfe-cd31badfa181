{"version": 3, "file": "CustomAuthAccountData.mjs", "sources": ["../../../../../../src/custom_auth/get_account/auth_flow/CustomAuthAccountData.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAsBH;;AAEG;MACU,qBAAqB,CAAA;IAC9B,WACqB,CAAA,OAAoB,EACpB,MAAsC,EACtC,WAAwC,EACxC,MAAc,EACd,aAAqB,EAAA;QAJrB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAa;QACpB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAgC;QACtC,IAAW,CAAA,WAAA,GAAX,WAAW,CAA6B;QACxC,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACd,IAAa,CAAA,aAAA,GAAb,aAAa,CAAQ;AAEtC,QAAA,8BAA8B,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;AAC/D,QAAA,kCAAkC,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;KACzE;AAED;;;;;AAKG;AACH,IAAA,MAAM,OAAO,GAAA;QACT,IAAI;AACA,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACrD,IAAI,CAAC,aAAa,CACrB,CAAC;YAEF,IAAI,CAAC,cAAc,EAAE;AACjB,gBAAA,MAAM,IAAI,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3D,aAAA;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAE5D,YAAA,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1B,aAAa,EAAE,IAAI,CAAC,aAAa;AACjC,gBAAA,OAAO,EAAE,cAAc;AAC1B,aAAA,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAE3D,OAAO,IAAI,aAAa,EAAE,CAAC;AAC9B,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAAA,mCAAA,EAAsC,KAAK,CAAA,CAAE,EAC7C,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,YAAA,OAAO,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC/C,SAAA;KACJ;IAED,UAAU,GAAA;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;AAED;;;;;AAKG;IACH,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;KAC/B;AAED;;;AAGG;IACH,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;KACrC;AAED;;;;;;AAMG;IACH,MAAM,cAAc,CAChB,0BAAsD,EAAA;QAEtD,IAAI;YACA,kCAAkC,CAC9B,4BAA4B,EAC5B,0BAA0B,EAC1B,IAAI,CAAC,aAAa,CACrB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAEpE,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,CACxB,CAAC;YAEF,IAAI,CAAC,cAAc,EAAE;AACjB,gBAAA,MAAM,IAAI,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3D,aAAA;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAEjE,YAAA,MAAM,SAAS,GACX,0BAA0B,CAAC,MAAM;AACjC,gBAAA,0BAA0B,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;kBACtC,0BAA0B,CAAC,MAAM;AACnC,kBAAE,CAAC,GAAG,aAAa,CAAC,CAAC;AAC7B,YAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,6BAA6B,CAC9D,cAAc,EACd,0BAA0B,CAAC,YAAY,EACvC,SAAS,CACZ,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAC9C,uBAAuB,CAC1B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2CAA2C,EAC3C,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,YAAA,OAAO,IAAI,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAC3C,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,wCAAwC,EACxC,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,YAAA,OAAO,oBAAoB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACtD,SAAA;KACJ;AAEO,IAAA,6BAA6B,CACjC,WAAwB,EACxB,YAAwB,GAAA,KAAK,EAC7B,aAA4B,EAAA;AAE5B,QAAA,MAAM,aAAa,GAAkB;AACjC,YAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;YACrC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,MAAM,EAAE,aAAa,IAAI,EAAE;AAC3B,YAAA,OAAO,EAAE,WAAW;YACpB,YAAY,EAAE,YAAY,IAAI,KAAK;AACnC,YAAA,YAAY,EAAE;AACV,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,YAAY,EAAE,IAAI;AACrB,aAAA;SACJ,CAAC;QAEF,OAAO;AACH,YAAA,GAAG,aAAa;YAChB,oBAAoB,EAAE,oBAAoB,CAAC,MAAM;SACzB,CAAC;KAChC;AACJ;;;;"}