{"version": 3, "file": "parseKeyVaultIdentifier.js", "sourceRoot": "", "sources": ["../../src/parseKeyVaultIdentifier.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AA0BlC,0DAyCC;AA/CD;;;;;GAKG;AACH,SAAgB,uBAAuB,CACrC,UAAkB,EAClB,UAA8B;IAE9B,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;QACxE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;QACxE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,OAAO,CAAC;IACZ,IAAI,CAAC;QACH,OAAO,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,WAAW,UAAU,gBAAgB,UAAU,mBAAmB,CAAC,CAAC;IACtF,CAAC;IAED,mDAAmD;IACnD,MAAM,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnD,MAAM,IAAI,KAAK,CACb,WAAW,UAAU,gBAAgB,UAAU,6BAA6B,QAAQ,CAAC,MAAM,EAAE,CAC9F,CAAC;IACJ,CAAC;IAED,IAAI,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CACb,WAAW,UAAU,gBAAgB,UAAU,4BAA4B,UAAU,aAAa,QAAQ,CAAC,CAAC,CAAC,GAAG,CACjH,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;IACxD,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAChE,OAAO;QACL,QAAQ;QACR,IAAI;QACJ,OAAO;KACR,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * The parsed components of a Key Vault entity identifier.\n */\nexport interface KeyVaultEntityIdentifier {\n  /**\n   * The vault URI.\n   */\n  vaultUrl: string;\n  /**\n   * The version of key/secret/certificate. May be undefined.\n   */\n  version?: string;\n  /**\n   * The name of key/secret/certificate.\n   */\n  name: string;\n}\n\n/**\n * Parses a Key Vault identifier into its components.\n *\n * @param collection - The collection of the Key Vault identifier.\n * @param identifier - The Key Vault identifier to be parsed.\n */\nexport function parseKeyVaultIdentifier(\n  collection: string,\n  identifier: string | undefined,\n): KeyVaultEntityIdentifier {\n  if (typeof collection !== \"string\" || !(collection = collection.trim())) {\n    throw new Error(\"Invalid collection argument\");\n  }\n\n  if (typeof identifier !== \"string\" || !(identifier = identifier.trim())) {\n    throw new Error(\"Invalid identifier argument\");\n  }\n\n  let baseUri;\n  try {\n    baseUri = new URL(identifier);\n  } catch (e: any) {\n    throw new Error(`Invalid ${collection} identifier: ${identifier}. Not a valid URI`);\n  }\n\n  // Path is of the form '/collection/name[/version]'\n  const segments = (baseUri.pathname || \"\").split(\"/\");\n  if (segments.length !== 3 && segments.length !== 4) {\n    throw new Error(\n      `Invalid ${collection} identifier: ${identifier}. Bad number of segments: ${segments.length}`,\n    );\n  }\n\n  if (collection !== segments[1]) {\n    throw new Error(\n      `Invalid ${collection} identifier: ${identifier}. segment [1] should be \"${collection}\", found \"${segments[1]}\"`,\n    );\n  }\n\n  const vaultUrl = `${baseUri.protocol}//${baseUri.host}`;\n  const name = segments[2];\n  const version = segments.length === 4 ? segments[3] : undefined;\n  return {\n    vaultUrl,\n    name,\n    version,\n  };\n}\n"]}