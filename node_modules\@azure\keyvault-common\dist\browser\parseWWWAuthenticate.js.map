{"version": 3, "file": "parseWWWAuthenticate.js", "sourceRoot": "", "sources": ["../../src/parseWWWAuthenticate.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AA0ClC,MAAM,8BAA8B,GAAuC;IACzE,eAAe;IACf,mBAAmB;IACnB,UAAU;IACV,OAAO;IACP,UAAU;IACV,QAAQ;IACR,OAAO;CACC,CAAC;AAEX;;;;;;;GAOG;AACH,MAAM,UAAU,0BAA0B,CAAC,WAAmB;IAC5D,MAAM,aAAa,GAAG,MAAM,CAAC;IAC7B,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,MAAM,CAAkB,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;QACrF,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACpB,2FAA2F;YAC3F,MAAM,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,8BAA8B,CAAC,QAAQ,CAAC,GAA4B,CAAC,EAAE,CAAC;gBAC1E,uEAAuE;gBACvE,uCAAY,OAAO,KAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAG;YAC7D,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,sGAAsG;IACtG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACrE,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC7B,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,oCAAoC,MAAM,CAAC,aAAa,eAAe,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Parameters parsed out of the WWW-Authenticate header value by the parseWWWAuthenticate function.\n */\nexport interface WWWAuthenticate {\n  /**\n   * The authorization parameter, if present.\n   */\n  authorization?: string;\n\n  /**\n   * The authorization_url parameter, if present.\n   */\n  authorization_url?: string;\n\n  /**\n   * The resource parameter, if present.\n   */\n  resource?: string;\n\n  /**\n   * The scope parameter, if present.\n   */\n  scope?: string;\n\n  /**\n   * The tenantId parameter, if present.\n   */\n  tenantId?: string;\n\n  /**\n   * The claims parameter, if present.\n   */\n  claims?: string;\n\n  /**\n   * The error parameter, if present.\n   */\n  error?: string;\n}\n\nconst validWWWAuthenticateProperties: readonly (keyof WWWAuthenticate)[] = [\n  \"authorization\",\n  \"authorization_url\",\n  \"resource\",\n  \"scope\",\n  \"tenantId\",\n  \"claims\",\n  \"error\",\n] as const;\n\n/**\n * Parses an WWW-Authenticate response header.\n * This transforms a string value like:\n * `Bearer authorization=\"https://some.url/tenantId\", resource=\"https://some.url\"`\n * into an object like:\n * `{ authorization: \"https://some.url/tenantId\", resource: \"https://some.url\" }`\n * @param headerValue - String value in the WWW-Authenticate header\n */\nexport function parseWWWAuthenticateHeader(headerValue: string): WWWAuthenticate {\n  const pairDelimiter = /,? +/;\n  const parsed = headerValue.split(pairDelimiter).reduce<WWWAuthenticate>((kvPairs, p) => {\n    if (p.match(/\\w=\"/)) {\n      // 'sampleKey=\"sample_value\"' -> [sampleKey, \"sample_value\"] -> { sampleKey: sample_value }\n      const [key, ...value] = p.split(\"=\");\n      if (validWWWAuthenticateProperties.includes(key as keyof WWWAuthenticate)) {\n        // The values will be wrapped in quotes, which need to be stripped out.\n        return { ...kvPairs, [key]: value.join(\"=\").slice(1, -1) };\n      }\n    }\n    return kvPairs;\n  }, {});\n\n  // Finally, we pull the tenantId from the authorization header to support multi-tenant authentication.\n  if (parsed.authorization) {\n    try {\n      const tenantId = new URL(parsed.authorization).pathname.substring(1);\n      if (tenantId) {\n        parsed.tenantId = tenantId;\n      }\n    } catch (_) {\n      throw new Error(`The challenge authorization URI '${parsed.authorization}' is invalid.`);\n    }\n  }\n\n  return parsed;\n}\n"]}