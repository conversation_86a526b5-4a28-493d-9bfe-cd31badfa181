/**
 * The parsed components of a Key Vault entity identifier.
 */
export interface KeyVaultEntityIdentifier {
    /**
     * The vault URI.
     */
    vaultUrl: string;
    /**
     * The version of key/secret/certificate. May be undefined.
     */
    version?: string;
    /**
     * The name of key/secret/certificate.
     */
    name: string;
}
/**
 * Parses a Key Vault identifier into its components.
 *
 * @param collection - The collection of the Key Vault identifier.
 * @param identifier - The Key Vault identifier to be parsed.
 */
export declare function parseKeyVaultIdentifier(collection: string, identifier: string | undefined): KeyVaultEntityIdentifier;
//# sourceMappingURL=parseKeyVaultIdentifier.d.ts.map