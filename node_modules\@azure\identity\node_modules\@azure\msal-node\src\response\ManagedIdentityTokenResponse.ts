/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

import { AuthenticationScheme } from "@azure/msal-common/node";

/**
 * Deserialized response object from server managed identity request.
 *
 * In case of success:
 * - access_token    - The requested access token. When called via a secured REST API, the token is embedded in the Authorization request header field as a "bearer" token, allowing the API to authenticate the caller
 * - client_id       - A unique identifier generated by Azure AD for the Azure Resource. The Client ID is a GUID value that uniquely identifies the application and its configuration within the identity platform
 * - expires_on      - The timespan when the access token expires. The date is represented as the number of seconds from "1970-01-01T0:0:0Z UTC" (corresponds to the token's exp claim)
 * - resource        - The resource the access token was requested for. It matches the resource query string parameter of the request
 * - token_type      - The type of token returned by the Managed Identity endpoint. It's a "Bearer" access token, which means the resource can give access to the bearer of this token
 *
 * In case of error:
 * - message: A specific error message that can help a developer identify the root cause of an authentication error.
 * - correlationId: A unique identifier for the request that can help in diagnostics across components.
 */
export type ManagedIdentityTokenResponse = {
    // success
    access_token?: string;
    client_id?: string;
    expires_on?: number; // will be converted to expires_in
    resource?: string; // equivalent to ServerAuthorizationTokenResponse's "scope" field
    token_type?: AuthenticationScheme;

    // error

    /*
     * (Web/Function) App Service
     * 500 errors can return this from all MI sources as well
     */
    message?: string;
    correlationId?: string;

    // IMDS, Azure Arc, Service Fabric (unconfirmed)
    error?: string | ErrorObject;
    error_description?: string;
    error_codes?: Array<string>;
    correlation_id?: string;
    timestamp?: string;
    trace_id?: string;
};

/*
 * This is the only error property that exists for Cloud Shell
 * It can also be the only thing App Service will return
 */
export type ErrorObject = {
    code: string;
    message: string;
};
