/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { SignUpResendCodeError } from '../error_type/SignUpError.mjs';
import { SignUpFailedState } from '../state/SignUpFailedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of resending code in a sign-up operation.
 */
class SignUpResendCodeResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of SignUpResendCodeResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of SignUpResendCodeResult with an error.
     * @param error The error that occurred.
     * @returns {SignUpResendCodeResult} A new instance of SignUpResendCodeResult with the error set.
     */
    static createWithError(error) {
        const result = new SignUpResendCodeResult(new SignUpFailedState());
        result.error = new SignUpResendCodeError(SignUpResendCodeResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignUpFailedState;
    }
    /**
     * Checks if the result is in a code required state.
     */
    isCodeRequired() {
        /*
         * The instanceof operator couldn't be used here to check the state type since the circular dependency issue.
         * So we are using the constructor name to check the state type.
         */
        return this.state.constructor?.name === "SignUpCodeRequiredState";
    }
}

export { SignUpResendCodeResult };
//# sourceMappingURL=SignUpResendCodeResult.mjs.map
