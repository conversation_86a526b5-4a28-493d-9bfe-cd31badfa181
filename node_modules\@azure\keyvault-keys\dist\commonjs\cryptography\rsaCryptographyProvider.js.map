{"version": 3, "file": "rsaCryptographyProvider.js", "sourceRoot": "", "sources": ["../../../src/cryptography/rsaCryptographyProvider.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,yCAAsE;AACtE,6CAA4C;AAC5C,2CAA2C;AAoB3C,qDAAmD;AAEnD,2CAAgE;AAEhE;;GAEG;AACH,MAAa,uBAAuB;IAClC,YAAY,GAAe;QAmH3B;;WAEG;QACK,yBAAoB,GAAa;YACvC,QAAQ;YACR,UAAU;YACV,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;SACR,CAAC;QAEF;;WAEG;QACK,yBAAoB,GAAoC;YAC9D,SAAS;YACT,SAAS;YACT,YAAY;SACb,CAAC;QAEF;;;WAGG;QACH,sCAAiC,GAA4B;YAC3D,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;SAChB,CAAC;QApJA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,SAAiB,EAAE,SAAwC;QACrE,OAAO,CACL,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAC/F,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,iBAAoC,EAAE,QAAyB;QACrE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,MAAM,MAAM,GAAG,IAAA,gCAAe,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEzC,MAAM,OAAO,GACX,iBAAiB,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,6BAAiB,CAAC,CAAC,CAAC,kCAAsB,CAAC;QAExF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,SAAS,EAAE,iBAAiB,CAAC,SAAS;YACtC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG;YACnB,MAAM,EAAE,IAAA,2BAAa,EACnB,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EACjC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CACzC;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CACL,kBAAqC,EACrC,QAAyB;QAEzB,MAAM,IAAI,6CAAiC,CACzC,uDAAuD,CACxD,CAAC;IACJ,CAAC;IAED,OAAO,CACL,SAA2B,EAC3B,SAAqB,EACrB,QAAyB;QAEzB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,MAAM,MAAM,GAAG,IAAA,gCAAe,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEzC,MAAM,OAAO,GAAG,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,6BAAiB,CAAC,CAAC,CAAC,kCAAsB,CAAC;QAEpF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,SAAS,EAAE,SAA6B;YACxC,MAAM,EAAE,IAAA,2BAAa,EAAC,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG;SACpB,CAAC,CAAC;IACL,CAAC;IAED,SAAS,CACP,UAA4B,EAC5B,aAAyB,EACzB,QAA2B;QAE3B,MAAM,IAAI,6CAAiC,CACzC,6DAA6D,CAC9D,CAAC;IACJ,CAAC;IAED,IAAI,CACF,UAA8B,EAC9B,OAAmB,EACnB,QAAsB;QAEtB,MAAM,IAAI,6CAAiC,CACzC,6DAA6D,CAC9D,CAAC;IACJ,CAAC;IAED,QAAQ,CACN,UAA8B,EAC9B,KAAiB,EACjB,QAAsB;QAEtB,MAAM,IAAI,6CAAiC,CACzC,oEAAoE,CACrE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CACV,UAA8B,EAC9B,OAAmB,EACnB,UAAsB,EACtB,QAAwB;QAExB,MAAM,IAAI,6CAAiC,CACzC,+DAA+D,CAChE,CAAC;IACJ,CAAC;IAED,UAAU,CACR,SAA6B,EAC7B,IAAgB,EAChB,SAAqB,EACrB,QAAwB;QAExB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,MAAM,MAAM,GAAG,IAAA,gCAAe,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEzC,MAAM,QAAQ,GAAG,IAAA,wBAAY,EAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/C,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG;SACpB,CAAC,CAAC;IACL,CAAC;IA2CO,WAAW;;QACjB,IACE,IAAI,CAAC,GAAG;YACR,CAAA,MAAA,IAAI,CAAC,GAAG,CAAC,GAAG,0CAAE,WAAW,EAAE,MAAK,KAAK;YACrC,CAAA,MAAA,IAAI,CAAC,GAAG,CAAC,GAAG,0CAAE,WAAW,EAAE,MAAK,SAAS,EACzC,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;CACF;AAjKD,0DAiKC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { RSA_PKCS1_OAEP_PADDING, RSA_PKCS1_PADDING } from \"constants\";\nimport { publicEncrypt } from \"node:crypto\";\nimport { createVerify } from \"./crypto.js\";\nimport type {\n  DecryptOptions,\n  DecryptParameters,\n  DecryptResult,\n  EncryptOptions,\n  EncryptParameters,\n  EncryptResult,\n  JsonWebKey,\n  KeyWrapAlgorithm,\n  SignOptions,\n  SignResult,\n  SignatureAlgorithm,\n  UnwrapKeyOptions,\n  UnwrapResult,\n  VerifyOptions,\n  VerifyResult,\n  Wrap<PERSON>eyOptions,\n  WrapResult,\n} from \"../index.js\";\nimport { convertJWKtoPEM } from \"./conversions.js\";\nimport type { CryptographyProvider, CryptographyProviderOperation } from \"./models.js\";\nimport { LocalCryptographyUnsupportedError } from \"./models.js\";\n\n/**\n * An RSA cryptography provider supporting RSA algorithms.\n */\nexport class RsaCryptographyProvider implements CryptographyProvider {\n  constructor(key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {\n    this.key = key;\n  }\n\n  isSupported(algorithm: string, operation: CryptographyProviderOperation): boolean {\n    return (\n      this.applicableAlgorithms.includes(algorithm) && this.applicableOperations.includes(operation)\n    );\n  }\n\n  encrypt(encryptParameters: EncryptParameters, _options?: EncryptOptions): Promise<EncryptResult> {\n    this.ensureValid();\n    const keyPEM = convertJWKtoPEM(this.key);\n\n    const padding =\n      encryptParameters.algorithm === \"RSA1_5\" ? RSA_PKCS1_PADDING : RSA_PKCS1_OAEP_PADDING;\n\n    return Promise.resolve({\n      algorithm: encryptParameters.algorithm,\n      keyID: this.key.kid,\n      result: publicEncrypt(\n        { key: keyPEM, padding: padding },\n        Buffer.from(encryptParameters.plaintext),\n      ),\n    });\n  }\n\n  decrypt(\n    _decryptParameters: DecryptParameters,\n    _options?: DecryptOptions,\n  ): Promise<DecryptResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Decrypting using a local JsonWebKey is not supported.\",\n    );\n  }\n\n  wrapKey(\n    algorithm: KeyWrapAlgorithm,\n    keyToWrap: Uint8Array,\n    _options?: WrapKeyOptions,\n  ): Promise<WrapResult> {\n    this.ensureValid();\n    const keyPEM = convertJWKtoPEM(this.key);\n\n    const padding = algorithm === \"RSA1_5\" ? RSA_PKCS1_PADDING : RSA_PKCS1_OAEP_PADDING;\n\n    return Promise.resolve({\n      algorithm: algorithm as KeyWrapAlgorithm,\n      result: publicEncrypt({ key: keyPEM, padding }, Buffer.from(keyToWrap)),\n      keyID: this.key.kid,\n    });\n  }\n\n  unwrapKey(\n    _algorithm: KeyWrapAlgorithm,\n    _encryptedKey: Uint8Array,\n    _options?: UnwrapKeyOptions,\n  ): Promise<UnwrapResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Unwrapping a key using a local JsonWebKey is not supported.\",\n    );\n  }\n\n  sign(\n    _algorithm: SignatureAlgorithm,\n    _digest: Uint8Array,\n    _options?: SignOptions,\n  ): Promise<SignResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Signing a digest using a local JsonWebKey is not supported.\",\n    );\n  }\n\n  signData(\n    _algorithm: SignatureAlgorithm,\n    _data: Uint8Array,\n    _options?: SignOptions,\n  ): Promise<SignResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Signing a block of data using a local JsonWebKey is not supported.\",\n    );\n  }\n\n  async verify(\n    _algorithm: SignatureAlgorithm,\n    _digest: Uint8Array,\n    _signature: Uint8Array,\n    _options?: VerifyOptions,\n  ): Promise<VerifyResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Verifying a digest using a local JsonWebKey is not supported.\",\n    );\n  }\n\n  verifyData(\n    algorithm: SignatureAlgorithm,\n    data: Uint8Array,\n    signature: Uint8Array,\n    _options?: VerifyOptions,\n  ): Promise<VerifyResult> {\n    this.ensureValid();\n    const keyPEM = convertJWKtoPEM(this.key);\n\n    const verifier = createVerify(algorithm, data);\n    return Promise.resolve({\n      result: verifier.verify(keyPEM, Buffer.from(signature)),\n      keyID: this.key.kid,\n    });\n  }\n\n  /**\n   * The {@link JsonWebKey} used to perform crypto operations.\n   */\n  private key: JsonWebKey;\n\n  /**\n   * The set of algorithms this provider supports\n   */\n  private applicableAlgorithms: string[] = [\n    \"RSA1_5\",\n    \"RSA-OAEP\",\n    \"PS256\",\n    \"RS256\",\n    \"PS384\",\n    \"RS384\",\n    \"PS512\",\n    \"RS512\",\n  ];\n\n  /**\n   * The set of operations this provider supports\n   */\n  private applicableOperations: CryptographyProviderOperation[] = [\n    \"encrypt\",\n    \"wrapKey\",\n    \"verifyData\",\n  ];\n\n  /**\n   * Mapping between signature algorithms and their corresponding hash algorithms. Externally used for testing.\n   * @internal\n   */\n  signatureAlgorithmToHashAlgorithm: { [s: string]: string } = {\n    PS256: \"SHA256\",\n    RS256: \"SHA256\",\n    PS384: \"SHA384\",\n    RS384: \"SHA384\",\n    PS512: \"SHA512\",\n    RS512: \"SHA512\",\n  };\n\n  private ensureValid(): void {\n    if (\n      this.key &&\n      this.key.kty?.toUpperCase() !== \"RSA\" &&\n      this.key.kty?.toUpperCase() !== \"RSA-HSM\"\n    ) {\n      throw new Error(\"Key type does not match the algorithm RSA\");\n    }\n  }\n}\n"]}