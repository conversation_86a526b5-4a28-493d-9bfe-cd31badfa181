{"version": 3, "file": "ApiRequestTypes.d.ts", "sourceRoot": "", "sources": ["../../../../../../../src/custom_auth/core/network_client/custom_auth_api/types/ApiRequestTypes.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAGnD,MAAM,WAAW,qBAAsB,SAAQ,cAAc;IACzD,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,sBAAuB,SAAQ,cAAc;IAC1D,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,MAAM,CAAC;CAC9B;AAED,UAAU,sBAAuB,SAAQ,cAAc;IACnD,kBAAkB,EAAE,MAAM,CAAC;IAC3B,KAAK,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,0BAA2B,SAAQ,sBAAsB;IACtE,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,qBAAsB,SAAQ,sBAAsB;IACjE,GAAG,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,8BAA+B,SAAQ,sBAAsB;IAC1E,QAAQ,EAAE,MAAM,CAAC;CACpB;AAGD,MAAM,WAAW,kBAAmB,SAAQ,cAAc;IACtD,QAAQ,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACvC;AAED,MAAM,WAAW,sBAAuB,SAAQ,cAAc;IAC1D,kBAAkB,EAAE,MAAM,CAAC;IAC3B,cAAc,EAAE,MAAM,CAAC;CAC1B;AAED,UAAU,yBAA0B,SAAQ,cAAc;IACtD,kBAAkB,EAAE,MAAM,CAAC;CAC9B;AAED,MAAM,WAAW,4BACb,SAAQ,yBAAyB;IACjC,GAAG,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,iCACb,SAAQ,yBAAyB;IACjC,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,mCACb,SAAQ,yBAAyB;IACjC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACtC;AAGD,MAAM,WAAW,yBAA0B,SAAQ,cAAc;IAC7D,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,6BAA8B,SAAQ,cAAc;IACjE,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,MAAM,CAAC;CAC9B;AAED,MAAM,WAAW,4BAA6B,SAAQ,cAAc;IAChE,kBAAkB,EAAE,MAAM,CAAC;IAC3B,GAAG,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,0BAA2B,SAAQ,cAAc;IAC9D,kBAAkB,EAAE,MAAM,CAAC;IAC3B,YAAY,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,kCAAmC,SAAQ,cAAc;IACtE,kBAAkB,EAAE,MAAM,CAAC;CAC9B"}