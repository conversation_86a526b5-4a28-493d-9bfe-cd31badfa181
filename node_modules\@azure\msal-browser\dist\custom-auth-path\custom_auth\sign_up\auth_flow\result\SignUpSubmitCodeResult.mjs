/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { SignUpSubmitCodeError } from '../error_type/SignUpError.mjs';
import { SignUpAttributesRequiredState } from '../state/SignUpAttributesRequiredState.mjs';
import { SignUpPasswordRequiredState } from '../state/SignUpPasswordRequiredState.mjs';
import { SignUpCompletedState } from '../state/SignUpCompletedState.mjs';
import { SignUpFailedState } from '../state/SignUpFailedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a sign-up operation that requires a code.
 */
class SignUpSubmitCodeResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of SignUpSubmitCodeResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of SignUpSubmitCodeResult with an error.
     * @param error The error that occurred.
     * @returns {SignUpSubmitCodeResult} A new instance of SignUpSubmitCodeResult with the error set.
     */
    static createWithError(error) {
        const result = new SignUpSubmitCodeResult(new SignUpFailedState());
        result.error = new SignUpSubmitCodeError(SignUpSubmitCodeResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignUpFailedState;
    }
    /**
     * Checks if the result is in a password required state.
     */
    isPasswordRequired() {
        return this.state instanceof SignUpPasswordRequiredState;
    }
    /**
     * Checks if the result is in an attributes required state.
     */
    isAttributesRequired() {
        return this.state instanceof SignUpAttributesRequiredState;
    }
    /**
     * Checks if the result is in a completed state.
     */
    isCompleted() {
        return this.state instanceof SignUpCompletedState;
    }
}

export { SignUpSubmitCodeResult };
//# sourceMappingURL=SignUpSubmitCodeResult.mjs.map
