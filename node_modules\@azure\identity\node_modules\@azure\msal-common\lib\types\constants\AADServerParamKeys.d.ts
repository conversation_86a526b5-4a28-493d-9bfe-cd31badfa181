export declare const CLIENT_ID = "client_id";
export declare const REDIRECT_URI = "redirect_uri";
export declare const RESPONSE_TYPE = "response_type";
export declare const RESPONSE_MODE = "response_mode";
export declare const GRANT_TYPE = "grant_type";
export declare const CLAIMS = "claims";
export declare const SCOPE = "scope";
export declare const ERROR = "error";
export declare const ERROR_DESCRIPTION = "error_description";
export declare const ACCESS_TOKEN = "access_token";
export declare const ID_TOKEN = "id_token";
export declare const REFRESH_TOKEN = "refresh_token";
export declare const EXPIRES_IN = "expires_in";
export declare const REFRESH_TOKEN_EXPIRES_IN = "refresh_token_expires_in";
export declare const STATE = "state";
export declare const NONCE = "nonce";
export declare const PROMPT = "prompt";
export declare const SESSION_STATE = "session_state";
export declare const CLIENT_INFO = "client_info";
export declare const CODE = "code";
export declare const CODE_CHALLENGE = "code_challenge";
export declare const CODE_CHALLENGE_METHOD = "code_challenge_method";
export declare const CODE_VERIFIER = "code_verifier";
export declare const CLIENT_REQUEST_ID = "client-request-id";
export declare const X_CLIENT_SKU = "x-client-SKU";
export declare const X_CLIENT_VER = "x-client-VER";
export declare const X_CLIENT_OS = "x-client-OS";
export declare const X_CLIENT_CPU = "x-client-CPU";
export declare const X_CLIENT_CURR_TELEM = "x-client-current-telemetry";
export declare const X_CLIENT_LAST_TELEM = "x-client-last-telemetry";
export declare const X_MS_LIB_CAPABILITY = "x-ms-lib-capability";
export declare const X_APP_NAME = "x-app-name";
export declare const X_APP_VER = "x-app-ver";
export declare const POST_LOGOUT_URI = "post_logout_redirect_uri";
export declare const ID_TOKEN_HINT = "id_token_hint";
export declare const DEVICE_CODE = "device_code";
export declare const CLIENT_SECRET = "client_secret";
export declare const CLIENT_ASSERTION = "client_assertion";
export declare const CLIENT_ASSERTION_TYPE = "client_assertion_type";
export declare const TOKEN_TYPE = "token_type";
export declare const REQ_CNF = "req_cnf";
export declare const OBO_ASSERTION = "assertion";
export declare const REQUESTED_TOKEN_USE = "requested_token_use";
export declare const ON_BEHALF_OF = "on_behalf_of";
export declare const FOCI = "foci";
export declare const CCS_HEADER = "X-AnchorMailbox";
export declare const RETURN_SPA_CODE = "return_spa_code";
export declare const NATIVE_BROKER = "nativebroker";
export declare const LOGOUT_HINT = "logout_hint";
export declare const SID = "sid";
export declare const LOGIN_HINT = "login_hint";
export declare const DOMAIN_HINT = "domain_hint";
export declare const X_CLIENT_EXTRA_SKU = "x-client-xtra-sku";
export declare const BROKER_CLIENT_ID = "brk_client_id";
export declare const BROKER_REDIRECT_URI = "brk_redirect_uri";
export declare const INSTANCE_AWARE = "instance_aware";
export declare const EAR_JWK = "ear_jwk";
export declare const EAR_JWE_CRYPTO = "ear_jwe_crypto";
//# sourceMappingURL=AADServerParamKeys.d.ts.map