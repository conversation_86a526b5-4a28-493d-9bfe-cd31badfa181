import { Authority, CommonAuthorizationUrlRequest, Logger } from "@azure/msal-common/node";
import { NodeConfiguration } from "../config/Configuration.js";
/**
 * Constructs the full /authorize URL with request parameters
 * @param config
 * @param authority
 * @param request
 * @param logger
 * @returns
 */
export declare function getAuthCodeRequestUrl(config: NodeConfiguration, authority: Authority, request: CommonAuthorizationUrlRequest, logger: Logger): string;
//# sourceMappingURL=Authorize.d.ts.map