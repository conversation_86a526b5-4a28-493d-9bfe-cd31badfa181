/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * The public API ids should be claim in the MSAL telemtry tracker.
 * All the following ids are hardcoded; so we need to find a way to claim them in the future and update them here.
 */
// Sign in
const SIGN_IN_WITH_CODE_START = 100001;
const SIGN_IN_WITH_PASSWORD_START = 100002;
const SIGN_IN_SUBMIT_CODE = 100003;
const SIGN_IN_SUBMIT_PASSWORD = 100004;
const SIGN_IN_RESEND_CODE = 100005;
const SIGN_IN_AFTER_SIGN_UP = 100006;
const SIGN_IN_AFTER_PASSWORD_RESET = 100007;
// Sign up
const SIGN_UP_WITH_PASSWORD_START = 100021;
const SIGN_UP_START = 100022;
const SIGN_UP_SUBMIT_CODE = 100023;
const SIGN_UP_SUBMIT_PASSWORD = 100024;
const SIGN_UP_SUBMIT_ATTRIBUTES = 100025;
const SIGN_UP_RESEND_CODE = 100026;
// Password reset
const PASSWORD_RESET_START = 100041;
const PASSWORD_RESET_SUBMIT_CODE = 100042;
const PASSWORD_RESET_SUBMIT_PASSWORD = 100043;
const PASSWORD_RESET_RESEND_CODE = 100044;
const ACCOUNT_GET_ACCESS_TOKEN = 100063;

export { ACCOUNT_GET_ACCESS_TOKEN, PASSWORD_RESET_RESEND_CODE, PASSWORD_RESET_START, PASSWORD_RESET_SUBMIT_CODE, PASSWORD_RESET_SUBMIT_PASSWORD, SIGN_IN_AFTER_PASSWORD_RESET, SIGN_IN_AFTER_SIGN_UP, SIGN_IN_RESEND_CODE, SIGN_IN_SUBMIT_CODE, SIGN_IN_SUBMIT_PASSWORD, SIGN_IN_WITH_CODE_START, SIGN_IN_WITH_PASSWORD_START, SIGN_UP_RESEND_CODE, SIGN_UP_START, SIGN_UP_SUBMIT_ATTRIBUTES, SIGN_UP_SUBMIT_CODE, SIGN_UP_SUBMIT_PASSWORD, SIGN_UP_WITH_PASSWORD_START };
//# sourceMappingURL=PublicApiId.mjs.map
