/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { PerformanceEvents, invokeAsync, ProtocolMode, HttpMethod, createClientConfigurationError, ClientConfigurationErrorCodes, AuthenticationScheme, StringUtils } from '@azure/msal-common/browser';
import { hashString } from '../crypto/BrowserCrypto.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Initializer function for all request APIs
 * @param request
 */
async function initializeBaseRequest(request, config, performanceClient, logger) {
    performanceClient.addQueueMeasurement(PerformanceEvents.InitializeBaseRequest, request.correlationId);
    const authority = request.authority || config.auth.authority;
    const scopes = [...((request && request.scopes) || [])];
    const validatedRequest = {
        ...request,
        correlationId: request.correlationId,
        authority,
        scopes,
    };
    // Set authenticationScheme to BEARER if not explicitly set in the request
    if (!validatedRequest.authenticationScheme) {
        validatedRequest.authenticationScheme = AuthenticationScheme.BEARER;
        logger.verbose('Authentication Scheme wasn\'t explicitly set in request, defaulting to "Bearer" request');
    }
    else {
        if (validatedRequest.authenticationScheme === AuthenticationScheme.SSH) {
            if (!request.sshJwk) {
                throw createClientConfigurationError(ClientConfigurationErrorCodes.missingSshJwk);
            }
            if (!request.sshKid) {
                throw createClientConfigurationError(ClientConfigurationErrorCodes.missingSshKid);
            }
        }
        logger.verbose(`Authentication Scheme set to "${validatedRequest.authenticationScheme}" as configured in Auth request`);
    }
    // Set requested claims hash if claims-based caching is enabled and claims were requested
    if (config.cache.claimsBasedCachingEnabled &&
        request.claims &&
        // Checks for empty stringified object "{}" which doesn't qualify as requested claims
        !StringUtils.isEmptyObj(request.claims)) {
        validatedRequest.requestedClaimsHash = await hashString(request.claims);
    }
    return validatedRequest;
}
async function initializeSilentRequest(request, account, config, performanceClient, logger) {
    performanceClient.addQueueMeasurement(PerformanceEvents.InitializeSilentRequest, request.correlationId);
    const baseRequest = await invokeAsync(initializeBaseRequest, PerformanceEvents.InitializeBaseRequest, logger, performanceClient, request.correlationId)(request, config, performanceClient, logger);
    return {
        ...request,
        ...baseRequest,
        account: account,
        forceRefresh: request.forceRefresh || false,
    };
}
/**
 * Validates that the combination of request method, protocol mode and authorize body parameters is correct.
 * Returns the validated or defaulted HTTP method or throws if the configured combination is invalid.
 * @param interactionRequest
 * @param protocolMode
 * @returns
 */
function validateRequestMethod(interactionRequest, protocolMode) {
    let httpMethod;
    const requestMethod = interactionRequest.httpMethod;
    if (protocolMode === ProtocolMode.EAR) {
        // Don't override httpMethod if it is already set, default to POST if not set
        httpMethod = requestMethod || HttpMethod.POST;
        // Validate that method is not GET if protocol mode is EAR
        if (httpMethod !== HttpMethod.POST) {
            throw createClientConfigurationError(ClientConfigurationErrorCodes.invalidRequestMethodForEAR);
        }
    }
    else {
        // For non-EAR protocol modes, default to GET if httpMethod is not set
        httpMethod = requestMethod || HttpMethod.GET;
    }
    // Regardless of protocolMode, if there are authorizePostBodyParameters, validate the request method is POST
    if (interactionRequest.authorizePostBodyParameters &&
        httpMethod !== HttpMethod.POST) {
        throw createClientConfigurationError(ClientConfigurationErrorCodes.invalidAuthorizePostBodyParameters);
    }
    return httpMethod;
}

export { initializeBaseRequest, initializeSilentRequest, validateRequestMethod };
//# sourceMappingURL=RequestHelpers.mjs.map
