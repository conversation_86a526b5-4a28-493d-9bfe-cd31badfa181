import { ResetPasswordApiClient } from "./ResetPasswordApiClient.js";
import { SignupApiClient } from "./SignupApiClient.js";
import { SignInApiClient } from "./SignInApiClient.js";
import { ICustomAuthApiClient } from "./ICustomAuthApiClient.js";
import { IHttpClient } from "../http_client/IHttpClient.js";
export declare class CustomAuthApiClient implements ICustomAuthApiClient {
    signInApi: SignInApiClient;
    signUpApi: SignupApiClient;
    resetPasswordApi: ResetPasswordApiClient;
    constructor(customAuthApiBaseUrl: string, clientId: string, httpClient: IHttpClient);
}
//# sourceMappingURL=CustomAuthApiClient.d.ts.map