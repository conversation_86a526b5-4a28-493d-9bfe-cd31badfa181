import { AuthFlowErrorBase } from "../../../core/auth_flow/AuthFlowErrorBase.js";
/**
 * The error class for get account errors.
 */
export declare class GetAccountError extends AuthFlowErrorBase {
    /**
     * Checks if the error is due to no cached account found.
     * @returns true if the error is due to no cached account found, false otherwise.
     */
    isCurrentAccountNotFound(): boolean;
}
/**
 * The error class for sign-out errors.
 */
export declare class SignOutError extends AuthFlowErrorBase {
    /**
     * Checks if the error is due to the user is not signed in.
     * @returns true if the error is due to the user is not signed in, false otherwise.
     */
    isUserNotSignedIn(): boolean;
}
/**
 * The error class for getting the current account access token errors.
 */
export declare class GetCurrentAccountAccessTokenError extends AuthFlowErrorBase {
    /**
     * Checks if the error is due to no cached account found.
     * @returns true if the error is due to no cached account found, false otherwise.
     */
    isCurrentAccountNotFound(): boolean;
}
//# sourceMappingURL=GetAccountError.d.ts.map