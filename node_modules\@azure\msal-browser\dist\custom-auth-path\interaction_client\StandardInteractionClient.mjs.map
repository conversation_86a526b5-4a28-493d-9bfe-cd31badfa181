{"version": 3, "file": "StandardInteractionClient.mjs", "sources": ["../../../../src/interaction_client/StandardInteractionClient.ts"], "sourcesContent": [null], "names": ["BrowserUtils.getCurrentUri"], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAsCH;;AAEG;AACG,MAAgB,yBAA0B,SAAQ,qBAAqB,CAAA;AACzE;;;AAGG;AACO,IAAA,uBAAuB,CAC7B,aAAiC,EAAA;QAEjC,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gCAAgC,EAChC,aAAa,EAAE,aAAa,CAC/B,CAAC;AAEF,QAAA,MAAM,kBAAkB,GAA4B;AAChD,YAAA,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,aAAa,EAAE;AACpD,YAAA,GAAG,aAAa;SACnB,CAAC;AAEF;;;AAGG;AACH,QAAA,IAAI,aAAa,EAAE;;AAEf,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC3B,IAAI,aAAa,CAAC,OAAO,EAAE;oBACvB,MAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAClD,aAAa,CAAC,OAAO,CACxB,CAAC;AACF,oBAAA,IAAI,UAAU,EAAE;AACZ,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gFAAgF,CACnF,CAAC;AACF,wBAAA,kBAAkB,CAAC,UAAU,GAAG,UAAU,CAAC;AAC9C,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mGAAmG,CACtG,CAAC;AACL,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD,CACrD,CAAC;AACL,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mEAAmE,CACtE,CAAC;AACL,SAAA;AAED;;;AAGG;QACH,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,qBAAqB,KAAK,IAAI,EAAE;AAChE,YAAA,IAAI,aAAa,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBACtD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4DAA4D,EAC5D,kBAAkB,CAAC,aAAa,CACnC,CAAC;AACF,gBAAA,kBAAkB,CAAC,qBAAqB;AACpC,oBAAA,SAAS,CAAC,cAAc,CACpB,aAAa,CAAC,qBAAqB,EACnCA,aAA0B,EAAE,CAC/B,CAAC;AACT,aAAA;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBACxD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sGAAsG,EACtG,kBAAkB,CAAC,aAAa,CACnC,CAAC;AACL,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC/C,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iDAAiD,EACjD,kBAAkB,CAAC,aAAa,CACnC,CAAC;AACF,gBAAA,kBAAkB,CAAC,qBAAqB;AACpC,oBAAA,SAAS,CAAC,cAAc,CACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EACtCA,aAA0B,EAAE,CAC/B,CAAC;AACT,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+CAA+C,EAC/C,kBAAkB,CAAC,aAAa,CACnC,CAAC;AACF,gBAAA,kBAAkB,CAAC,qBAAqB;AACpC,oBAAA,SAAS,CAAC,cAAc,CACpBA,aAA0B,EAAE,EAC5BA,aAA0B,EAAE,CAC/B,CAAC;AACT,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4EAA4E,EAC5E,kBAAkB,CAAC,aAAa,CACnC,CAAC;AACL,SAAA;AAED,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAED;;;;AAIG;AACO,IAAA,8BAA8B,CACpC,OAAoB,EAAA;AAEpB,QAAA,MAAM,aAAa,GAA8B,OAAO,CAAC,aAAa,CAAC;AACvE,QAAA,IAAI,aAAa,EAAE;YACf,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC1B,OAAO,aAAa,CAAC,UAAU,CAAC;AACnC,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oIAAoI,CACvI,CAAC;AACL,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uGAAuG,CAC1G,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;;;;;;AASG;IACO,MAAM,oBAAoB,CAAC,MAMpC,EAAA;AACG,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,aAAa,CACrB,CAAC;;AAEF,QAAA,MAAM,YAAY,GAAG,MAAM,WAAW,CAClC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,MAAM,CAAC,CAAC;QAEV,OAAO,IAAI,uBAAuB,CAC9B,YAAY,EACZ,IAAI,CAAC,iBAAiB,CACzB,CAAC;KACL;AAED;;;;;;;;;AASG;IACO,MAAM,sBAAsB,CAAC,MAMtC,EAAA;AACG,QAAA,MAAM,EACF,sBAAsB,EACtB,gBAAgB,EAChB,wBAAwB,EACxB,2BAA2B,EAC3B,OAAO,GACV,GAAG,MAAM,CAAC;AAEX,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,QAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;YACE,gBAAgB;YAChB,wBAAwB;YACxB,2BAA2B;YAC3B,OAAO;AACV,SAAA,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;QAEhD,OAAO;AACH,YAAA,WAAW,EAAE;AACT,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,gBAAA,SAAS,EAAE,mBAAmB;AAC9B,gBAAA,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;AACvD,gBAAA,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;AAC5C,aAAA;AACD,YAAA,aAAa,EAAE;AACX,gBAAA,yBAAyB,EACrB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AAChD,gBAAA,oBAAoB,EAAE,IAAI;AAC7B,aAAA;AACD,YAAA,aAAa,EAAE;gBACX,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;gBAC3C,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;AACpC,aAAA;AACD,YAAA,YAAY,EAAE;AACV,gBAAA,yBAAyB,EACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB;AAClD,aAAA;YACD,eAAe,EAAE,IAAI,CAAC,aAAa;YACnC,gBAAgB,EAAE,IAAI,CAAC,aAAa;YACpC,gBAAgB,EAAE,IAAI,CAAC,cAAc;AACrC,YAAA,sBAAsB,EAAE,sBAAsB;AAC9C,YAAA,WAAW,EAAE;gBACT,GAAG,EAAE,gBAAgB,CAAC,QAAQ;AAC9B,gBAAA,OAAO,EAAE,OAAO;gBAChB,GAAG,EAAE,SAAS,CAAC,YAAY;gBAC3B,EAAE,EAAE,SAAS,CAAC,YAAY;AAC7B,aAAA;AACD,YAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;SACnC,CAAC;KACL;AAED;;;;AAIG;AACO,IAAA,MAAM,8BAA8B,CAC1C,OAA0D,EAC1D,eAAgC,EAAA;AAEhC,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,aAAa,CACrB,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC7D,QAAA,MAAM,YAAY,GAAuB;AACrC,YAAA,eAAe,EAAE,eAAe;SACnC,CAAC;QACF,MAAM,KAAK,GAAG,aAAa,CAAC,eAAe,CACvC,IAAI,CAAC,aAAa,EAClB,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,YAAY,EACpD,YAAY,CACf,CAAC;QAEF,MAAM,WAAW,GAAoB,MAAM,WAAW,CAClD,qBAAqB,EACrB,iBAAiB,CAAC,qBAAqB,EACvC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CACG,EAAE,GAAG,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,EACjD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,CACd,CAAC;AAEF,QAAA,MAAM,kBAAkB,GAAkC;AACtD,YAAA,GAAG,WAAW;AACd,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,aAAa,EAAE;AACvC,YAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;iBACrC,kBAAkC;SAC1C,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAG;AACrB,YAAA,GAAG,kBAAkB;AACrB,YAAA,UAAU,EAAE,qBAAqB,CAC7B,kBAAkB,EAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAChC;SACJ,CAAC;;AAGF,QAAA,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,EAAE;AAClC,YAAA,OAAO,gBAAgB,CAAC;AAC3B,SAAA;AAED,QAAA,MAAM,OAAO,GACT,OAAO,CAAC,OAAO;YACf,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7D,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mCAAmC,EACnC,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,YAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CAAsC,mCAAA,EAAA,OAAO,CAAC,aAAa,EAAE,EAC7D,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,YAAA,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;AACtC,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC;KAC3B;AACJ;;;;"}