/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Represents the state of a sign-in operation that has been failed.
 */
class SignInFailedState extends AuthFlowStateBase {
}

export { SignInFailedState };
//# sourceMappingURL=SignInFailedState.mjs.map
