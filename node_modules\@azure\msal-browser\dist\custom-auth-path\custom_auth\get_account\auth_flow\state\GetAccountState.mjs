/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * The completed state of the get account flow.
 */
class GetAccountCompletedState extends AuthFlowStateBase {
}
/**
 * The failed state of the get account flow.
 */
class GetAccountFailedState extends AuthFlowStateBase {
}

export { GetAccountCompletedState, GetAccountFailedState };
//# sourceMappingURL=GetAccountState.mjs.map
