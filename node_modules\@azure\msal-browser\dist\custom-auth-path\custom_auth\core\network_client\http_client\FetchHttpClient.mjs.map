{"version": 3, "file": "FetchHttpClient.mjs", "sources": ["../../../../../../../src/custom_auth/core/network_client/http_client/FetchHttpClient.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAUH;;AAEG;MACU,eAAe,CAAA;AACxB,IAAA,WAAA,CAAoB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;KAAI;AAEtC,IAAA,MAAM,SAAS,CACX,GAAiB,EACjB,OAAoB,EAAA;AAEpB,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAiC,CAAC;QAC1D,MAAM,aAAa,GACf,OAAO,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC;QAEjE,IAAI;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAsB,mBAAA,EAAA,GAAG,CAAE,CAAA,EAAE,aAAa,CAAC,CAAC;AAEnE,YAAA,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC3C,YAAA,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAElC,YAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CAAA,YAAA,EAAe,GAAG,CACd,eAAA,EAAA,OAAO,GAAG,SACd,CAAA,oBAAA,EAAuB,QAAQ,CAAC,MAAM,EAAE,EACxC,aAAa,CAChB,CAAC;AAEF,YAAA,OAAO,QAAQ,CAAC;AACnB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAAA,0BAAA,EAA6B,GAAG,CAAA,EAAA,EAAK,CAAC,CAAA,CAAE,EACxC,aAAa,CAChB,CAAC;AAEF,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC1B,MAAM,IAAI,SAAS,CACf,qBAAqB,EACrB,CAA4B,yBAAA,EAAA,CAAC,CAAE,CAAA,EAC/B,aAAa,CAChB,CAAC;AACL,aAAA;YAED,MAAM,IAAI,SAAS,CACf,iBAAiB,EACjB,CAA2B,wBAAA,EAAA,CAAC,CAAE,CAAA,EAC9B,aAAa,CAChB,CAAC;AACL,SAAA;KACJ;IAED,MAAM,IAAI,CACN,GAAiB,EACjB,IAAiB,EACjB,UAAkC,EAAE,EAAA;AAEpC,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;YACvB,MAAM,EAAE,UAAU,CAAC,IAAI;YACvB,OAAO;YACP,IAAI;AACP,SAAA,CAAC,CAAC;KACN;AAED,IAAA,MAAM,GAAG,CACL,GAAiB,EACjB,UAAkC,EAAE,EAAA;AAEpC,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;YACvB,MAAM,EAAE,UAAU,CAAC,GAAG;YACtB,OAAO;AACV,SAAA,CAAC,CAAC;KACN;AACJ;;;;"}