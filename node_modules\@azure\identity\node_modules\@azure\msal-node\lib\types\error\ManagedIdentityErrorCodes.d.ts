export declare const invalidFileExtension = "invalid_file_extension";
export declare const invalidFilePath = "invalid_file_path";
export declare const invalidManagedIdentityIdType = "invalid_managed_identity_id_type";
export declare const invalidSecret = "invalid_secret";
export declare const missingId = "missing_client_id";
export declare const networkUnavailable = "network_unavailable";
export declare const platformNotSupported = "platform_not_supported";
export declare const unableToCreateAzureArc = "unable_to_create_azure_arc";
export declare const unableToCreateCloudShell = "unable_to_create_cloud_shell";
export declare const unableToCreateSource = "unable_to_create_source";
export declare const unableToReadSecretFile = "unable_to_read_secret_file";
export declare const urlParseError = "url_parse_error";
export declare const userAssignedNotAvailableAtRuntime = "user_assigned_not_available_at_runtime";
export declare const wwwAuthenticateHeaderMissing = "www_authenticate_header_missing";
export declare const wwwAuthenticateHeaderUnsupportedFormat = "www_authenticate_header_unsupported_format";
export declare const MsiEnvironmentVariableUrlMalformedErrorCodes: {
    readonly AZURE_POD_IDENTITY_AUTHORITY_HOST: "azure_pod_identity_authority_host_url_malformed";
    readonly IDENTITY_ENDPOINT: "identity_endpoint_url_malformed";
    readonly IMDS_ENDPOINT: "imds_endpoint_url_malformed";
    readonly MSI_ENDPOINT: "msi_endpoint_url_malformed";
};
export type MsiEnvironmentVariableErrorCodes = (typeof MsiEnvironmentVariableUrlMalformedErrorCodes)[keyof typeof MsiEnvironmentVariableUrlMalformedErrorCodes];
//# sourceMappingURL=ManagedIdentityErrorCodes.d.ts.map