/**
 * @internal
 * Use the platform-local hashing functionality
 */
export declare function createHash(_algorithm: string, _data: Uint8Array): Promise<Buffer>;
/**
 * @internal
 * Use the platform-local verify functionality
 */
export declare function createVerify(_algorithm: string, _data: Uint8Array): never;
/**
 * @internal
 * Use the platform-local randomBytes functionality
 */
export declare function randomBytes(_length: number): Uint8Array;
//# sourceMappingURL=crypto-browser.d.mts.map