/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthActionErrorBase } from '../../../core/auth_flow/AuthFlowErrorBase.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
class SignUpError extends AuthActionErrorBase {
    /**
     * Checks if the error is due to the user already exists.
     * @returns {boolean} True if the error is due to the user already exists, false otherwise.
     */
    isUserAlreadyExists() {
        return this.isUserAlreadyExistsError();
    }
    /**
     * Checks if the error is due to the username is invalid.
     * @returns {boolean} True if the error is due to the user is invalid, false otherwise.
     */
    isInvalidUsername() {
        return this.isUserInvalidError();
    }
    /**
     * Checks if the error is due to the password being invalid or incorrect.
     * @returns {boolean} True if the error is due to the password being invalid, false otherwise.
     */
    isInvalidPassword() {
        return this.isInvalidNewPasswordError();
    }
    /**
     * Checks if the error is due to the required attributes are missing.
     * @returns {boolean} True if the error is due to the required attributes are missing, false otherwise.
     */
    isMissingRequiredAttributes() {
        return this.isAttributeRequiredError();
    }
    /**
     * Checks if the error is due to the attributes validation failed.
     * @returns {boolean} True if the error is due to the attributes validation failed, false otherwise.
     */
    isAttributesValidationFailed() {
        return this.isAttributeValidationFailedError();
    }
    /**
     * Checks if the error is due to the provided challenge type is not supported.
     * @returns {boolean} True if the error is due to the provided challenge type is not supported, false otherwise.
     */
    isUnsupportedChallengeType() {
        return this.isUnsupportedChallengeTypeError();
    }
    /**
     * Check if client app supports the challenge type configured in Entra.
     * @returns {boolean} True if "loginPopup" function is required to continue sthe operation.
     */
    isRedirectRequired() {
        return this.isRedirectError();
    }
}
class SignUpSubmitPasswordError extends AuthActionErrorBase {
    /**
     * Checks if the error is due to the password being invalid or incorrect.
     * @returns {boolean} True if the error is due to the password being invalid, false otherwise.
     */
    isInvalidPassword() {
        return (this.isPasswordIncorrectError() || this.isInvalidNewPasswordError());
    }
    /**
     * Check if client app supports the challenge type configured in Entra.
     * @returns {boolean} True if "loginPopup" function is required to continue sthe operation.
     */
    isRedirectRequired() {
        return this.isRedirectError();
    }
}
class SignUpSubmitCodeError extends AuthActionErrorBase {
    /**
     * Checks if the provided code is invalid.
     * @returns {boolean} True if the provided code is invalid, false otherwise.
     */
    isInvalidCode() {
        return this.isInvalidCodeError();
    }
    /**
     * Check if client app supports the challenge type configured in Entra.
     * @returns {boolean} True if "loginPopup" function is required to continue sthe operation.
     */
    isRedirectRequired() {
        return this.isRedirectError();
    }
}
class SignUpSubmitAttributesError extends AuthActionErrorBase {
    /**
     * Checks if the error is due to the required attributes are missing.
     * @returns {boolean} True if the error is due to the required attributes are missing, false otherwise.
     */
    isMissingRequiredAttributes() {
        return this.isAttributeRequiredError();
    }
    /**
     * Checks if the error is due to the attributes validation failed.
     * @returns {boolean} True if the error is due to the attributes validation failed, false otherwise.
     */
    isAttributesValidationFailed() {
        return this.isAttributeValidationFailedError();
    }
    /**
     * Check if client app supports the challenge type configured in Entra.
     * @returns {boolean} True if "loginPopup" function is required to continue sthe operation.
     */
    isRedirectRequired() {
        return this.isRedirectError();
    }
}
class SignUpResendCodeError extends AuthActionErrorBase {
    /**
     * Check if client app supports the challenge type configured in Entra.
     * @returns {boolean} True if "loginPopup" function is required to continue sthe operation.
     */
    isRedirectRequired() {
        return this.isRedirectError();
    }
}

export { SignUpError, SignUpResendCodeError, SignUpSubmitAttributesError, SignUpSubmitCodeError, SignUpSubmitPasswordError };
//# sourceMappingURL=SignUpError.mjs.map
