{"name": "readable-stream", "version": "2.1.5", "description": "Streams3, a user-land copy of the stream library from Node.js", "main": "readable.js", "dependencies": {"buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}, "devDependencies": {"assert": "~1.4.0", "babel-polyfill": "^6.9.1", "nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0"}, "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov"}, "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream"}, "keywords": ["readable", "stream", "pipe"], "browser": {"util": false}, "nyc": {"include": ["lib/**.js"]}, "license": "MIT"}