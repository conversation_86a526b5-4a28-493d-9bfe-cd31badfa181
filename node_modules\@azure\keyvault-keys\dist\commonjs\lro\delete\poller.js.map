{"version": 3, "file": "poller.js", "sourceRoot": "", "sources": ["../../../../src/lro/delete/poller.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAGlC,iDAAwD;AAGxD,kEAA4D;AAE5D;;GAEG;AACH,MAAa,eAAgB,SAAQ,wCAA0D;IAC7F,YAAY,OAAiC;QAC3C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,YAAY,GAAG,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAEpF,IAAI,KAA8C,CAAC;QAEnD,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;QACvC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,qCAAsB,iCAErC,KAAK,KACR,IAAI,KAEN,MAAM,EACN,gBAAgB,CACjB,CAAC;QAEF,KAAK,CAAC,SAAS,CAAC,CAAC;QAEjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;CACF;AAvBD,0CAuBC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { DeleteKeyPollOperationState } from \"./operation.js\";\nimport { DeleteKeyPollOperation } from \"./operation.js\";\nimport type { DeletedKey } from \"../../keysModels.js\";\nimport type { KeyVaultKeyPollerOptions } from \"../keyVaultKeyPoller.js\";\nimport { KeyVaultKeyPoller } from \"../keyVaultKeyPoller.js\";\n\n/**\n * Class that creates a poller that waits until a key finishes being deleted.\n */\nexport class DeleteKeyPoller extends KeyVaultKeyPoller<DeleteKeyPollOperationState, DeletedKey> {\n  constructor(options: KeyVaultKeyPollerOptions) {\n    const { client, name, operationOptions, intervalInMs = 2000, resumeFrom } = options;\n\n    let state: DeleteKeyPollOperationState | undefined;\n\n    if (resumeFrom) {\n      state = JSON.parse(resumeFrom).state;\n    }\n\n    const operation = new DeleteKeyPollOperation(\n      {\n        ...state,\n        name,\n      },\n      client,\n      operationOptions,\n    );\n\n    super(operation);\n\n    this.intervalInMs = intervalInMs;\n  }\n}\n"]}