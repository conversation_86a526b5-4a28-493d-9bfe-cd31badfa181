{"version": 3, "file": "msalBrowserCommon.js", "sourceRoot": "", "sources": ["../../../../src/msal/browserFlows/msalBrowserCommon.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,WAAW,MAAM,qBAAqB,CAAC;AAGnD,OAAO,EACL,qBAAqB,EACrB,oBAAoB,EACpB,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,eAAe,EACf,YAAY,EACZ,YAAY,GACb,MAAM,aAAa,CAAC;AAIrB,OAAO,EAAE,2BAA2B,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AAE1F,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EACL,yBAAyB,EACzB,mCAAmC,EACnC,eAAe,GAChB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,uCAAuC;AACvC,gDAAgD;AAChD,MAAM,iBAAiB,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC;AAErF;;;GAGG;AACH,SAAS,gCAAgC,CACvC,OAA+B;;IAE/B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,eAAe,CAAC;IACrD,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;IAChE,OAAO;QACL,IAAI,EAAE;YACJ,QAAQ,EAAE,OAAO,CAAC,QAAS;YAC3B,SAAS;YACT,gBAAgB,EAAE,mBAAmB,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,wBAAwB,CAAC;YAC5F,qDAAqD;YACrD,yCAAyC;YACzC,kEAAkE;YAClE,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;SAC3F;QACD,KAAK,EAAE;YACL,aAAa,EAAE,gBAAgB;YAC/B,sBAAsB,EAAE,IAAI,EAAE,0DAA0D;SACzF;QACD,MAAM,EAAE;YACN,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC;gBAChE,QAAQ,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC;gBACxC,iBAAiB,EAAE,MAAA,OAAO,CAAC,cAAc,0CAAE,0BAA0B;aACtE;SACF;KACF,CAAC;AACJ,CAAC;AAWD,uCAAuC;AACvC,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;AAExE;;;;GAIG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAA+B;;IACrE,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IACtC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,0BAA0B,CAAC,qCAAqC,CAAC,CAAC;IAC9E,CAAC;IACD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC9B,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC7E,MAAM,4BAA4B,GAAa,mCAAmC,CAChF,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,sBAAsB,0CAAE,0BAA0B,CAC5D,CAAC;IACF,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;IAC5C,MAAM,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,CAAC;IAC7D,MAAM,8BAA8B,GAAG,OAAO,CAAC,8BAA8B,CAAC;IAC9E,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IAEpC,IAAI,OAAyC,CAAC;IAC9C,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;QACjC,OAAO,mCACF,OAAO,CAAC,oBAAoB,KAC/B,QAAQ,GACT,CAAC;IACJ,CAAC;IAED,sEAAsE;IACtE,IAAI,GAAyC,CAAC;IAC9C;;;OAGG;IACH,KAAK,UAAU,MAAM;QACnB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,+BAA+B;YAC/B,GAAG,GAAG,MAAM,WAAW,CAAC,uBAAuB,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;YAE1F,sDAAsD;YACtD,IAAI,OAAO,EAAE,CAAC;gBACZ,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,KAAK,UAAU,mBAAmB,CAChC,MAAyC;QAEzC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,MAAM,EAAE,CAAC;YAC/B,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;gBAC1D,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACzC,OAAO,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,CAAC;QACD,OAAO;IACT,CAAC;IAED;;;;OAIG;IACH,SAAS,YAAY,CACnB,MAAyB,EACzB,MAAmB,EACnB,eAAiC;;QAEjC,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE,CAAC;YACpB,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QACD,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5C,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,WAAW;YACzB,kBAAkB,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE;YAC9C,qBAAqB,EAAE,MAAA,MAAM,CAAC,SAAS,0CAAE,OAAO,EAAE;YAClD,SAAS,EAAE,QAAQ;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,UAAU,cAAc;QAC3B,MAAM,OAAO,GAAG,MAAM,MAAM,EAAE,CAAC;QAC/B,OAAO,mBAAmB,CAAC,CAAC,MAAM,OAAO,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACH,KAAK,UAAU,gBAAgB;QAC7B,MAAM,OAAO,GAAG,MAAM,MAAM,EAAE,CAAC;QAC/B,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,UAAU,KAAK,CAAC,SAA4B,EAAE;QACjD,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC9D,MAAM,YAAY,GAAgC;YAChD,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,SAAS;SACrB,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,MAAM,EAAE,CAAC;QAC/B,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,UAAU,CAAC,CAAC,CAAC;gBAChB,MAAM,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACtC,OAAO;YACT,CAAC;YACD,KAAK,OAAO;gBACV,OAAO,mBAAmB,CAAC,MAAM,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,UAAU,cAAc,CAC3B,MAAgB,EAChB,eAA+C;QAE/C,MAAM,aAAa,GAAG,MAAM,gBAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,2BAA2B,CAAC;gBACpC,MAAM;gBACN,eAAe;gBACf,OAAO,EACL,sFAAsF;aACzF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAA8B;YAC5C,SAAS,EAAE,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,SAAS,KAAI,UAAU,CAAC,IAAI,CAAC,SAAU;YACnE,aAAa,EAAE,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,aAAa;YAC7C,MAAM,EAAE,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM;YAC/B,OAAO,EAAE,YAAY,CAAC,aAAa,CAAC;YACpC,YAAY,EAAE,KAAK;YACnB,MAAM;SACP,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACpD,MAAM,OAAO,GAAG,MAAM,MAAM,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC9D,OAAO,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,UAAU,mBAAmB,CAChC,MAAgB,EAChB,eAA+C;QAE/C,MAAM,aAAa,GAAG,MAAM,gBAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,2BAA2B,CAAC;gBACpC,MAAM;gBACN,eAAe;gBACf,OAAO,EACL,sFAAsF;aACzF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAgC;YAC9C,SAAS,EAAE,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,SAAS,KAAI,UAAU,CAAC,IAAI,CAAC,SAAU;YACnE,aAAa,EAAE,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,aAAa;YAC7C,MAAM,EAAE,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM;YAC/B,OAAO,EAAE,YAAY,CAAC,aAAa,CAAC;YACpC,SAAS,EAAE,SAAS;YACpB,MAAM;SACP,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,MAAM,EAAE,CAAC;QAC/B,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,UAAU;gBACb,gCAAgC;gBAChC,8DAA8D;gBAC9D,kDAAkD;gBAElD,MAAM,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBAC/C,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;YACnE,KAAK,OAAO;gBACV,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,UAAU,QAAQ,CACrB,MAAgB,EAChB,kBAAiD,EAAE;QAEnD,MAAM,gBAAgB,GACpB,yBAAyB,CAAC,QAAQ,EAAE,eAAe,EAAE,4BAA4B,CAAC;YAClF,QAAQ,CAAC;QAEX,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;YAC/B,eAAe,CAAC,SAAS,GAAG,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAC5E,CAAC;QAED,uDAAuD;QACvD,MAAM,cAAc,EAAE,CAAC;QAEvB,IAAI,CAAC,CAAC,MAAM,gBAAgB,EAAE,CAAC,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACnE,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;QAED,8EAA8E;QAC9E,IAAI,CAAC;YACH,OAAO,MAAM,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAAE,CAAC;gBAC/C,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,IAAI,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,8BAA8B,EAAE,CAAC;gBACpD,MAAM,IAAI,2BAA2B,CAAC;oBACpC,MAAM;oBACN,eAAe;oBACf,OAAO,EACL,qFAAqF;iBACxF,CAAC,CAAC;YACL,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,oEAAoE,UAAU,EAAE,CAAC,CAAC;YAC9F,OAAO,mBAAmB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IACD,OAAO;QACL,gBAAgB;QAChB,QAAQ;KACT,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as msal<PERSON>rowser from \"@azure/msal-browser\";\n\nimport type { MsalBrowserFlowOptions } from \"./msalBrowserOptions.js\";\nimport {\n  defaultLoggerCallback,\n  ensureValidMsalToken,\n  getAuthority,\n  getKnownAuthorities,\n  getMSALLogLevel,\n  handleMsalError,\n  msalToPublic,\n  publicToMsal,\n} from \"../utils.js\";\n\nimport type { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport type { AuthenticationRecord, MsalResult } from \"../types.js\";\nimport { AuthenticationRequiredError, CredentialUnavailableError } from \"../../errors.js\";\nimport type { CredentialFlowGetTokenOptions } from \"../credentials.js\";\nimport { getLogLevel } from \"@azure/logger\";\nimport { formatSuccess } from \"../../util/logging.js\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n  resolveTenantId,\n} from \"../../util/tenantIdUtils.js\";\nimport { DefaultTenantId } from \"../../constants.js\";\n\n// We keep a copy of the redirect hash.\n// Check if self and location object is defined.\nconst isLocationDefined = typeof self !== \"undefined\" && self.location !== undefined;\n\n/**\n * Generates a MSAL configuration that generally works for browsers\n * @internal\n */\nfunction generateMsalBrowserConfiguration(\n  options: MsalBrowserFlowOptions,\n): msalBrowser.Configuration {\n  const tenantId = options.tenantId || DefaultTenantId;\n  const authority = getAuthority(tenantId, options.authorityHost);\n  return {\n    auth: {\n      clientId: options.clientId!,\n      authority,\n      knownAuthorities: getKnownAuthorities(tenantId, authority, options.disableInstanceDiscovery),\n      // If the users picked redirect as their login style,\n      // but they didn't provide a redirectUri,\n      // we can try to use the current page we're in as a default value.\n      redirectUri: options.redirectUri || (isLocationDefined ? self.location.origin : undefined),\n    },\n    cache: {\n      cacheLocation: \"sessionStorage\",\n      storeAuthStateInCookie: true, // Set to true to improve the experience on IE11 and Edge.\n    },\n    system: {\n      loggerOptions: {\n        loggerCallback: defaultLoggerCallback(options.logger, \"Browser\"),\n        logLevel: getMSALLogLevel(getLogLevel()),\n        piiLoggingEnabled: options.loggingOptions?.enableUnsafeSupportLogging,\n      },\n    },\n  };\n}\n\n/**\n * Methods that are used by InteractiveBrowserCredential\n * @internal\n */\nexport interface MsalBrowserClient {\n  getActiveAccount(): Promise<AuthenticationRecord | undefined>;\n  getToken(scopes: string[], options: CredentialFlowGetTokenOptions): Promise<AccessToken>;\n}\n\n// We keep a copy of the redirect hash.\nconst redirectHash = isLocationDefined ? self.location.hash : undefined;\n\n/**\n * Uses MSAL Browser 2.X for browser authentication,\n * which uses the [Auth Code Flow](https://learn.microsoft.com/azure/active-directory/develop/v2-oauth2-auth-code-flow).\n * @internal\n */\nexport function createMsalBrowserClient(options: MsalBrowserFlowOptions): MsalBrowserClient {\n  const loginStyle = options.loginStyle;\n  if (!options.clientId) {\n    throw new CredentialUnavailableError(\"A client ID is required in browsers\");\n  }\n  const clientId = options.clientId;\n  const logger = options.logger;\n  const tenantId = resolveTenantId(logger, options.tenantId, options.clientId);\n  const additionallyAllowedTenantIds: string[] = resolveAdditionallyAllowedTenantIds(\n    options?.tokenCredentialOptions?.additionallyAllowedTenants,\n  );\n  const authorityHost = options.authorityHost;\n  const msalConfig = generateMsalBrowserConfiguration(options);\n  const disableAutomaticAuthentication = options.disableAutomaticAuthentication;\n  const loginHint = options.loginHint;\n\n  let account: AuthenticationRecord | undefined;\n  if (options.authenticationRecord) {\n    account = {\n      ...options.authenticationRecord,\n      tenantId,\n    };\n  }\n\n  // This variable should only be used through calling `getApp` function\n  let app: msalBrowser.IPublicClientApplication;\n  /**\n   * Return the MSAL account if not set yet\n   * @returns MSAL application\n   */\n  async function getApp(): Promise<msalBrowser.IPublicClientApplication> {\n    if (!app) {\n      // Prepare the MSAL application\n      app = await msalBrowser.PublicClientApplication.createPublicClientApplication(msalConfig);\n\n      // setting the account right after the app is created.\n      if (account) {\n        app.setActiveAccount(publicToMsal(account));\n      }\n    }\n\n    return app;\n  }\n\n  /**\n   * Loads the account based on the result of the authentication.\n   * If no result was received, tries to load the account from the cache.\n   * @param result - Result object received from MSAL.\n   */\n  async function handleBrowserResult(\n    result?: msalBrowser.AuthenticationResult,\n  ): Promise<AuthenticationRecord | undefined> {\n    try {\n      const msalApp = await getApp();\n      if (result && result.account) {\n        logger.info(`MSAL Browser V2 authentication successful.`);\n        msalApp.setActiveAccount(result.account);\n        return msalToPublic(clientId, result.account);\n      }\n    } catch (e: any) {\n      logger.info(`Failed to acquire token through MSAL. ${e.message}`);\n    }\n    return;\n  }\n\n  /**\n   * Handles the MSAL authentication result.\n   * If the result has an account, we update the local account reference.\n   * If the token received is invalid, an error will be thrown depending on what's missing.\n   */\n  function handleResult(\n    scopes: string | string[],\n    result?: MsalResult,\n    getTokenOptions?: GetTokenOptions,\n  ): AccessToken {\n    if (result?.account) {\n      account = msalToPublic(clientId, result.account);\n    }\n    ensureValidMsalToken(scopes, result, getTokenOptions);\n    logger.getToken.info(formatSuccess(scopes));\n    return {\n      token: result.accessToken,\n      expiresOnTimestamp: result.expiresOn.getTime(),\n      refreshAfterTimestamp: result.refreshOn?.getTime(),\n      tokenType: \"Bearer\",\n    };\n  }\n\n  /**\n   * Uses MSAL to handle the redirect.\n   */\n  async function handleRedirect(): Promise<AuthenticationRecord | undefined> {\n    const msalApp = await getApp();\n    return handleBrowserResult((await msalApp.handleRedirectPromise(redirectHash)) || undefined);\n  }\n\n  /**\n   * Uses MSAL to retrieve the active account.\n   */\n  async function getActiveAccount(): Promise<AuthenticationRecord | undefined> {\n    const msalApp = await getApp();\n    const activeAccount = msalApp.getActiveAccount();\n    if (!activeAccount) {\n      return;\n    }\n    return msalToPublic(clientId, activeAccount);\n  }\n\n  /**\n   * Uses MSAL to trigger a redirect or a popup login.\n   */\n  async function login(scopes: string | string[] = []): Promise<AuthenticationRecord | undefined> {\n    const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n    const loginRequest: msalBrowser.RedirectRequest = {\n      scopes: arrayScopes,\n      loginHint: loginHint,\n    };\n    const msalApp = await getApp();\n    switch (loginStyle) {\n      case \"redirect\": {\n        await app.loginRedirect(loginRequest);\n        return;\n      }\n      case \"popup\":\n        return handleBrowserResult(await msalApp.loginPopup(loginRequest));\n    }\n  }\n\n  /**\n   * Tries to retrieve the token silently using MSAL.\n   */\n  async function getTokenSilent(\n    scopes: string[],\n    getTokenOptions?: CredentialFlowGetTokenOptions,\n  ): Promise<AccessToken> {\n    const activeAccount = await getActiveAccount();\n    if (!activeAccount) {\n      throw new AuthenticationRequiredError({\n        scopes,\n        getTokenOptions,\n        message:\n          \"Silent authentication failed. We couldn't retrieve an active account from the cache.\",\n      });\n    }\n\n    const parameters: msalBrowser.SilentRequest = {\n      authority: getTokenOptions?.authority || msalConfig.auth.authority!,\n      correlationId: getTokenOptions?.correlationId,\n      claims: getTokenOptions?.claims,\n      account: publicToMsal(activeAccount),\n      forceRefresh: false,\n      scopes,\n    };\n\n    try {\n      logger.info(\"Attempting to acquire token silently\");\n      const msalApp = await getApp();\n      const response = await msalApp.acquireTokenSilent(parameters);\n      return handleResult(scopes, response);\n    } catch (err: any) {\n      throw handleMsalError(scopes, err, options);\n    }\n  }\n\n  /**\n   * Attempts to retrieve the token in the browser through interactive methods.\n   */\n  async function getTokenInteractive(\n    scopes: string[],\n    getTokenOptions?: CredentialFlowGetTokenOptions,\n  ): Promise<AccessToken> {\n    const activeAccount = await getActiveAccount();\n    if (!activeAccount) {\n      throw new AuthenticationRequiredError({\n        scopes,\n        getTokenOptions,\n        message:\n          \"Silent authentication failed. We couldn't retrieve an active account from the cache.\",\n      });\n    }\n\n    const parameters: msalBrowser.RedirectRequest = {\n      authority: getTokenOptions?.authority || msalConfig.auth.authority!,\n      correlationId: getTokenOptions?.correlationId,\n      claims: getTokenOptions?.claims,\n      account: publicToMsal(activeAccount),\n      loginHint: loginHint,\n      scopes,\n    };\n    const msalApp = await getApp();\n    switch (loginStyle) {\n      case \"redirect\":\n        // This will go out of the page.\n        // Once the InteractiveBrowserCredential is initialized again,\n        // we'll load the MSAL account in the constructor.\n\n        await msalApp.acquireTokenRedirect(parameters);\n        return { token: \"\", expiresOnTimestamp: 0, tokenType: \"Bearer\" };\n      case \"popup\":\n        return handleResult(scopes, await app.acquireTokenPopup(parameters));\n    }\n  }\n\n  /**\n   * Attempts to get token through the silent flow.\n   * If failed, get token through interactive method with `doGetToken` method.\n   */\n  async function getToken(\n    scopes: string[],\n    getTokenOptions: CredentialFlowGetTokenOptions = {},\n  ): Promise<AccessToken> {\n    const getTokenTenantId =\n      processMultiTenantRequest(tenantId, getTokenOptions, additionallyAllowedTenantIds) ||\n      tenantId;\n\n    if (!getTokenOptions.authority) {\n      getTokenOptions.authority = getAuthority(getTokenTenantId, authorityHost);\n    }\n\n    // We ensure that redirection is handled at this point.\n    await handleRedirect();\n\n    if (!(await getActiveAccount()) && !disableAutomaticAuthentication) {\n      await login(scopes);\n    }\n\n    // Attempts to get the token silently; else, falls back to interactive method.\n    try {\n      return await getTokenSilent(scopes, getTokenOptions);\n    } catch (err: any) {\n      if (err.name !== \"AuthenticationRequiredError\") {\n        throw err;\n      }\n      if (getTokenOptions?.disableAutomaticAuthentication) {\n        throw new AuthenticationRequiredError({\n          scopes,\n          getTokenOptions,\n          message:\n            \"Automatic authentication has been disabled. You may call the authenticate() method.\",\n        });\n      }\n      logger.info(`Silent authentication failed, falling back to interactive method ${loginStyle}`);\n      return getTokenInteractive(scopes, getTokenOptions);\n    }\n  }\n  return {\n    getActiveAccount,\n    getToken,\n  };\n}\n"]}