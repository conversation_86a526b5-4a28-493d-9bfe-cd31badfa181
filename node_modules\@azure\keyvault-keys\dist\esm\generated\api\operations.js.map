{"version": 3, "file": "operations.js", "sourceRoot": "", "sources": ["../../../../src/generated/api/operations.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAEL,6BAA6B,EAE7B,qBAAqB,EACrB,yBAAyB,EAEzB,6BAA6B,EAE7B,4BAA4B,EAE5B,6BAA6B,EAE7B,0BAA0B,EAG1B,2BAA2B,EAE3B,8BAA8B,EAE9B,iCAAiC,EAEjC,8BAA8B,EAE9B,2BAA2B,EAE3B,6BAA6B,EAE7B,2BAA2B,EAE3B,8BAA8B,EAE9B,4BAA4B,EAE5B,iCAAiC,EAGjC,2BAA2B,EAC3B,6BAA6B,EAE7B,+BAA+B,EAE/B,uBAAuB,GACxB,MAAM,qBAAqB,CAAC;AA4B7B,OAAO,EAEL,uBAAuB,GACxB,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAGL,eAAe,EACf,mCAAmC,GACpC,MAAM,yBAAyB,CAAC;AAEjC,MAAM,UAAU,sBAAsB,CACpC,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA2C,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEjE,MAAM,IAAI,GAAG,iBAAiB,CAC5B,4DAA4D,EAC5D;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,6BAA6B,CACjD,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,0IAA0I;AAC1I,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA2C,EAAE,cAAc,EAAE,EAAE,EAAE;IAEjE,MAAM,MAAM,GAAG,MAAM,sBAAsB,CACzC,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,6BAA6B,CAAC,MAAM,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,OAAe,EACf,UAAiC,EACjC,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAE9D,MAAM,IAAI,GAAG,iBAAiB,CAC5B,sBAAsB,EACtB;QACE,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,+BAA+B,CAAC,UAAU,CAAC,IACjD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,qFAAqF;AACrF,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,OAAe,EACf,UAAiC,EACjC,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;IAE9D,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACvE,OAAO,0BAA0B,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,OAAe,EACf,OAAe,EACf,iBAAoC,EACpC,UAAiD,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEvE,MAAM,IAAI,GAAG,iBAAiB,CAC5B,iDAAiD,EACjD;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,2BAA2B,CAAC,iBAAiB,CAAC,IACpD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mCAAmC,CACvD,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpD,CAAC;AAED,8HAA8H;AAC9H,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,OAAe,EACf,OAAe,EACf,iBAAoC,EACpC,UAAiD,EAAE,cAAc,EAAE,EAAE,EAAE;IAEvE,MAAM,MAAM,GAAG,MAAM,4BAA4B,CAC/C,OAAO,EACP,OAAO,EACP,iBAAiB,EACjB,OAAO,CACR,CAAC;IACF,OAAO,mCAAmC,CAAC,MAAM,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,OAAe,EACf,OAAe,EACf,UAA8C,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEpE,MAAM,IAAI,GAAG,iBAAiB,CAC5B,iDAAiD,EACjD;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gCAAgC,CACpD,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpD,CAAC;AAED,iKAAiK;AACjK,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,OAAe,EACf,OAAe,EACf,UAA8C,EAAE,cAAc,EAAE,EAAE,EAAE;IAEpE,MAAM,MAAM,GAAG,MAAM,yBAAyB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1E,OAAO,gCAAgC,CAAC,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,OAAe,EACf,OAAe,EACf,UAA2C,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEjE,MAAM,IAAI,GAAG,iBAAiB,CAC5B,iDAAiD,EACjD;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,6BAA6B,CACjD,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,+WAA+W;AAC/W,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,OAAe,EACf,UAA2C,EAAE,cAAc,EAAE,EAAE,EAAE;IAEjE,MAAM,MAAM,GAAG,MAAM,sBAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvE,OAAO,6BAA6B,CAAC,MAAM,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,OAAe,EACf,OAAe,EACf,UAAyC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAE/D,MAAM,IAAI,GAAG,iBAAiB,CAC5B,yCAAyC,EACzC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,MAAM,iCACF,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,2BAA2B,CAC/C,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO;AACT,CAAC;AAED,+PAA+P;AAC/P,MAAM,CAAC,KAAK,UAAU,eAAe,CACnC,OAAe,EACf,OAAe,EACf,UAAyC,EAAE,cAAc,EAAE,EAAE,EAAE;IAE/D,MAAM,MAAM,GAAG,MAAM,oBAAoB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACrE,OAAO,2BAA2B,CAAC,MAAM,CAAC,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,OAAe,EACf,OAAe,EACf,UAAuC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAE7D,MAAM,IAAI,GAAG,iBAAiB,CAC5B,yCAAyC,EACzC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAC7C,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,CAAC;AAED,2PAA2P;AAC3P,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,OAAe,EACf,OAAe,EACf,UAAuC,EAAE,cAAc,EAAE,EAAE,EAAE;IAE7D,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACnE,OAAO,yBAAyB,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,OAAe,EACf,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAE9D,MAAM,IAAI,GAAG,iBAAiB,CAC5B,yCAAyC,EACzC;QACE,eAAe,EAAE,OAAO,CAAC,UAAU;QACnC,UAAU,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU;KAChC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,iCAAiC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxD,CAAC;AAED,gbAAgb;AAChb,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;IAE9D,OAAO,uBAAuB,CAC5B,OAAO,EACP,GAAG,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,EAC3C,0BAA0B,EAC1B,CAAC,KAAK,CAAC,EACP,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,CAChD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAgC,EAChC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEvD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,wDAAwD,EACxD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,8BAA8B,CAAC,UAAU,CAAC,IAChD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,CAAC;AAED,+JAA+J;AAC/J,MAAM,CAAC,KAAK,UAAU,OAAO,CAC3B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAgC,EAChC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEvD,MAAM,MAAM,GAAG,MAAM,YAAY,CAC/B,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,0DAA0D,EAC1D;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,iCAAiC,CAAC,UAAU,CAAC,IACnD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,CAAC;AAED,yVAAyV;AACzV,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CACjC,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEvD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,wDAAwD,EACxD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,iCAAiC,CAAC,UAAU,CAAC,IACnD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,CAAC;AAED,0hBAA0hB;AAC1hB,MAAM,CAAC,KAAK,UAAU,OAAO,CAC3B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEvD,MAAM,MAAM,GAAG,MAAM,YAAY,CAC/B,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,MAAM,UAAU,WAAW,CACzB,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA+B,EAC/B,UAAgC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEtD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,uDAAuD,EACvD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,6BAA6B,CAAC,UAAU,CAAC,IAC/C,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,8aAA8a;AAC9a,MAAM,CAAC,KAAK,UAAU,MAAM,CAC1B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA+B,EAC/B,UAAgC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEtD,MAAM,MAAM,GAAG,MAAM,WAAW,CAC9B,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,SAAS,CACvB,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA6B,EAC7B,UAA8B,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEpD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,qDAAqD,EACrD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,2BAA2B,CAAC,UAAU,CAAC,IAC7C,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,CAAC;AAED,8MAA8M;AAC9M,MAAM,CAAC,KAAK,UAAU,IAAI,CACxB,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA6B,EAC7B,UAA8B,EAAE,cAAc,EAAE,EAAE,EAAE;IAEpD,MAAM,MAAM,GAAG,MAAM,SAAS,CAC5B,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEvD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,wDAAwD,EACxD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,iCAAiC,CAAC,UAAU,CAAC,IACnD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,CAAC;AAED,+uBAA+uB;AAC/uB,MAAM,CAAC,KAAK,UAAU,OAAO,CAC3B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEvD,MAAM,MAAM,GAAG,MAAM,YAAY,CAC/B,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEvD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,wDAAwD,EACxD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,iCAAiC,CAAC,UAAU,CAAC,IACnD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,CAAC;AAED,sqBAAsqB;AACtqB,MAAM,CAAC,KAAK,UAAU,OAAO,CAC3B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEvD,MAAM,MAAM,GAAG,MAAM,YAAY,CAC/B,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,OAAe,EACf,UAAgC,EAChC,UAAoC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAE1D,MAAM,IAAI,GAAG,iBAAiB,CAC5B,+BAA+B,EAC/B;QACE,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,8BAA8B,CAAC,UAAU,CAAC,IAChD,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAC1C,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,45BAA45B;AAC55B,MAAM,CAAC,KAAK,UAAU,UAAU,CAC9B,OAAe,EACf,UAAgC,EAChC,UAAoC,EAAE,cAAc,EAAE,EAAE,EAAE;IAE1D,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACnE,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,yCAAyC,EACzC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,o7BAAo7B;AACp7B,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/D,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,OAAe,EACf,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEvD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,kCAAkC,EAClC;QACE,eAAe,EAAE,OAAO,CAAC,UAAU;QACnC,UAAU,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU;KAChC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC;AAED,wXAAwX;AACxX,MAAM,UAAU,OAAO,CACrB,OAAe,EACf,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEvD,OAAO,uBAAuB,CAC5B,OAAO,EACP,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,EACpC,mBAAmB,EACnB,CAAC,KAAK,CAAC,EACP,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,CAChD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,OAAe,EACf,OAAe,EACf,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAE9D,MAAM,IAAI,GAAG,iBAAiB,CAC5B,sDAAsD,EACtD;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;QACnC,UAAU,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU;KAChC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC;AAED,oIAAoI;AACpI,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;IAE9D,OAAO,uBAAuB,CAC5B,OAAO,EACP,GAAG,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EACpD,0BAA0B,EAC1B,CAAC,KAAK,CAAC,EACP,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,CAChD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,WAAW,CACzB,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAgC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEtD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,gDAAgD,EAChD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,kMAAkM;AAClM,MAAM,CAAC,KAAK,UAAU,MAAM,CAC1B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAAgC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEtD,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACxE,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,gDAAgD,EAChD;QACE,UAAU,EAAE,OAAO;QACnB,aAAa,EAAE,UAAU;QACzB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,KAAK,iCACD,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,6BAA6B,CAAC,UAAU,CAAC,IAC/C,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,+MAA+M;AAC/M,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CACjC,OAAO,EACP,OAAO,EACP,UAAU,EACV,UAAU,EACV,OAAO,CACR,CAAC;IACF,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,kCAAkC,EAClC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,MAAM,iCACF,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,CAAC;AAED,mTAAmT;AACnT,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/D,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,kCAAkC,EAClC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,iCACC,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,6BAA6B,CAAC,UAAU,CAAC,IAC/C,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,kOAAkO;AAClO,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3E,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,yCAAyC,EACzC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAEpC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,yGAAyG;AACzG,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/D,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;;IAEzD,MAAM,IAAI,GAAG,iBAAiB,CAC5B,yCAAyC,EACzC;QACE,UAAU,EAAE,OAAO;QACnB,eAAe,EAAE,OAAO,CAAC,UAAU;KACpC,EACD;QACE,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,eAAe;KACxD,CACF,CAAC;IACF,OAAO,OAAO;SACX,IAAI,CAAC,IAAI,CAAC;SACV,IAAI,iCACA,mCAAmC,CAAC,OAAO,CAAC,KAC/C,WAAW,EAAE,kBAAkB,EAC/B,OAAO,kBACL,MAAM,EAAE,kBAAkB,IACvB,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,GAEpC,IAAI,EAAE,6BAA6B,CAAC,UAAU,CAAC,IAC/C,CAAC;AACP,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B;IAE7B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,iNAAiN;AACjN,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,OAAe,EACf,OAAe,EACf,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;IAEzD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3E,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { KeyVaultContext as Client } from \"./index.js\";\nimport {\n  KeyCreateParameters,\n  keyCreateParametersSerializer,\n  KeyBundle,\n  keyBundleDeserializer,\n  keyVaultErrorDeserializer,\n  KeyImportParameters,\n  keyImportParametersSerializer,\n  DeletedKeyBundle,\n  deletedKeyBundleDeserializer,\n  KeyUpdateParameters,\n  keyUpdateParametersSerializer,\n  _KeyListResult,\n  _keyListResultDeserializer,\n  KeyItem,\n  BackupKeyResult,\n  backupKeyResultDeserializer,\n  KeyRestoreParameters,\n  keyRestoreParametersSerializer,\n  KeyOperationsParameters,\n  keyOperationsParametersSerializer,\n  KeyOperationResult,\n  keyOperationResultDeserializer,\n  KeySignParameters,\n  keySignParametersSerializer,\n  KeyVerifyParameters,\n  keyVerifyParametersSerializer,\n  KeyVerifyResult,\n  keyVerifyResultDeserializer,\n  KeyReleaseParameters,\n  keyReleaseParametersSerializer,\n  KeyReleaseResult,\n  keyReleaseResultDeserializer,\n  _DeletedKeyListResult,\n  _deletedKeyListResultDeserializer,\n  DeletedKeyItem,\n  KeyRotationPolicy,\n  keyRotationPolicySerializer,\n  keyRotationPolicyDeserializer,\n  GetRandomBytesRequest,\n  getRandomBytesRequestSerializer,\n  RandomBytes,\n  randomBytesDeserializer,\n} from \"../models/models.js\";\nimport {\n  GetKeyAttestationOptionalParams,\n  GetRandomBytesOptionalParams,\n  UpdateKeyRotationPolicyOptionalParams,\n  GetKeyRotationPolicyOptionalParams,\n  RecoverDeletedKeyOptionalParams,\n  PurgeDeletedKeyOptionalParams,\n  GetDeletedKeyOptionalParams,\n  GetDeletedKeysOptionalParams,\n  ReleaseOptionalParams,\n  UnwrapKeyOptionalParams,\n  WrapKeyOptionalParams,\n  VerifyOptionalParams,\n  SignOptionalParams,\n  DecryptOptionalParams,\n  EncryptOptionalParams,\n  RestoreKeyOptionalParams,\n  BackupKeyOptionalParams,\n  GetKeysOptionalParams,\n  GetKeyVersionsOptionalParams,\n  GetKeyOptionalParams,\n  UpdateKeyOptionalParams,\n  DeleteKeyOptionalParams,\n  ImportKeyOptionalParams,\n  RotateKeyOptionalParams,\n  CreateKeyOptionalParams,\n} from \"./options.js\";\nimport {\n  PagedAsyncIterableIterator,\n  buildPagedAsyncIterator,\n} from \"../static-helpers/pagingHelpers.js\";\nimport { expandUrlTemplate } from \"../static-helpers/urlTemplate.js\";\nimport {\n  StreamableMethod,\n  PathUncheckedResponse,\n  createRestError,\n  operationOptionsToRequestParameters,\n} from \"@azure-rest/core-client\";\n\nexport function _getKeyAttestationSend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  options: GetKeyAttestationOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/attestation{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getKeyAttestationDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** The get key attestation operation returns the key along with its attestation blob. This operation requires the keys/get permission. */\nexport async function getKeyAttestation(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  options: GetKeyAttestationOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _getKeyAttestationSend(\n    context,\n    keyName,\n    keyVersion,\n    options,\n  );\n  return _getKeyAttestationDeserialize(result);\n}\n\nexport function _getRandomBytesSend(\n  context: Client,\n  parameters: GetRandomBytesRequest,\n  options: GetRandomBytesOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/rng{?api%2Dversion}\",\n    {\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: getRandomBytesRequestSerializer(parameters),\n    });\n}\n\nexport async function _getRandomBytesDeserialize(\n  result: PathUncheckedResponse,\n): Promise<RandomBytes> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return randomBytesDeserializer(result.body);\n}\n\n/** Get the requested number of bytes containing random values from a managed HSM. */\nexport async function getRandomBytes(\n  context: Client,\n  parameters: GetRandomBytesRequest,\n  options: GetRandomBytesOptionalParams = { requestOptions: {} },\n): Promise<RandomBytes> {\n  const result = await _getRandomBytesSend(context, parameters, options);\n  return _getRandomBytesDeserialize(result);\n}\n\nexport function _updateKeyRotationPolicySend(\n  context: Client,\n  keyName: string,\n  keyRotationPolicy: KeyRotationPolicy,\n  options: UpdateKeyRotationPolicyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/rotationpolicy{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .put({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyRotationPolicySerializer(keyRotationPolicy),\n    });\n}\n\nexport async function _updateKeyRotationPolicyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyRotationPolicy> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyRotationPolicyDeserializer(result.body);\n}\n\n/** Set specified members in the key policy. Leave others as undefined. This operation requires the keys/update permission. */\nexport async function updateKeyRotationPolicy(\n  context: Client,\n  keyName: string,\n  keyRotationPolicy: KeyRotationPolicy,\n  options: UpdateKeyRotationPolicyOptionalParams = { requestOptions: {} },\n): Promise<KeyRotationPolicy> {\n  const result = await _updateKeyRotationPolicySend(\n    context,\n    keyName,\n    keyRotationPolicy,\n    options,\n  );\n  return _updateKeyRotationPolicyDeserialize(result);\n}\n\nexport function _getKeyRotationPolicySend(\n  context: Client,\n  keyName: string,\n  options: GetKeyRotationPolicyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/rotationpolicy{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getKeyRotationPolicyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyRotationPolicy> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyRotationPolicyDeserializer(result.body);\n}\n\n/** The GetKeyRotationPolicy operation returns the specified key policy resources in the specified key vault. This operation requires the keys/get permission. */\nexport async function getKeyRotationPolicy(\n  context: Client,\n  keyName: string,\n  options: GetKeyRotationPolicyOptionalParams = { requestOptions: {} },\n): Promise<KeyRotationPolicy> {\n  const result = await _getKeyRotationPolicySend(context, keyName, options);\n  return _getKeyRotationPolicyDeserialize(result);\n}\n\nexport function _recoverDeletedKeySend(\n  context: Client,\n  keyName: string,\n  options: RecoverDeletedKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/deletedkeys/{key-name}/recover{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _recoverDeletedKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** The Recover Deleted Key operation is applicable for deleted keys in soft-delete enabled vaults. It recovers the deleted key back to its latest version under /keys. An attempt to recover an non-deleted key will return an error. Consider this the inverse of the delete operation on soft-delete enabled vaults. This operation requires the keys/recover permission. */\nexport async function recoverDeletedKey(\n  context: Client,\n  keyName: string,\n  options: RecoverDeletedKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _recoverDeletedKeySend(context, keyName, options);\n  return _recoverDeletedKeyDeserialize(result);\n}\n\nexport function _purgeDeletedKeySend(\n  context: Client,\n  keyName: string,\n  options: PurgeDeletedKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/deletedkeys/{key-name}{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .delete({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _purgeDeletedKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<void> {\n  const expectedStatuses = [\"204\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return;\n}\n\n/** The Purge Deleted Key operation is applicable for soft-delete enabled vaults. While the operation can be invoked on any vault, it will return an error if invoked on a non soft-delete enabled vault. This operation requires the keys/purge permission. */\nexport async function purgeDeletedKey(\n  context: Client,\n  keyName: string,\n  options: PurgeDeletedKeyOptionalParams = { requestOptions: {} },\n): Promise<void> {\n  const result = await _purgeDeletedKeySend(context, keyName, options);\n  return _purgeDeletedKeyDeserialize(result);\n}\n\nexport function _getDeletedKeySend(\n  context: Client,\n  keyName: string,\n  options: GetDeletedKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/deletedkeys/{key-name}{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getDeletedKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<DeletedKeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return deletedKeyBundleDeserializer(result.body);\n}\n\n/** The Get Deleted Key operation is applicable for soft-delete enabled vaults. While the operation can be invoked on any vault, it will return an error if invoked on a non soft-delete enabled vault. This operation requires the keys/get permission. */\nexport async function getDeletedKey(\n  context: Client,\n  keyName: string,\n  options: GetDeletedKeyOptionalParams = { requestOptions: {} },\n): Promise<DeletedKeyBundle> {\n  const result = await _getDeletedKeySend(context, keyName, options);\n  return _getDeletedKeyDeserialize(result);\n}\n\nexport function _getDeletedKeysSend(\n  context: Client,\n  options: GetDeletedKeysOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/deletedkeys{?api%2Dversion,maxresults}\",\n    {\n      \"api%2Dversion\": context.apiVersion,\n      maxresults: options?.maxresults,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getDeletedKeysDeserialize(\n  result: PathUncheckedResponse,\n): Promise<_DeletedKeyListResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return _deletedKeyListResultDeserializer(result.body);\n}\n\n/** Retrieves a list of the keys in the Key Vault as JSON Web Key structures that contain the public part of a deleted key. This operation includes deletion-specific information. The Get Deleted Keys operation is applicable for vaults enabled for soft-delete. While the operation can be invoked on any vault, it will return an error if invoked on a non soft-delete enabled vault. This operation requires the keys/list permission. */\nexport function getDeletedKeys(\n  context: Client,\n  options: GetDeletedKeysOptionalParams = { requestOptions: {} },\n): PagedAsyncIterableIterator<DeletedKeyItem> {\n  return buildPagedAsyncIterator(\n    context,\n    () => _getDeletedKeysSend(context, options),\n    _getDeletedKeysDeserialize,\n    [\"200\"],\n    { itemName: \"value\", nextLinkName: \"nextLink\" },\n  );\n}\n\nexport function _releaseSend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyReleaseParameters,\n  options: ReleaseOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/release{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyReleaseParametersSerializer(parameters),\n    });\n}\n\nexport async function _releaseDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyReleaseResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyReleaseResultDeserializer(result.body);\n}\n\n/** The release key operation is applicable to all key types. The target key must be marked exportable. This operation requires the keys/release permission. */\nexport async function release(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyReleaseParameters,\n  options: ReleaseOptionalParams = { requestOptions: {} },\n): Promise<KeyReleaseResult> {\n  const result = await _releaseSend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _releaseDeserialize(result);\n}\n\nexport function _unwrapKeySend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: UnwrapKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/unwrapkey{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyOperationsParametersSerializer(parameters),\n    });\n}\n\nexport async function _unwrapKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyOperationResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyOperationResultDeserializer(result.body);\n}\n\n/** The UNWRAP operation supports decryption of a symmetric key using the target key encryption key. This operation is the reverse of the WRAP operation. The UNWRAP operation applies to asymmetric and symmetric keys stored in Azure Key Vault since it uses the private portion of the key. This operation requires the keys/unwrapKey permission. */\nexport async function unwrapKey(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: UnwrapKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyOperationResult> {\n  const result = await _unwrapKeySend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _unwrapKeyDeserialize(result);\n}\n\nexport function _wrapKeySend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: WrapKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/wrapkey{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyOperationsParametersSerializer(parameters),\n    });\n}\n\nexport async function _wrapKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyOperationResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyOperationResultDeserializer(result.body);\n}\n\n/** The WRAP operation supports encryption of a symmetric key using a key encryption key that has previously been stored in an Azure Key Vault. The WRAP operation is only strictly necessary for symmetric keys stored in Azure Key Vault since protection with an asymmetric key can be performed using the public portion of the key. This operation is supported for asymmetric keys as a convenience for callers that have a key-reference but do not have access to the public key material. This operation requires the keys/wrapKey permission. */\nexport async function wrapKey(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: WrapKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyOperationResult> {\n  const result = await _wrapKeySend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _wrapKeyDeserialize(result);\n}\n\nexport function _verifySend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyVerifyParameters,\n  options: VerifyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/verify{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyVerifyParametersSerializer(parameters),\n    });\n}\n\nexport async function _verifyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyVerifyResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyVerifyResultDeserializer(result.body);\n}\n\n/** The VERIFY operation is applicable to symmetric keys stored in Azure Key Vault. VERIFY is not strictly necessary for asymmetric keys stored in Azure Key Vault since signature verification can be performed using the public portion of the key but this operation is supported as a convenience for callers that only have a key-reference and not the public portion of the key. This operation requires the keys/verify permission. */\nexport async function verify(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyVerifyParameters,\n  options: VerifyOptionalParams = { requestOptions: {} },\n): Promise<KeyVerifyResult> {\n  const result = await _verifySend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _verifyDeserialize(result);\n}\n\nexport function _signSend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeySignParameters,\n  options: SignOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/sign{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keySignParametersSerializer(parameters),\n    });\n}\n\nexport async function _signDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyOperationResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyOperationResultDeserializer(result.body);\n}\n\n/** The SIGN operation is applicable to asymmetric and symmetric keys stored in Azure Key Vault since this operation uses the private portion of the key. This operation requires the keys/sign permission. */\nexport async function sign(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeySignParameters,\n  options: SignOptionalParams = { requestOptions: {} },\n): Promise<KeyOperationResult> {\n  const result = await _signSend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _signDeserialize(result);\n}\n\nexport function _decryptSend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: DecryptOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/decrypt{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyOperationsParametersSerializer(parameters),\n    });\n}\n\nexport async function _decryptDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyOperationResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyOperationResultDeserializer(result.body);\n}\n\n/** The DECRYPT operation decrypts a well-formed block of ciphertext using the target encryption key and specified algorithm. This operation is the reverse of the ENCRYPT operation; only a single block of data may be decrypted, the size of this block is dependent on the target key and the algorithm to be used. The DECRYPT operation applies to asymmetric and symmetric keys stored in Azure Key Vault since it uses the private portion of the key. This operation requires the keys/decrypt permission. Microsoft recommends not to use CBC algorithms for decryption without first ensuring the integrity of the ciphertext using an HMAC, for example. See https://learn.microsoft.com/dotnet/standard/security/vulnerabilities-cbc-mode for more information. */\nexport async function decrypt(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: DecryptOptionalParams = { requestOptions: {} },\n): Promise<KeyOperationResult> {\n  const result = await _decryptSend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _decryptDeserialize(result);\n}\n\nexport function _encryptSend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: EncryptOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}/encrypt{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyOperationsParametersSerializer(parameters),\n    });\n}\n\nexport async function _encryptDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyOperationResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyOperationResultDeserializer(result.body);\n}\n\n/** The ENCRYPT operation encrypts an arbitrary sequence of bytes using an encryption key that is stored in Azure Key Vault. Note that the ENCRYPT operation only supports a single block of data, the size of which is dependent on the target key and the encryption algorithm to be used. The ENCRYPT operation is only strictly necessary for symmetric keys stored in Azure Key Vault since protection with an asymmetric key can be performed using public portion of the key. This operation is supported for asymmetric keys as a convenience for callers that have a key-reference but do not have access to the public key material. This operation requires the keys/encrypt permission. */\nexport async function encrypt(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyOperationsParameters,\n  options: EncryptOptionalParams = { requestOptions: {} },\n): Promise<KeyOperationResult> {\n  const result = await _encryptSend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _encryptDeserialize(result);\n}\n\nexport function _restoreKeySend(\n  context: Client,\n  parameters: KeyRestoreParameters,\n  options: RestoreKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/restore{?api%2Dversion}\",\n    {\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyRestoreParametersSerializer(parameters),\n    });\n}\n\nexport async function _restoreKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** Imports a previously backed up key into Azure Key Vault, restoring the key, its key identifier, attributes and access control policies. The RESTORE operation may be used to import a previously backed up key. Individual versions of a key cannot be restored. The key is restored in its entirety with the same key name as it had when it was backed up. If the key name is not available in the target Key Vault, the RESTORE operation will be rejected. While the key name is retained during restore, the final key identifier will change if the key is restored to a different vault. Restore will restore all versions and preserve version identifiers. The RESTORE operation is subject to security constraints: The target Key Vault must be owned by the same Microsoft Azure Subscription as the source Key Vault The user must have RESTORE permission in the target Key Vault. This operation requires the keys/restore permission. */\nexport async function restoreKey(\n  context: Client,\n  parameters: KeyRestoreParameters,\n  options: RestoreKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _restoreKeySend(context, parameters, options);\n  return _restoreKeyDeserialize(result);\n}\n\nexport function _backupKeySend(\n  context: Client,\n  keyName: string,\n  options: BackupKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/backup{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _backupKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<BackupKeyResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return backupKeyResultDeserializer(result.body);\n}\n\n/** The Key Backup operation exports a key from Azure Key Vault in a protected form. Note that this operation does NOT return key material in a form that can be used outside the Azure Key Vault system, the returned key material is either protected to a Azure Key Vault HSM or to Azure Key Vault itself. The intent of this operation is to allow a client to GENERATE a key in one Azure Key Vault instance, BACKUP the key, and then RESTORE it into another Azure Key Vault instance. The BACKUP operation may be used to export, in protected form, any key type from Azure Key Vault. Individual versions of a key cannot be backed up. BACKUP / RESTORE can be performed within geographical boundaries only; meaning that a BACKUP from one geographical area cannot be restored to another geographical area. For example, a backup from the US geographical area cannot be restored in an EU geographical area. This operation requires the key/backup permission. */\nexport async function backupKey(\n  context: Client,\n  keyName: string,\n  options: BackupKeyOptionalParams = { requestOptions: {} },\n): Promise<BackupKeyResult> {\n  const result = await _backupKeySend(context, keyName, options);\n  return _backupKeyDeserialize(result);\n}\n\nexport function _getKeysSend(\n  context: Client,\n  options: GetKeysOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys{?api%2Dversion,maxresults}\",\n    {\n      \"api%2Dversion\": context.apiVersion,\n      maxresults: options?.maxresults,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getKeysDeserialize(\n  result: PathUncheckedResponse,\n): Promise<_KeyListResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return _keyListResultDeserializer(result.body);\n}\n\n/** Retrieves a list of the keys in the Key Vault as JSON Web Key structures that contain the public part of a stored key. The LIST operation is applicable to all key types, however only the base key identifier, attributes, and tags are provided in the response. Individual versions of a key are not listed in the response. This operation requires the keys/list permission. */\nexport function getKeys(\n  context: Client,\n  options: GetKeysOptionalParams = { requestOptions: {} },\n): PagedAsyncIterableIterator<KeyItem> {\n  return buildPagedAsyncIterator(\n    context,\n    () => _getKeysSend(context, options),\n    _getKeysDeserialize,\n    [\"200\"],\n    { itemName: \"value\", nextLinkName: \"nextLink\" },\n  );\n}\n\nexport function _getKeyVersionsSend(\n  context: Client,\n  keyName: string,\n  options: GetKeyVersionsOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/versions{?api%2Dversion,maxresults}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n      maxresults: options?.maxresults,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getKeyVersionsDeserialize(\n  result: PathUncheckedResponse,\n): Promise<_KeyListResult> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return _keyListResultDeserializer(result.body);\n}\n\n/** The full key identifier, attributes, and tags are provided in the response. This operation requires the keys/list permission. */\nexport function getKeyVersions(\n  context: Client,\n  keyName: string,\n  options: GetKeyVersionsOptionalParams = { requestOptions: {} },\n): PagedAsyncIterableIterator<KeyItem> {\n  return buildPagedAsyncIterator(\n    context,\n    () => _getKeyVersionsSend(context, keyName, options),\n    _getKeyVersionsDeserialize,\n    [\"200\"],\n    { itemName: \"value\", nextLinkName: \"nextLink\" },\n  );\n}\n\nexport function _getKeySend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  options: GetKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .get({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _getKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** The get key operation is applicable to all key types. If the requested key is symmetric, then no key material is released in the response. This operation requires the keys/get permission. */\nexport async function getKey(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  options: GetKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _getKeySend(context, keyName, keyVersion, options);\n  return _getKeyDeserialize(result);\n}\n\nexport function _updateKeySend(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyUpdateParameters,\n  options: UpdateKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/{key-version}{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"key-version\": keyVersion,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .patch({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyUpdateParametersSerializer(parameters),\n    });\n}\n\nexport async function _updateKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** In order to perform this operation, the key must already exist in the Key Vault. Note: The cryptographic material of a key itself cannot be changed. This operation requires the keys/update permission. */\nexport async function updateKey(\n  context: Client,\n  keyName: string,\n  keyVersion: string,\n  parameters: KeyUpdateParameters,\n  options: UpdateKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _updateKeySend(\n    context,\n    keyName,\n    keyVersion,\n    parameters,\n    options,\n  );\n  return _updateKeyDeserialize(result);\n}\n\nexport function _deleteKeySend(\n  context: Client,\n  keyName: string,\n  options: DeleteKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .delete({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _deleteKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<DeletedKeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return deletedKeyBundleDeserializer(result.body);\n}\n\n/** The delete key operation cannot be used to remove individual versions of a key. This operation removes the cryptographic material associated with the key, which means the key is not usable for Sign/Verify, Wrap/Unwrap or Encrypt/Decrypt operations. This operation requires the keys/delete permission. */\nexport async function deleteKey(\n  context: Client,\n  keyName: string,\n  options: DeleteKeyOptionalParams = { requestOptions: {} },\n): Promise<DeletedKeyBundle> {\n  const result = await _deleteKeySend(context, keyName, options);\n  return _deleteKeyDeserialize(result);\n}\n\nexport function _importKeySend(\n  context: Client,\n  keyName: string,\n  parameters: KeyImportParameters,\n  options: ImportKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .put({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyImportParametersSerializer(parameters),\n    });\n}\n\nexport async function _importKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** The import key operation may be used to import any key type into an Azure Key Vault. If the named key already exists, Azure Key Vault creates a new version of the key. This operation requires the keys/import permission. */\nexport async function importKey(\n  context: Client,\n  keyName: string,\n  parameters: KeyImportParameters,\n  options: ImportKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _importKeySend(context, keyName, parameters, options);\n  return _importKeyDeserialize(result);\n}\n\nexport function _rotateKeySend(\n  context: Client,\n  keyName: string,\n  options: RotateKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/rotate{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n    });\n}\n\nexport async function _rotateKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** The operation will rotate the key based on the key policy. It requires the keys/rotate permission. */\nexport async function rotateKey(\n  context: Client,\n  keyName: string,\n  options: RotateKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _rotateKeySend(context, keyName, options);\n  return _rotateKeyDeserialize(result);\n}\n\nexport function _createKeySend(\n  context: Client,\n  keyName: string,\n  parameters: KeyCreateParameters,\n  options: CreateKeyOptionalParams = { requestOptions: {} },\n): StreamableMethod {\n  const path = expandUrlTemplate(\n    \"/keys/{key-name}/create{?api%2Dversion}\",\n    {\n      \"key-name\": keyName,\n      \"api%2Dversion\": context.apiVersion,\n    },\n    {\n      allowReserved: options?.requestOptions?.skipUrlEncoding,\n    },\n  );\n  return context\n    .path(path)\n    .post({\n      ...operationOptionsToRequestParameters(options),\n      contentType: \"application/json\",\n      headers: {\n        accept: \"application/json\",\n        ...options.requestOptions?.headers,\n      },\n      body: keyCreateParametersSerializer(parameters),\n    });\n}\n\nexport async function _createKeyDeserialize(\n  result: PathUncheckedResponse,\n): Promise<KeyBundle> {\n  const expectedStatuses = [\"200\"];\n  if (!expectedStatuses.includes(result.status)) {\n    const error = createRestError(result);\n    error.details = keyVaultErrorDeserializer(result.body);\n    throw error;\n  }\n\n  return keyBundleDeserializer(result.body);\n}\n\n/** The create key operation can be used to create any key type in Azure Key Vault. If the named key already exists, Azure Key Vault creates a new version of the key. It requires the keys/create permission. */\nexport async function createKey(\n  context: Client,\n  keyName: string,\n  parameters: KeyCreateParameters,\n  options: CreateKeyOptionalParams = { requestOptions: {} },\n): Promise<KeyBundle> {\n  const result = await _createKeySend(context, keyName, parameters, options);\n  return _createKeyDeserialize(result);\n}\n"]}