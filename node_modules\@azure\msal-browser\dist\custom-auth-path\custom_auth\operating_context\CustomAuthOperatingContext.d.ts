import { BaseOperatingContext } from "../../operatingcontext/BaseOperatingContext.js";
import { CustomAuthBrowserConfiguration, CustomAuthConfiguration } from "../configuration/CustomAuthConfiguration.js";
export declare class CustomAuthOperatingContext extends BaseOperatingContext {
    private readonly customAuthOptions;
    private static readonly MODULE_NAME;
    private static readonly ID;
    constructor(configuration: CustomAuthConfiguration);
    getModuleName(): string;
    getId(): string;
    getCustomAuthConfig(): CustomAuthBrowserConfiguration;
    initialize(): Promise<boolean>;
}
//# sourceMappingURL=CustomAuthOperatingContext.d.ts.map