{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../../../src/generated/api/options.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { OperationOptions } from \"@azure-rest/core-client\";\n\n/** Optional parameters. */\nexport interface GetKeyAttestationOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface GetRandomBytesOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface UpdateKeyRotationPolicyOptionalParams\n  extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface GetKeyRotationPolicyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface RecoverDeletedKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface PurgeDeletedKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface GetDeletedKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface GetDeletedKeysOptionalParams extends OperationOptions {\n  /** Maximum number of results to return in a page. If not specified the service will return up to 25 results. */\n  maxresults?: number;\n}\n\n/** Optional parameters. */\nexport interface ReleaseOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface UnwrapKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface WrapKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface VerifyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface SignOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface DecryptOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface EncryptOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface RestoreKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface BackupKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface GetKeysOptionalParams extends OperationOptions {\n  /** Maximum number of results to return in a page. If not specified the service will return up to 25 results. */\n  maxresults?: number;\n}\n\n/** Optional parameters. */\nexport interface GetKeyVersionsOptionalParams extends OperationOptions {\n  /** Maximum number of results to return in a page. If not specified the service will return up to 25 results. */\n  maxresults?: number;\n}\n\n/** Optional parameters. */\nexport interface GetKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface UpdateKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface DeleteKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface ImportKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface RotateKeyOptionalParams extends OperationOptions {}\n\n/** Optional parameters. */\nexport interface CreateKeyOptionalParams extends OperationOptions {}\n"]}