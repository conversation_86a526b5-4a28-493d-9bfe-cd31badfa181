{"version": 3, "file": "ThrottlingUtils.mjs", "sources": ["../../src/network/ThrottlingUtils.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAkBH;MACa,eAAe,CAAA;AACxB;;;AAGG;IACH,OAAO,4BAA4B,CAAC,UAA6B,EAAA;AAC7D,QAAA,OAAO,CAAG,EAAA,mBAAmB,CAAC,iBAAiB,CAAI,CAAA,EAAA,IAAI,CAAC,SAAS,CAC7D,UAAU,CACb,CAAA,CAAE,CAAC;KACP;AAED;;;;AAIG;AACH,IAAA,OAAO,UAAU,CACb,YAA0B,EAC1B,UAA6B,EAC7B,aAAqB,EAAA;QAErB,MAAM,GAAG,GAAG,eAAe,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QACrE,MAAM,KAAK,GAAG,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAEnD,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE;AACjC,gBAAA,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBAC5C,OAAO;AACV,aAAA;YACD,MAAM,IAAI,WAAW,CACjB,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,YAAY,EACrD,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,QAAQ,CACjB,CAAC;AACL,SAAA;KACJ;AAED;;;;;AAKG;IACH,OAAO,WAAW,CACd,YAA0B,EAC1B,UAA6B,EAC7B,QAA2D,EAC3D,aAAqB,EAAA;AAErB,QAAA,IACI,eAAe,CAAC,mBAAmB,CAAC,QAAQ,CAAC;AAC7C,YAAA,eAAe,CAAC,0BAA0B,CAAC,QAAQ,CAAC,EACtD;AACE,YAAA,MAAM,eAAe,GAAqB;AACtC,gBAAA,YAAY,EAAE,eAAe,CAAC,qBAAqB,CAC/C,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CACtD;AACD,gBAAA,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;AAC1B,gBAAA,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW;AACrC,gBAAA,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;AAC7C,gBAAA,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;aACnC,CAAC;AACF,YAAA,YAAY,CAAC,kBAAkB,CAC3B,eAAe,CAAC,4BAA4B,CAAC,UAAU,CAAC,EACxD,eAAe,EACf,aAAa,CAChB,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;IACH,OAAO,mBAAmB,CACtB,QAA2D,EAAA;AAE3D,QAAA,QACI,QAAQ,CAAC,MAAM,KAAK,GAAG;AACvB,aAAC,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC,EACnD;KACL;AAED;;;AAGG;IACH,OAAO,0BAA0B,CAC7B,QAA2D,EAAA;QAE3D,IAAI,QAAQ,CAAC,OAAO,EAAE;YAClB,QACI,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC;AACxD,iBAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,EACnD;AACL,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED;;;AAGG;IACH,OAAO,qBAAqB,CAAC,YAAoB,EAAA;AAC7C,QAAA,MAAM,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QAElD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QACzC,OAAO,IAAI,CAAC,KAAK,CACb,IAAI,CAAC,GAAG,CACJ,cAAc;AACV,aAAC,IAAI,IAAI,mBAAmB,CAAC,6BAA6B,CAAC,EAC/D,cAAc;AACV,YAAA,mBAAmB,CAAC,iCAAiC,CAC5D,GAAG,IAAI,CACX,CAAC;KACL;IAED,OAAO,cAAc,CACjB,YAA0B,EAC1B,QAAgB,EAChB,OAAwB,EACxB,qBAA8B,EAAA;QAE9B,MAAM,UAAU,GAAG,oBAAoB,CACnC,QAAQ,EACR,OAAO,EACP,qBAAqB,CACxB,CAAC;QACF,MAAM,GAAG,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QAC1D,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;KACvD;AACJ;;;;"}