{"version": 3, "file": "clientCertificateCredentialModels.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientCertificateCredentialModels.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with the string contents of a PEM certificate\n */\nexport interface ClientCertificatePEMCertificate {\n  /**\n   * The PEM-encoded public/private key certificate on the filesystem.\n   * Ensure that certificate is in PEM format and contains both the public and private keys.\n   */\n  certificate: string;\n\n  /**\n   * The password for the certificate file.\n   */\n  certificatePassword?: string;\n}\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with the path to a PEM certificate.\n */\nexport interface ClientCertificatePEMCertificatePath {\n  /**\n   * The path to the PEM-encoded public/private key certificate on the filesystem.\n   */\n  certificatePath: string;\n\n  /**\n   * The password for the certificate file.\n   */\n  certificatePassword?: string;\n}\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with either the string contents of a PEM certificate, or the path to a PEM certificate.\n */\nexport type ClientCertificateCredentialPEMConfiguration =\n  | ClientCertificatePEMCertificate\n  | ClientCertificatePEMCertificatePath;\n"]}