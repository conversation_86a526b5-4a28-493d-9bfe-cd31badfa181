{"version": 3, "file": "keysModels.js", "sourceRoot": "", "sources": ["../../src/keysModels.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAelC;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,KAAK,CAAC;AAulBxC,qEAAqE;AACrE,MAAM,CAAN,IAAY,kBAeX;AAfD,WAAY,kBAAkB;IAC5B,8BAA8B;IAC9B,yCAAmB,CAAA;IACnB,8BAA8B;IAC9B,yCAAmB,CAAA;IACnB,2BAA2B;IAC3B,mCAAa,CAAA;IACb,6BAA6B;IAC7B,uCAAiB,CAAA;IACjB,8BAA8B;IAC9B,yCAAmB,CAAA;IACnB,gCAAgC;IAChC,6CAAuB,CAAA;IACvB,6BAA6B;IAC7B,uCAAiB,CAAA;AACnB,CAAC,EAfW,kBAAkB,KAAlB,kBAAkB,QAe7B", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type * as coreClient from \"@azure-rest/core-client\";\nimport type { ExtendedCommonClientOptions } from \"@azure/core-http-compat\";\n\nimport type { DeletionRecoveryLevel } from \"./generated/models/index.js\";\nimport {\n  JsonWebKeyOperation as KeyOperation,\n  JsonWebKeyType as KeyType,\n} from \"./generated/models/index.js\";\n\nimport type { KeyCurveName } from \"./cryptographyClientModels.js\";\n\nexport { KeyType, KeyOperation };\n\n/**\n * The latest supported Key Vault service API version\n */\nexport const LATEST_API_VERSION = \"7.6\";\n\n/**\n * The optional parameters accepted by the KeyVault's KeyClient\n */\nexport interface KeyClientOptions extends ExtendedCommonClientOptions {\n  /**\n   * The version of the KeyVault's service API to make calls against.\n   */\n  serviceVersion?: string;\n\n  /**\n   * Whether to disable verification that the authentication challenge resource matches the Key Vault or Managed HSM domain.\n   * Defaults to false.\n   */\n  disableChallengeResourceVerification?: boolean;\n}\n\n/**\n * The optional parameters accepted by the KeyVault's CryptographyClient\n */\nexport interface CryptographyClientOptions extends KeyClientOptions {}\n\n/**\n * As of http://tools.ietf.org/html/draft-ietf-jose-json-web-key-18\n */\nexport interface JsonWebKey {\n  /**\n   * Key identifier.\n   */\n  kid?: string;\n  /**\n   * JsonWebKey Key Type (kty), as defined in\n   * https://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-40. Possible values include:\n   * 'EC', 'EC-HSM', 'RSA', 'RSA-HSM', 'oct', \"oct-HSM\"\n   */\n  kty?: KeyType;\n  /**\n   * Json web key operations. For more\n   * information on possible key operations, see KeyOperation.\n   */\n  keyOps?: KeyOperation[];\n  /**\n   * RSA modulus.\n   */\n  n?: Uint8Array;\n  /**\n   * RSA public exponent.\n   */\n  e?: Uint8Array;\n  /**\n   * RSA private exponent, or the D component of an EC private key.\n   */\n  d?: Uint8Array;\n  /**\n   * RSA private key parameter.\n   */\n  dp?: Uint8Array;\n  /**\n   * RSA private key parameter.\n   */\n  dq?: Uint8Array;\n  /**\n   * RSA private key parameter.\n   */\n  qi?: Uint8Array;\n  /**\n   * RSA secret prime.\n   */\n  p?: Uint8Array;\n  /**\n   * RSA secret prime, with `p < q`.\n   */\n  q?: Uint8Array;\n  /**\n   * Symmetric key.\n   */\n  k?: Uint8Array;\n  /**\n   * HSM Token, used with 'Bring Your Own Key'.\n   */\n  t?: Uint8Array;\n  /**\n   * Elliptic curve name. For valid values, see KeyCurveName. Possible values include:\n   * 'P-256', 'P-384', 'P-521', 'P-256K'\n   */\n  crv?: KeyCurveName;\n  /**\n   * X component of an EC public key.\n   */\n  x?: Uint8Array;\n  /**\n   * Y component of an EC public key.\n   */\n  y?: Uint8Array;\n}\n\n/**\n * An interface representing a Key Vault Key, with its name, value and {@link KeyProperties}.\n */\nexport interface KeyVaultKey {\n  /**\n   * The key value.\n   */\n  key?: JsonWebKey;\n  /**\n   * The name of the key.\n   */\n  name: string;\n  /**\n   * Key identifier.\n   */\n  id?: string;\n  /**\n   * JsonWebKey Key Type (kty), as defined in\n   * https://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-40. Possible values include:\n   * 'EC', 'EC-HSM', 'RSA', 'RSA-HSM', 'oct', \"oct-HSM\"\n   */\n  keyType?: KeyType;\n  /**\n   * Operations allowed on this key\n   */\n  keyOperations?: KeyOperation[];\n  /**\n   * The properties of the key.\n   */\n  properties: KeyProperties;\n}\n\n/**\n * An interface representing the properties of a key's attestation\n */\nexport interface KeyAttestation {\n  /**\n   * The certificate used for attestation validation, in PEM format.\n   */\n  certificatePemFile?: Uint8Array;\n  /**\n   * The key attestation corresponding to the private key material of the key.\n   */\n  privateKeyAttestation?: Uint8Array;\n  /**\n   * The key attestation corresponding to the public key material of the key.\n   */\n  publicKeyAttestation?: Uint8Array;\n  /**\n   * The version of the attestation.\n   */\n  version?: string;\n}\n\n/**\n * An interface representing the Properties of {@link KeyVaultKey}\n */\nexport interface KeyProperties {\n  /**\n   * Key identifier.\n   */\n  id?: string;\n  /**\n   * The name of the key.\n   */\n  name: string;\n  /**\n   * The vault URI.\n   */\n  vaultUrl: string;\n  /**\n   * The version of the key. May be undefined.\n   */\n  version?: string;\n  /**\n   * Determines whether the object is enabled.\n   */\n  enabled?: boolean;\n  /**\n   * Not before date in UTC.\n   */\n  notBefore?: Date;\n  /**\n   * Expiry date in UTC.\n   */\n  expiresOn?: Date;\n  /**\n   * Application specific metadata in the form of key-value pairs.\n   */\n  tags?: { [propertyName: string]: string };\n  /**\n   * Creation time in UTC.\n   * **NOTE: This property will not be serialized. It can only be populated by\n   * the server.**\n   */\n  readonly createdOn?: Date;\n  /**\n   * Last updated time in UTC.\n   * **NOTE: This property will not be serialized. It can only be populated by\n   * the server.**\n   */\n  readonly updatedOn?: Date;\n  /**\n   * Reflects the deletion recovery level currently in effect for keys in the current vault.\n   * If it contains 'Purgeable' the key can be permanently deleted by a privileged\n   * user; otherwise, only the system can purge the key, at the end of the\n   * retention interval. Possible values include: 'Purgeable',\n   * 'Recoverable+Purgeable', 'Recoverable',\n   * 'Recoverable+ProtectedSubscription'\n   * **NOTE: This property will not be serialized. It can only be populated by\n   * the server.**\n   */\n  readonly recoveryLevel?: DeletionRecoveryLevel;\n  /**\n   * The retention dates of the softDelete data.\n   * The value should be `>=7` and `<=90` when softDelete enabled.\n   * **NOTE: This property will not be serialized. It can only be populated by the server.**\n   */\n  recoverableDays?: number;\n\n  /**\n   * True if the secret's lifetime is managed by\n   * key vault. If this is a secret backing a certificate, then managed will be\n   * true.\n   * **NOTE: This property will not be serialized. It can only be populated by\n   * the server.**\n   */\n  readonly managed?: boolean;\n\n  /**\n   * Indicates whether the private key can be exported.\n   */\n  exportable?: boolean;\n\n  /**\n   * A {@link KeyReleasePolicy} object specifying the rules under which the key can be exported.\n   */\n  releasePolicy?: KeyReleasePolicy;\n\n  /**\n   * The underlying HSM Platform.\n   * NOTE: This property will not be serialized. It can only be populated by the server.\n   */\n  readonly hsmPlatform?: string;\n\n  /**\n   * The key attestation, if available and requested.\n   */\n  attestation?: KeyAttestation;\n}\n\n/**\n * An interface representing a deleted Key Vault Key.\n */\nexport interface DeletedKey {\n  /**\n   * The key value.\n   */\n  key?: JsonWebKey;\n  /**\n   * The name of the key.\n   */\n  name: string;\n  /**\n   * Key identifier.\n   */\n  id?: string;\n  /**\n   * JsonWebKey Key Type (kty), as defined in\n   * https://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-40. Possible values include:\n   * 'EC', 'EC-HSM', 'RSA', 'RSA-HSM', 'oct', \"oct-HSM\"\n   */\n  keyType?: KeyType;\n  /**\n   * Operations allowed on this key\n   */\n  keyOperations?: KeyOperation[];\n  /**\n   * The properties of the key.\n   */\n  properties: KeyProperties & {\n    /**\n     * The url of the recovery object, used to\n     * identify and recover the deleted key.\n     */\n    readonly recoveryId?: string;\n    /**\n     * The time when the key is scheduled to be purged, in UTC\n     * **NOTE: This property will not be serialized. It can only be populated by\n     * the server.**\n     */\n    readonly scheduledPurgeDate?: Date;\n    /**\n     * The time when the key was deleted, in UTC\n     * **NOTE: This property will not be serialized. It can only be populated by\n     * the server.**\n     */\n    deletedOn?: Date;\n  };\n}\n\n/**\n * The policy rules under which a key can be exported.\n */\nexport interface KeyReleasePolicy {\n  /**\n   * Content type and version of key release policy.\n   *\n   * Defaults to \"application/json; charset=utf-8\" if omitted.\n   */\n  contentType?: string;\n\n  /**\n   * The policy rules under which the key can be released. Encoded based on the {@link KeyReleasePolicy.contentType}.\n   *\n   * For more information regarding the release policy grammar for Azure Key Vault, please refer to:\n   * - https://aka.ms/policygrammarkeys for Azure Key Vault release policy grammar.\n   * - https://aka.ms/policygrammarmhsm for Azure Managed HSM release policy grammar.\n   */\n  encodedPolicy?: Uint8Array;\n\n  /** Marks a release policy as immutable. An immutable release policy cannot be changed or updated after being marked immutable. */\n  immutable?: boolean;\n}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link createKey}\n */\nexport interface CreateKeyOptions extends coreClient.OperationOptions {\n  /**\n   * Application specific metadata in the form of key-value pairs.\n   */\n  tags?: { [propertyName: string]: string };\n  /**\n   * Json web key operations. For more\n   * information on possible key operations, see KeyOperation.\n   */\n  keyOps?: KeyOperation[];\n  /**\n   * Determines whether the object is enabled.\n   */\n  enabled?: boolean;\n  /**\n   * Not before date in UTC.\n   */\n  notBefore?: Date;\n  /**\n   * Expiry date in UTC.\n   */\n  readonly expiresOn?: Date;\n  /**\n   * The key size in bits. For example: 2048, 3072, or 4096 for RSA.\n   */\n  keySize?: number;\n  /**\n   * Elliptic curve name. For valid values, see KeyCurveName.\n   * Possible values include: 'P-256', 'P-384', 'P-521', 'P-256K'\n   */\n  curve?: KeyCurveName;\n  /**\n   * Whether to import as a hardware key (HSM) or software key.\n   */\n  hsm?: boolean;\n\n  /**\n   * Indicates whether the private key can be exported.\n   */\n  exportable?: boolean;\n\n  /**\n   * A {@link KeyReleasePolicy} object specifying the rules under which the key can be exported.\n   */\n  releasePolicy?: KeyReleasePolicy;\n}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link beginDeleteKey} and {@link beginRecoverDeletedKey}\n */\nexport interface KeyPollerOptions extends coreClient.OperationOptions {\n  /**\n   * Time between each polling\n   */\n  intervalInMs?: number;\n  /**\n   * A serialized poller, used to resume an existing operation\n   */\n  resumeFrom?: string;\n}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link beginDeleteKey}\n */\nexport interface BeginDeleteKeyOptions extends KeyPollerOptions {}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link beginRecoverDeletedKey}\n */\nexport interface BeginRecoverDeletedKeyOptions extends KeyPollerOptions {}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link createEcKey}\n */\nexport interface CreateEcKeyOptions extends CreateKeyOptions {}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link createRsaKey}\n */\nexport interface CreateRsaKeyOptions extends CreateKeyOptions {\n  /** The public exponent for a RSA key. */\n  publicExponent?: number;\n}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link createOctKey}\n */\nexport interface CreateOctKeyOptions extends CreateKeyOptions {}\n\n/**\n * An interface representing the optional parameters that can be\n * passed to {@link importKey}\n */\nexport interface ImportKeyOptions extends coreClient.OperationOptions {\n  /**\n   * Application specific metadata in the form of key-value pairs.\n   */\n  tags?: { [propertyName: string]: string };\n  /**\n   * Whether to import as a hardware key (HSM) or software key.\n   */\n  hardwareProtected?: boolean;\n  /**\n   * Determines whether the object is enabled.\n   */\n  enabled?: boolean;\n  /**\n   * Not before date in UTC.\n   */\n  notBefore?: Date;\n  /**\n   * Expiry date in UTC.\n   */\n  expiresOn?: Date;\n\n  /**\n   * Indicates whether the private key can be exported.\n   */\n  exportable?: boolean;\n\n  /**\n   * A {@link KeyReleasePolicy} object specifying the rules under which the key can be exported.\n   */\n  releasePolicy?: KeyReleasePolicy;\n}\n\n/**\n * Options for {@link updateKeyProperties}.\n */\nexport interface UpdateKeyPropertiesOptions extends coreClient.OperationOptions {\n  /**\n   * Json web key operations. For more\n   * information on possible key operations, see KeyOperation.\n   */\n  keyOps?: KeyOperation[];\n  /**\n   * Determines whether the object is enabled.\n   */\n  enabled?: boolean;\n  /**\n   * Not before date in UTC.\n   */\n  notBefore?: Date;\n  /**\n   * Expiry date in UTC.\n   */\n  expiresOn?: Date;\n  /**\n   * Application specific metadata in the form of key-value pairs.\n   */\n  tags?: { [propertyName: string]: string };\n\n  /**\n   * A {@link KeyReleasePolicy} object specifying the rules under which the key can be exported.\n   * Only valid if the key is marked exportable, which cannot be changed after key creation.\n   */\n  releasePolicy?: KeyReleasePolicy;\n}\n\n/**\n * Options for {@link getKey}.\n */\nexport interface GetKeyOptions extends coreClient.OperationOptions {\n  /**\n   * The version of the secret to retrieve. If not\n   * specified the latest version of the secret will be retrieved.\n   */\n  version?: string;\n}\n\n/**\n * Options for {@link getKeyAttestation}.\n */\nexport interface GetKeyAttestationOptions extends coreClient.OperationOptions {\n  /**\n   * The version of the key to retrieve the attestation for. If not\n   * specified the latest version of the key will be retrieved.\n   */\n  version?: string;\n}\n\n/**\n * An interface representing optional parameters for KeyClient paged operations passed to {@link listKeys}.\n */\nexport interface ListKeysOptions extends coreClient.OperationOptions {}\n\n/**\n * An interface representing optional parameters for KeyClient paged operations passed to {@link listPropertiesOfKeys}.\n */\nexport interface ListPropertiesOfKeysOptions extends coreClient.OperationOptions {}\n\n/**\n * An interface representing optional parameters for KeyClient paged operations passed to {@link listPropertiesOfKeyVersions}.\n */\nexport interface ListPropertiesOfKeyVersionsOptions extends coreClient.OperationOptions {}\n\n/**\n * An interface representing optional parameters for KeyClient paged operations passed to {@link listDeletedKeys}.\n */\nexport interface ListDeletedKeysOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link getDeletedKey}.\n */\nexport interface GetDeletedKeyOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link purgeDeletedKey}.\n */\nexport interface PurgeDeletedKeyOptions extends coreClient.OperationOptions {}\n\n/**\n * @internal\n * Options for {@link recoverDeletedKey}.\n */\nexport interface RecoverDeletedKeyOptions extends coreClient.OperationOptions {}\n\n/**\n * @internal\n * Options for {@link deleteKey}.\n */\nexport interface DeleteKeyOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link backupKey}.\n */\nexport interface BackupKeyOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link restoreKeyBackup}.\n */\nexport interface RestoreKeyBackupOptions extends coreClient.OperationOptions {}\n\n/**\n * An interface representing the options of the cryptography API methods, go to the {@link CryptographyClient} for more information.\n */\nexport interface CryptographyOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link KeyClient.getRandomBytes}\n */\nexport interface GetRandomBytesOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link KeyClient.releaseKey}\n */\nexport interface ReleaseKeyOptions extends coreClient.OperationOptions {\n  /** A client provided nonce for freshness. */\n  nonce?: string;\n\n  /** The {@link KeyExportEncryptionAlgorithm} to for protecting the exported key material. */\n  algorithm?: KeyExportEncryptionAlgorithm;\n\n  /**\n   * The version of the key to release. Defaults to the latest version of the key if omitted.\n   */\n  version?: string;\n}\n\n/**\n * Result of the {@link KeyClient.releaseKey} operation.\n */\nexport interface ReleaseKeyResult {\n  /** A signed token containing the released key. */\n  value: string;\n}\n\n/** Known values of {@link KeyOperation} that the service accepts. */\nexport enum KnownKeyOperations {\n  /** Key operation - encrypt */\n  Encrypt = \"encrypt\",\n  /** Key operation - decrypt */\n  Decrypt = \"decrypt\",\n  /** Key operation - sign */\n  Sign = \"sign\",\n  /** Key operation - verify */\n  Verify = \"verify\",\n  /** Key operation - wrapKey */\n  WrapKey = \"wrapKey\",\n  /** Key operation - unwrapKey */\n  UnwrapKey = \"unwrapKey\",\n  /** Key operation - import */\n  Import = \"import\",\n}\n\n/* eslint-disable tsdoc/syntax */\n/**\n * Defines values for KeyEncryptionAlgorithm.\n * {@link KnownKeyExportEncryptionAlgorithm} can be used interchangeably with KeyEncryptionAlgorithm,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **CKM_RSA_AES_KEY_WRAP** \\\n * **RSA_AES_KEY_WRAP_256** \\\n * **RSA_AES_KEY_WRAP_384**\n */\nexport type KeyExportEncryptionAlgorithm = string;\n/* eslint-enable tsdoc/syntax */\n\n/**\n * Options for {@link KeyClient.getCryptographyClient}.\n */\nexport interface GetCryptographyClientOptions {\n  /**\n   * The version of the key to use for cryptographic operations.\n   *\n   * When undefined, the latest version of the key will be used.\n   */\n  keyVersion?: string;\n}\n\n/**\n * Options for {@link KeyClient.rotateKey}\n */\nexport interface RotateKeyOptions extends coreClient.OperationOptions {}\n\n/**\n * The properties of a key rotation policy that the client can set for a given key.\n *\n * You may also reset the key rotation policy to its default values by setting lifetimeActions to an empty array.\n */\nexport interface KeyRotationPolicyProperties {\n  /**\n   * Optional key expiration period used to define the duration after which a newly rotated key will expire, defined as an ISO 8601 duration.\n   */\n  expiresIn?: string;\n\n  /**\n   * Actions that will be performed by Key Vault over the lifetime of a key.\n   *\n   * You may also pass an empty array to restore to its default values.\n   */\n  lifetimeActions?: KeyRotationLifetimeAction[];\n}\n\n/**\n * The complete key rotation policy that belongs to a key.\n */\nexport interface KeyRotationPolicy extends KeyRotationPolicyProperties {\n  /**\n   * The identifier of the Key Rotation Policy.\n   * May be undefined if a policy has not been explicitly set.\n   */\n  readonly id?: string;\n\n  /**\n   * The created time in UTC.\n   * May be undefined if a policy has not been explicitly set.\n   */\n  readonly createdOn?: Date;\n\n  /**\n   * The last updated time in UTC.\n   * May be undefined if a policy has not been explicitly set.\n   */\n  readonly updatedOn?: Date;\n}\n\n/**\n * An action and its corresponding trigger that will be performed by Key Vault over the lifetime of a key.\n */\nexport interface KeyRotationLifetimeAction {\n  /**\n   * Time after creation to attempt the specified action, defined as an ISO 8601 duration.\n   */\n  timeAfterCreate?: string;\n\n  /**\n   * Time before expiry to attempt the specified action, defined as an ISO 8601 duration.\n   */\n  timeBeforeExpiry?: string;\n\n  /**\n   * The action that will be executed.\n   */\n  action: KeyRotationPolicyAction;\n}\n\n/**\n * The action that will be executed.\n */\nexport type KeyRotationPolicyAction = \"Rotate\" | \"Notify\";\n\n/**\n * Options for {@link KeyClient.updateKeyRotationPolicy}\n */\nexport interface UpdateKeyRotationPolicyOptions extends coreClient.OperationOptions {}\n\n/**\n * Options for {@link KeyClient.getRotationPolicy}\n */\nexport interface GetKeyRotationPolicyOptions extends coreClient.OperationOptions {}\n"]}