{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/logger.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,0CAAmD;AAEtC,QAAA,MAAM,GAAG,IAAA,2BAAkB,EAAC,iBAAiB,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createClientLogger } from \"@azure/logger\";\n\nexport const logger = createClientLogger(\"keyvault-common\");\n"]}