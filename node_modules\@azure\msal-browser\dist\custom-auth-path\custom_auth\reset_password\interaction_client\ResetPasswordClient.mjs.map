{"version": 3, "file": "ResetPasswordClient.mjs", "sources": ["../../../../../../src/custom_auth/reset_password/interaction_client/ResetPasswordClient.ts"], "sourcesContent": [null], "names": ["PublicApiId.PASSWORD_RESET_START", "PublicApiId.PASSWORD_RESET_SUBMIT_CODE", "PublicApiId.PASSWORD_RESET_RESEND_CODE", "PublicApiId.PASSWORD_RESET_SUBMIT_PASSWORD", "CustomAuthApiErrorCode.UNSUPPORTED_CHALLENGE_TYPE", "CustomAuthApiErrorCode.PASSWORD_CHANGE_FAILED", "CustomAuthApiErrorCode.PASSWORD_RESET_TIMEOUT"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAiCG,MAAO,mBAAoB,SAAQ,+BAA+B,CAAA;AACpE;;;;AAIG;IACH,MAAM,KAAK,CACP,UAAoC,EAAA;AAEpC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;AAC/C,QAAA,MAAM,KAAK,GAAGA,oBAAgC,CAAC;QAC/C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,YAAY,GAA8B;YAC5C,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;YAChE,QAAQ,EAAE,UAAU,CAAC,QAAQ;AAC7B,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iDAAiD,EACjD,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,aAAa,GACf,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAExE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0DAA0D,EAC1D,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAkC;AACpD,YAAA,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,IAAI,EAAE;YAC1D,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;AAChE,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;KACzD;AAED;;;;AAIG;IACH,MAAM,UAAU,CACZ,UAAyC,EAAA;AAEzC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAC/C,8BAA8B,CAC1B,iBAAiB,EACjB,UAAU,CAAC,IAAI,EACf,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,KAAK,GAAGC,0BAAsC,CAAC;QACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,eAAe,GAAiC;YAClD,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,GAAG,EAAE,UAAU,CAAC,IAAI;AACpB,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yDAAyD,EACzD,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,gBAAgB,CAC5D,eAAe,CAClB,CAAC;QAEN,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qEAAqE,EACrE,QAAQ,CAAC,cAAc,CAC1B,CAAC;QAEF,OAAO;YACH,aAAa,EAAE,QAAQ,CAAC,cAAc;AACtC,YAAA,iBAAiB,EAAE,QAAQ,CAAC,kBAAkB,IAAI,EAAE;SACvD,CAAC;KACL;AAED;;;;AAIG;IACH,MAAM,UAAU,CACZ,UAAyC,EAAA;AAEzC,QAAA,MAAM,KAAK,GAAGC,0BAAsC,CAAC;QACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,gBAAgB,GAAkC;YACpD,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;YAChE,aAAa,EAAE,UAAU,CAAC,aAAa;AACvC,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;KACzD;AAED;;;;AAIG;IACH,MAAM,iBAAiB,CACnB,UAAgD,EAAA;AAEhD,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAE/C,8BAA8B,CAC1B,wBAAwB,EACxB,UAAU,CAAC,WAAW,EACtB,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,KAAK,GAAGC,8BAA0C,CAAC;QACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,aAAa,GAA+B;YAC9C,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,YAAY,EAAE,UAAU,CAAC,WAAW;AACpC,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,EAC/D,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,cAAc,GAChB,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,iBAAiB,CAC7D,aAAa,CAChB,CAAC;QAEN,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2EAA2E,EAC3E,aAAa,CAChB,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,4BAA4B,CACpC,cAAc,CAAC,kBAAkB,IAAI,EAAE,EACvC,cAAc,CAAC,aAAa,EAC5B,aAAa,EACb,gBAAgB,CACnB,CAAC;KACL;IAEO,MAAM,uBAAuB,CACjC,OAAsC,EAAA;AAEtC,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qDAAqD,EACrD,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,gBAAgB,CAC5D,OAAO,CACV,CAAC;QAEN,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8DAA8D,EAC9D,aAAa,CAChB,CAAC;AAEF,QAAA,IAAI,QAAQ,CAAC,cAAc,KAAK,aAAa,CAAC,GAAG,EAAE;;YAE/C,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2CAA2C,EAC3C,aAAa,CAChB,CAAC;YAEF,OAAO;gBACH,aAAa,EAAE,QAAQ,CAAC,cAAc;AACtC,gBAAA,iBAAiB,EAAE,QAAQ,CAAC,kBAAkB,IAAI,EAAE;AACpD,gBAAA,gBAAgB,EAAE,QAAQ,CAAC,iBAAiB,IAAI,EAAE;AAClD,gBAAA,oBAAoB,EAAE,QAAQ,CAAC,sBAAsB,IAAI,EAAE;AAC3D,gBAAA,UAAU,EACN,QAAQ,CAAC,WAAW,IAAI,8BAA8B;AAC1D,gBAAA,aAAa,EAAE,QAAQ,CAAC,cAAc,IAAI,EAAE;aAC/C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAA,4BAAA,EAA+B,QAAQ,CAAC,cAAc,CAAA,sDAAA,CAAwD,EAC9G,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,IAAI,kBAAkB,CACxBC,0BAAiD,EACjD,CAA+B,4BAAA,EAAA,QAAQ,CAAC,cAAc,CAAA,EAAA,CAAI,EAC1D,aAAa,CAChB,CAAC;KACL;IAEO,MAAM,4BAA4B,CACtC,iBAAyB,EACzB,YAAoB,EACpB,aAAqB,EACrB,gBAAwC,EAAA;AAExC,QAAA,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAEpC,QAAA,OACI,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS;AAC7B,YAAA,+BAA+B,EACjC;AACE,YAAA,MAAM,WAAW,GAAuC;AACpD,gBAAA,kBAAkB,EAAE,iBAAiB;AACrC,gBAAA,aAAa,EAAE,aAAa;AAC5B,gBAAA,gBAAgB,EAAE,gBAAgB;aACrC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,EAC/D,aAAa,CAChB,CAAC;AAEF,YAAA,MAAM,YAAY,GACd,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAC1D,WAAW,CACd,CAAC;YAEN,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oEAAoE,EACpE,aAAa,CAChB,CAAC;AAEF,YAAA,IAAI,YAAY,CAAC,MAAM,KAAK,uBAAuB,CAAC,SAAS,EAAE;gBAC3D,OAAO;oBACH,aAAa,EAAE,YAAY,CAAC,cAAc;AAC1C,oBAAA,iBAAiB,EAAE,YAAY,CAAC,kBAAkB,IAAI,EAAE;iBAC3D,CAAC;AACL,aAAA;AAAM,iBAAA,IAAI,YAAY,CAAC,MAAM,KAAK,uBAAuB,CAAC,MAAM,EAAE;AAC/D,gBAAA,MAAM,IAAI,kBAAkB,CACxBC,sBAA6C,EAC7C,iCAAiC,EACjC,YAAY,CAAC,cAAc,CAC9B,CAAC;AACL,aAAA;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAsF,mFAAA,EAAA,YAAY,CAA0B,wBAAA,CAAA,EAC5H,aAAa,CAChB,CAAC;YAEF,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;AACzC,SAAA;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,aAAa,CAAC,CAAC;QAEvE,MAAM,IAAI,kBAAkB,CACxBC,sBAA6C,EAC7C,oCAAoC,EACpC,aAAa,CAChB,CAAC;KACL;IAEO,MAAM,KAAK,CAAC,EAAU,EAAA;AAC1B,QAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;KAC5D;AACJ;;;;"}