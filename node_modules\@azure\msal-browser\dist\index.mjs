/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import * as BrowserUtils from './utils/BrowserUtils.mjs';
export { BrowserUtils };
export { PublicClientApplication, createNestablePublicClientApplication, createStandardPublicClientApplication } from './app/PublicClientApplication.mjs';
export { PublicClientNext } from './app/PublicClientNext.mjs';
export { DEFAULT_IFRAME_TIMEOUT_MS } from './config/Configuration.mjs';
export { ApiId, BrowserCacheLocation, CacheLookupPolicy, InteractionStatus, InteractionType, WrapperSKU } from './utils/BrowserConstants.mjs';
export { BrowserAuthError, BrowserAuthErrorMessage } from './error/BrowserAuthError.mjs';
export { BrowserConfigurationAuthError, BrowserConfigurationAuthErrorMessage } from './error/BrowserConfigurationAuthError.mjs';
export { stubbedPublicClientApplication } from './app/IPublicClientApplication.mjs';
export { NavigationClient } from './navigation/NavigationClient.mjs';
export { MemoryStorage } from './cache/MemoryStorage.mjs';
export { LocalStorage } from './cache/LocalStorage.mjs';
export { SessionStorage } from './cache/SessionStorage.mjs';
export { EventMessageUtils } from './event/EventMessage.mjs';
export { EventType } from './event/EventType.mjs';
export { EventHandler } from './event/EventHandler.mjs';
export { SignedHttpRequest } from './crypto/SignedHttpRequest.mjs';
export { BrowserPerformanceClient } from './telemetry/BrowserPerformanceClient.mjs';
export { BrowserPerformanceMeasurement } from './telemetry/BrowserPerformanceMeasurement.mjs';
export { AccountEntity, AuthError, AuthErrorCodes, AuthErrorMessage, AuthenticationHeaderParser, AuthenticationScheme, AzureCloudInstance, ClientAuthError, ClientAuthErrorCodes, ClientAuthErrorMessage, ClientConfigurationError, ClientConfigurationErrorCodes, ClientConfigurationErrorMessage, InteractionRequiredAuthError, InteractionRequiredAuthErrorCodes, InteractionRequiredAuthErrorMessage, JsonWebTokenTypes, LogLevel, Logger, OIDC_DEFAULT_SCOPES, PerformanceEvents, PromptValue, ProtocolMode, ServerError, ServerResponseType, StringUtils, StubPerformanceClient, UrlString } from '@azure/msal-common/browser';
export { version } from './packageMetadata.mjs';
export { isPlatformBrokerAvailable } from './broker/nativeBroker/PlatformAuthProvider.mjs';
import * as BrowserAuthErrorCodes from './error/BrowserAuthErrorCodes.mjs';
export { BrowserAuthErrorCodes };
import * as BrowserConfigurationAuthErrorCodes from './error/BrowserConfigurationAuthErrorCodes.mjs';
export { BrowserConfigurationAuthErrorCodes };
//# sourceMappingURL=index.mjs.map
