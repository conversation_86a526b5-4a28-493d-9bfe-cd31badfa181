/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/**
 * Controls whether tokens should be stored in the cache or not. If set to false, tokens may still be acquired and returned but will not be cached for later retrieval.
 */
export type StoreInCache = {
    /* Indicates whether or not the acquired accessToken will be stored in the cache */
    accessToken?: boolean;
    /* Indicates whether or not the acquired idToken will be stored in the cache */
    idToken?: boolean;
    /* Indicates whether or not the acquired refreshToken will be stored in the cache */
    refreshToken?: boolean;
};
