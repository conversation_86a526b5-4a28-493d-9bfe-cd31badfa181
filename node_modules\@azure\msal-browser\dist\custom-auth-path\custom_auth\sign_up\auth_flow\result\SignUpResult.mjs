/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { SignUpError } from '../error_type/SignUpError.mjs';
import { SignUpAttributesRequiredState } from '../state/SignUpAttributesRequiredState.mjs';
import { SignUpCodeRequiredState } from '../state/SignUpCodeRequiredState.mjs';
import { SignUpFailedState } from '../state/SignUpFailedState.mjs';
import { SignUpPasswordRequiredState } from '../state/SignUpPasswordRequiredState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a sign-up operation.
 */
class SignUpResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of SignUpResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of SignUpResult with an error.
     * @param error The error that occurred.
     * @returns {SignUpResult} A new instance of SignUpResult with the error set.
     */
    static createWithError(error) {
        const result = new SignUpResult(new SignUpFailedState());
        result.error = new SignUpError(SignUpResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignUpFailedState;
    }
    /**
     * Checks if the result is in a code required state.
     */
    isCodeRequired() {
        return this.state instanceof SignUpCodeRequiredState;
    }
    /**
     * Checks if the result is in a password required state.
     */
    isPasswordRequired() {
        return this.state instanceof SignUpPasswordRequiredState;
    }
    /**
     * Checks if the result is in an attributes required state.
     */
    isAttributesRequired() {
        return this.state instanceof SignUpAttributesRequiredState;
    }
}

export { SignUpResult };
//# sourceMappingURL=SignUpResult.mjs.map
