{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../src/log.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,0CAAmD;AAEnD;;GAEG;AACU,QAAA,MAAM,GAAG,IAAA,2BAAkB,EAAC,eAAe,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createClientLogger } from \"@azure/logger\";\n\n/**\n * The \\@azure/logger configuration for this package.\n */\nexport const logger = createClientLogger(\"keyvault-keys\");\n"]}