{"name": "@azure/keyvault-keys", "sdk-type": "client", "author": "Microsoft Corporation", "version": "4.10.0", "license": "MIT", "description": "Isomorphic client library for Azure KeyVault's keys.", "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/keyvault/keyvault-keys/README.md", "repository": "github:Azure/azure-sdk-for-js", "keywords": ["node", "azure", "cloud", "typescript", "browser", "isomorphic", "keyvault"], "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18.0.0"}, "files": ["dist/", "README.md", "LICENSE"], "browser": "./dist/browser/index.js", "scripts": {"build": "npm run clean && dev-tool run build-package && dev-tool run extract-api", "build:samples": "echo Obsolete.", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "clean": "dev-tool run vendored rimraf --glob dist dist-* types *.tgz *.log dist-browser statistics.html coverage && dev-tool run vendored rimraf --glob src/**/*.js && dev-tool run vendored rimraf --glob test/**/*.js", "execute:samples": "dev-tool samples run samples-dev", "extract-api": "dev-tool run build-package && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "generate:client": "node ../generate.js", "lint": "eslint package.json api-extractor.json src test", "lint:fix": "eslint package.json api-extractor.json src test --fix --fix-type [problem,suggestion]", "pack": "npm pack 2>&1", "test": "npm run test:node && npm run test:browser", "test:browser": "echo skipped", "test:node": "dev-tool run test:vitest", "test:node:esm": "dev-tool run test:vitest --no-test-proxy --esm", "update-snippets": "dev-tool run update-snippets"}, "sideEffects": false, "//metadata": {"constantPaths": [{"path": "src/generated/keyVaultClient.ts", "prefix": "packageDetails"}, {"path": "src/constants.ts", "prefix": "SDK_VERSION"}]}, "//sampleConfiguration": {"productName": "Azure Key Vault Keys", "productSlugs": ["azure", "azure-key-vault"], "requiredResources": {"Azure Key Vault": "https://learn.microsoft.com/azure/key-vault/quick-create-portal"}, "customSnippets": {"prerequisites": "samples-dev/snippets/_prerequisites.md"}}, "dependencies": {"@azure-rest/core-client": "^2.3.3", "@azure/abort-controller": "^2.1.2", "@azure/core-auth": "^1.9.0", "@azure/core-http-compat": "^2.2.0", "@azure/core-lro": "^2.7.2", "@azure/core-paging": "^1.6.2", "@azure/core-rest-pipeline": "^1.19.0", "@azure/core-tracing": "^1.2.0", "@azure/core-util": "^1.11.0", "@azure/keyvault-common": "^2.0.0", "@azure/logger": "^1.1.4", "tslib": "^2.8.1"}, "devDependencies": {"@azure-tools/test-credential": "^2.0.0", "@azure-tools/test-recorder": "^4.1.0", "@azure-tools/test-utils-vitest": "^1.0.0", "@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/identity": "^4.6.0", "@types/node": "^18.0.0", "@vitest/browser": "^3.0.9", "@vitest/coverage-istanbul": "^3.0.9", "dotenv": "^16.0.0", "eslint": "^9.9.0", "playwright": "^1.47.2", "typescript": "~5.8.2", "vitest": "^3.0.9"}, "type": "module", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser"], "selfLink": false, "project": "./tsconfig.src.json"}, "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}}