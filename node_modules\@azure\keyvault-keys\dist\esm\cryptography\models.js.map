{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../src/cryptography/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAsBlC,MAAM,OAAO,iCAAkC,SAAQ,KAAK;CAAG", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions } from \"@azure-rest/core-client\";\nimport type {\n  DecryptOptions,\n  DecryptParameters,\n  DecryptResult,\n  EncryptOptions,\n  EncryptParameters,\n  EncryptResult,\n  KeyWrapAlgorithm,\n  SignOptions,\n  SignResult,\n  SignatureAlgorithm,\n  UnwrapKeyOptions,\n  UnwrapResult,\n  VerifyOptions,\n  VerifyResult,\n  WrapKeyOptions,\n  WrapResult,\n} from \"../index.js\";\n\nexport class LocalCryptographyUnsupportedError extends Error {}\n\n/**\n * The set of operations a {@link CryptographyProvider} supports.\n *\n * This corresponds to every single method on the interface so that providers\n * can declare whether they support this method or not.\n *\n * Purposely more granular than {@link KnownKeyOperations} because some providers\n * support verifyData but not verify.\n * @internal\n */\nexport type CryptographyProviderOperation =\n  | \"encrypt\"\n  | \"decrypt\"\n  | \"wrapKey\"\n  | \"unwrapKey\"\n  | \"sign\"\n  | \"signData\"\n  | \"verify\"\n  | \"verifyData\";\n\n/**\n *\n * Represents an object that can perform cryptography operations.\n * @internal\n */\nexport interface CryptographyProvider {\n  /**\n   * Encrypts the given plaintext with the specified encryption parameters.\n   * @internal\n   *\n   * @param encryptParameters - The encryption parameters, keyed on the encryption algorithm chosen.\n   * @param options - Additional options.\n   */\n  encrypt(encryptParameters: EncryptParameters, options?: EncryptOptions): Promise<EncryptResult>;\n\n  /**\n   * Decrypts the given ciphertext with the specified decryption parameters.\n   * @internal\n   *\n   * @param decryptParameters - The decryption parameters.\n   * @param options - Additional options.\n   */\n  decrypt(decryptParameters: DecryptParameters, options?: DecryptOptions): Promise<DecryptResult>;\n\n  /**\n   *\n   * @param algorithm - The algorithm to check support for.\n   * @param operation - The {@link CryptographyProviderOperation} to check support for.\n   */\n  isSupported(algorithm: string, operation: CryptographyProviderOperation): boolean;\n\n  /**\n   * Wraps the given key using the specified cryptography algorithm\n   * @internal\n   *\n   * @param algorithm - The encryption algorithm to use to wrap the given key.\n   * @param keyToWrap - The key to wrap.\n   * @param options - Additional options.\n   */\n  wrapKey(\n    algorithm: KeyWrapAlgorithm,\n    keyToWrap: Uint8Array,\n    options?: WrapKeyOptions,\n  ): Promise<WrapResult>;\n\n  /**\n   * Unwraps the given wrapped key using the specified cryptography algorithm\n   * @internal\n   *\n   * @param algorithm - The decryption algorithm to use to unwrap the key.\n   * @param encryptedKey - The encrypted key to unwrap.\n   * @param options - Additional options.\n   */\n  unwrapKey(\n    algorithm: KeyWrapAlgorithm,\n    encryptedKey: Uint8Array,\n    options?: UnwrapKeyOptions,\n  ): Promise<UnwrapResult>;\n\n  /**\n   * Cryptographically sign the digest of a message\n   * @internal\n   *\n   * @param algorithm - The signing algorithm to use.\n   * @param digest - The digest of the data to sign.\n   * @param options - Additional options.\n   */\n  sign(\n    algorithm: SignatureAlgorithm,\n    digest: Uint8Array,\n    options?: SignOptions,\n  ): Promise<SignResult>;\n\n  /**\n   * Cryptographically sign a block of data\n   * @internal\n   *\n   * @param algorithm - The signing algorithm to use.\n   * @param data - The data to sign.\n   * @param options - Additional options.\n   */\n  signData(\n    algorithm: SignatureAlgorithm,\n    data: Uint8Array,\n    options?: SignOptions,\n  ): Promise<SignResult>;\n\n  /**\n   * Verify the signed message digest\n   * @internal\n   *\n   * @param algorithm - The signing algorithm to use to verify with.\n   * @param digest - The digest to verify.\n   * @param signature - The signature to verify the digest against.\n   * @param options - Additional options.\n   */\n  verify(\n    algorithm: SignatureAlgorithm,\n    digest: Uint8Array,\n    signature: Uint8Array,\n    options?: VerifyOptions,\n  ): Promise<VerifyResult>;\n\n  /**\n   * Verify the signed block of data\n   * @internal\n   *\n   * @param algorithm - The algorithm to use to verify with.\n   * @param data - The signed block of data to verify.\n   * @param signature - The signature to verify the block against.\n   * @param updatedOptions - Additional options.\n   */\n  verifyData(\n    algorithm: string,\n    data: Uint8Array,\n    signature: Uint8Array,\n    updatedOptions: OperationOptions,\n  ): Promise<VerifyResult>;\n}\n"]}