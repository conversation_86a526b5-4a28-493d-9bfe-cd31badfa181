{"version": 3, "file": "CacheManager.d.ts", "sourceRoot": "", "sources": ["../../../src/cache/CacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,aAAa,EACb,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,gBAAgB,EAChB,SAAS,EAEZ,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAYxD,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAK7D,OAAO,EACH,WAAW,EAGd,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAC5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAElE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE1D,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAE1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,gDAAgD,CAAC;AAIpF;;;GAGG;AACH,8BAAsB,YAAa,YAAW,aAAa;IACvD,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;IAE9B,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,sBAAsB,CAAC,CAAyB;IACxD,SAAS,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;gBAG5C,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,OAAO,EACnB,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,kBAAkB,EACrC,sBAAsB,CAAC,EAAE,sBAAsB;IASnD;;;OAGG;IACH,QAAQ,CAAC,UAAU,CACf,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,GACtB,aAAa,GAAG,IAAI;IAEvB;;;;OAIG;IACH,QAAQ,CAAC,UAAU,CACf,OAAO,EAAE,aAAa,EACtB,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAEhB;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CACzB,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,GACtB,aAAa,GAAG,IAAI;IAEvB;;;;OAIG;IACH,QAAQ,CAAC,oBAAoB,CACzB,OAAO,EAAE,aAAa,EACtB,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAEhB;;;OAGG;IACH,QAAQ,CAAC,wBAAwB,CAC7B,cAAc,EAAE,MAAM,EACtB,aAAa,EAAE,MAAM,GACtB,iBAAiB,GAAG,IAAI;IAE3B;;;;OAIG;IACH,QAAQ,CAAC,wBAAwB,CAC7B,WAAW,EAAE,iBAAiB,EAC9B,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAEhB;;;OAGG;IACH,QAAQ,CAAC,yBAAyB,CAC9B,eAAe,EAAE,MAAM,EACvB,aAAa,EAAE,MAAM,GACtB,kBAAkB,GAAG,IAAI;IAE5B;;;;OAIG;IACH,QAAQ,CAAC,yBAAyB,CAC9B,YAAY,EAAE,kBAAkB,EAChC,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAEhB;;;OAGG;IACH,QAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAEzE;;;OAGG;IACH,QAAQ,CAAC,cAAc,CACnB,WAAW,EAAE,iBAAiB,EAC9B,aAAa,EAAE,MAAM,GACtB,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,GAC3B,qBAAqB,GAAG,IAAI;IAE/B;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,qBAAqB,EACtC,aAAa,EAAE,MAAM,GACtB,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IAE1E;;OAEG;IACH,QAAQ,CAAC,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAElD;;;;OAIG;IACH,QAAQ,CAAC,oBAAoB,CACzB,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,uBAAuB,GAC/B,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,GAC3B,gBAAgB,GAAG,IAAI;IAE1B;;;;OAIG;IACH,QAAQ,CAAC,kBAAkB,CACvB,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,gBAAgB,EACjC,aAAa,EAAE,MAAM,GACtB,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAE7D;;OAEG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,EAAE;IAE5B;;OAEG;IACH,QAAQ,CAAC,cAAc,IAAI,MAAM,EAAE;IAEnC;;OAEG;IACH,QAAQ,CAAC,YAAY,IAAI,SAAS;IAElC;;;;OAIG;IACH,cAAc,CACV,aAAa,EAAE,aAAa,EAC5B,aAAa,EAAE,MAAM,GACtB,WAAW,EAAE;IAQhB;;OAEG;IACH,wBAAwB,CACpB,aAAa,EAAE,aAAa,EAC5B,aAAa,EAAE,MAAM,GACtB,WAAW,GAAG,IAAI;IAgBrB;;;;OAIG;IACH,kBAAkB,CACd,aAAa,EAAE,aAAa,EAC5B,aAAa,EAAE,MAAM,GACtB,WAAW,GAAG,IAAI;IAYrB;;;;;;OAMG;IACH,OAAO,CAAC,mBAAmB;IAe3B,OAAO,CAAC,8BAA8B;IAwDtC,OAAO,CAAC,kCAAkC;IA0C1C,OAAO,CAAC,0BAA0B;IA+BlC,OAAO,CAAC,qCAAqC;IAsD7C;;;;;OAKG;IACG,eAAe,CACjB,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,MAAM,EACrB,YAAY,CAAC,EAAE,YAAY,GAC5B,OAAO,CAAC,IAAI,CAAC;IAoDhB;;;OAGG;YACW,eAAe;IA0C7B;;;;OAIG;IACH,qBAAqB,CACjB,aAAa,EAAE,aAAa,EAC5B,aAAa,EAAE,MAAM,GACtB,aAAa,EAAE;IA2FlB;;;;;;OAMG;IACH,YAAY,CACR,GAAG,EAAE,MAAM,EACX,aAAa,CAAC,EAAE,MAAM,EACtB,QAAQ,CAAC,EAAE,MAAM,GAClB,OAAO;IAsBV;;;OAGG;IACH,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IA2CrC;;;;;OAKG;IACH,uBAAuB,CACnB,MAAM,EAAE,mBAAmB,EAC3B,MAAM,EAAE,gBAAgB,GACzB,OAAO;IAoFV;;;OAGG;IACH,wBAAwB,CAAC,MAAM,EAAE,iBAAiB,GAAG,gBAAgB;IAqCrE;;;OAGG;IACH,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IA8BzE;;OAEG;IACH,iBAAiB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI;IAQ9C;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAS9D;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAuBzE;;;;OAIG;IACH,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAmC3D;;OAEG;IACH,iBAAiB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO;IAWjD;;;OAGG;IACH,oBAAoB,CAChB,OAAO,EAAE,WAAW,EACpB,aAAa,EAAE,MAAM,GACtB,aAAa,GAAG,IAAI;IAMvB;;;;;;;OAOG;IACH,UAAU,CACN,OAAO,EAAE,WAAW,EACpB,aAAa,EAAE,MAAM,EACrB,SAAS,CAAC,EAAE,SAAS,EACrB,WAAW,CAAC,EAAE,MAAM,EACpB,iBAAiB,CAAC,EAAE,kBAAkB,GACvC,aAAa,GAAG,IAAI;IAsEvB;;;;OAIG;IACH,mBAAmB,CACf,MAAM,EAAE,gBAAgB,EACxB,aAAa,EAAE,MAAM,EACrB,SAAS,CAAC,EAAE,SAAS,GACtB,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;IA0B7B;;;;;OAKG;IACH,uBAAuB,CACnB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,GACzB,OAAO;IAmBV;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAIvD;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAI5D;;;;;;;OAOG;IACH,cAAc,CACV,OAAO,EAAE,WAAW,EACpB,OAAO,EAAE,eAAe,EACxB,SAAS,CAAC,EAAE,SAAS,EACrB,WAAW,CAAC,EAAE,MAAM,GACrB,iBAAiB,GAAG,IAAI;IAyF3B;;;;;;OAMG;IACH,2BAA2B,CACvB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,EACxB,uBAAuB,EAAE,OAAO,GACjC,OAAO;IAiDV;;;;OAIG;IACH,uBAAuB,CACnB,MAAM,EAAE,gBAAgB,EACxB,aAAa,EAAE,MAAM,GACtB,iBAAiB,EAAE;IAwBtB;;;;;;;OAOG;IACH,eAAe,CACX,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,OAAO,EACjB,aAAa,EAAE,MAAM,EACrB,SAAS,CAAC,EAAE,SAAS,EACrB,iBAAiB,CAAC,EAAE,kBAAkB,GACvC,kBAAkB,GAAG,IAAI;IA0D5B;;;;OAIG;IACH,4BAA4B,CACxB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,gBAAgB,GACzB,OAAO;IA4BV;;OAEG;IACH,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAwBvE;;;;OAIG;IACH,iBAAiB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAK/C;;;;OAIG;IACH,OAAO,CAAC,kBAAkB;IAU1B;;;;;OAKG;IACH,OAAO,CAAC,kCAAkC;IAQ1C,OAAO,CAAC,oCAAoC;IAO5C;;;;;OAKG;IACH,OAAO,CAAC,SAAS;IAIjB;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAWrB;;;;OAIG;IACH,OAAO,CAAC,sBAAsB;IAU9B;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IA6BxB;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAU3B;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAOrB;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAOrB;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAOlB;;;;;OAKG;IACH,OAAO,CAAC,oBAAoB;IAS5B;;;;;;;;OAQG;IACH,OAAO,CAAC,6BAA6B;IAmBrC;;;;;OAKG;IACH,OAAO,CAAC,QAAQ;IAIhB,OAAO,CAAC,kBAAkB;IAU1B;;;;OAIG;IACH,OAAO,CAAC,WAAW;IAenB;;;;OAIG;IACH,OAAO,CAAC,cAAc;IAOtB;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAIlB;;;OAGG;IACH,OAAO,CAAC,aAAa;IAIrB;;;OAGG;IACH,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAInD;;OAEG;IACH,iCAAiC,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAI5D;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC;CAM9C;AAED,gBAAgB;AAChB,qBAAa,mBAAoB,SAAQ,YAAY;IAC3C,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAGjC,UAAU,IAAI,aAAa;IAGrB,oBAAoB,IAAI,OAAO,CAAC,IAAI,CAAC;IAG3C,oBAAoB,IAAI,aAAa;IAG/B,wBAAwB,IAAI,OAAO,CAAC,IAAI,CAAC;IAG/C,wBAAwB,IAAI,iBAAiB;IAGvC,yBAAyB,IAAI,OAAO,CAAC,IAAI,CAAC;IAGhD,yBAAyB,IAAI,kBAAkB;IAG/C,cAAc,IAAI,IAAI;IAGtB,cAAc,IAAI,iBAAiB;IAGnC,kBAAkB,IAAI,IAAI;IAG1B,kBAAkB,IAAI,qBAAqB;IAG3C,oBAAoB,IAAI,IAAI;IAG5B,oBAAoB,IAAI,uBAAuB,GAAG,IAAI;IAGtD,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAGzC,kBAAkB,IAAI,IAAI;IAG1B,kBAAkB,IAAI,gBAAgB;IAGtC,UAAU,IAAI,OAAO;IAGrB,OAAO,IAAI,MAAM,EAAE;IAGnB,cAAc,IAAI,MAAM,EAAE;IAG1B,YAAY,IAAI,SAAS;CAG5B"}