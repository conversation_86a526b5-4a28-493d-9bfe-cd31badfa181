{"version": 3, "file": "Authority.mjs", "sources": ["../../src/authority/Authority.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.endpointResolutionError", "ClientAuthErrorCodes.endSessionEndpointNotSupported", "CacheHelpers.generateAuthorityMetadataExpiresAt", "CacheHelpers.updateAuthorityEndpointMetadata", "ClientAuthErrorCodes.openIdConfigError", "CacheHelpers.isAuthorityMetadataExpired", "ClientConfigurationErrorCodes.invalidAuthorityMetadata", "CacheHelpers.updateCloudDiscoveryMetadata", "ClientConfigurationErrorCodes.untrustedAuthority", "ClientConfigurationErrorCodes.invalidCloudDiscoveryMetadata"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AA0DH;;;;AAIG;MACU,SAAS,CAAA;AAkClB,IAAA,WAAA,CACI,SAAiB,EACjB,gBAAgC,EAChC,YAA2B,EAC3B,gBAAkC,EAClC,MAAc,EACd,aAAqB,EACrB,iBAAsC,EACtC,eAAyB,EAAA;AAEzB,QAAA,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,uBAAuB,GAAG;AAC3B,YAAA,WAAW,EAAE,SAAS;AACtB,YAAA,aAAa,EAAE,SAAS;AACxB,YAAA,cAAc,EAAE,SAAS;SAC5B,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,KAAK,CAAC;QAChD,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CACtC,gBAAgB,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,gBAAgB,CAAC,YAAkB,EAAA;;QAEvC,IAAI,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;YAChE,OAAO,aAAa,CAAC,IAAI,CAAC;AAC7B,SAAA;AAED,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;QAC/C,IAAI,YAAY,CAAC,MAAM,EAAE;AACrB,YAAA,QAAQ,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBACjC,KAAK,SAAS,CAAC,IAAI;oBACf,OAAO,aAAa,CAAC,IAAI,CAAC;gBAC9B,KAAK,SAAS,CAAC,IAAI;oBACf,OAAO,aAAa,CAAC,IAAI,CAAC;AAGjC,aAAA;AACJ,SAAA;QACD,OAAO,aAAa,CAAC,OAAO,CAAC;KAChC;;AAGD,IAAA,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;KACtE;AAED;;AAEG;AACH,IAAA,IAAW,YAAY,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;KAC7C;AAED;;AAEG;AACH,IAAA,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAChC;AAED;;AAEG;AACH,IAAA,IAAW,kBAAkB,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;KAC7C;AAED;;AAEG;IACH,IAAW,kBAAkB,CAAC,GAAW,EAAA;QACrC,IAAI,CAAC,mBAAmB,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;KAChD;AAED;;AAEG;AACH,IAAA,IAAW,+BAA+B,GAAA;AACtC,QAAA,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;AACxC,YAAA,IAAI,CAAC,gCAAgC;AACjC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;AACnD,SAAA;QAED,OAAO,IAAI,CAAC,gCAAgC,CAAC;KAChD;AAED;;AAEG;AACH,IAAA,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;KAC7E;AAED;;AAEG;AACH,IAAA,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;KAC/D;AAED;;AAEG;AACH,IAAA,IAAW,qBAAqB,GAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AACjE,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,IAAW,aAAa,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED,IAAA,IAAW,kBAAkB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC1B,YAAA,OAAO,IAAI,CAAC,WAAW,CACnB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC,CAChE,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,IAAW,kBAAkB,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;;AAE1B,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;AACrC,gBAAA,MAAM,qBAAqB,CACvBC,8BAAmD,CACtD,CAAC;AACL,aAAA;YACD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AAC/D,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBD,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,IAAW,qBAAqB,GAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACjD,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,IAAW,OAAO,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnD,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBA,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;AACK,IAAA,gBAAgB,CAAC,YAAkB,EAAA;AACvC,QAAA,QACI,YAAY,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;AACtC,YAAA,CAAC,SAAS,CAAC,qBAAqB,CAAC,GAAG,CAChC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAC/B;YACD,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,aAAa,CAAC,OAAO;AAC7D,YAAA,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EACzC;KACL;AAED;;;AAGG;AACK,IAAA,aAAa,CAAC,SAAiB,EAAA;QACnC,OAAO,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACjE;AAED;;;AAGG;AACK,IAAA,WAAW,CAAC,SAAiB,EAAA;QACjC,IAAI,QAAQ,GAAG,SAAS,CAAC;QACzB,MAAM,kBAAkB,GAAG,IAAI,SAAS,CACpC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACpC,CAAC;AACF,QAAA,MAAM,4BAA4B,GAC9B,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;AAC1C,QAAA,MAAM,oBAAoB,GAAG,4BAA4B,CAAC,YAAY,CAAC;AACvE,QAAA,MAAM,qBAAqB,GACvB,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC;QAEtD,qBAAqB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,KAAI;AACjD,YAAA,IAAI,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC7C,IACI,KAAK,KAAK,CAAC;AACX,gBAAA,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,EACrD;AACE,gBAAA,MAAM,QAAQ,GAAG,IAAI,SAAS,CAC1B,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CACvC,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AACrC;;;;AAIG;gBACH,IAAI,UAAU,KAAK,QAAQ,EAAE;oBACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAgC,6BAAA,EAAA,UAAU,CAAY,SAAA,EAAA,QAAQ,CAAE,CAAA,CACnE,CAAC;oBACF,UAAU,GAAG,QAAQ,CAAC;AACzB,iBAAA;AACJ,aAAA;YACD,IAAI,WAAW,KAAK,UAAU,EAAE;AAC5B,gBAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CACvB,CAAA,CAAA,EAAI,UAAU,CAAA,CAAA,CAAG,EACjB,CAAA,CAAA,EAAI,WAAW,CAAA,CAAA,CAAG,CACrB,CAAC;AACL,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;KACvC;AAED;;AAEG;AACH,IAAA,IAAc,kCAAkC,GAAA;AAC5C,QAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,eAAe,CAAC;AACpD,QAAA,IACI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC;AACzC,YAAA,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI;AACzC,aAAC,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI;AACpC,gBAAA,CAAC,IAAI,CAAC,gCAAgC,CAAC,sBAAsB,CAAC,CAAC,EACrE;AACE,YAAA,OAAO,CAAG,EAAA,IAAI,CAAC,kBAAkB,kCAAkC,CAAC;AACvE,SAAA;AACD,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,kBAAkB,uCAAuC,CAAC;KAC5E;AAED;;AAEG;IACH,iBAAiB,GAAA;AACb,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC1B;AAED;;;AAGG;AACI,IAAA,MAAM,qBAAqB,GAAA;AAC9B,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,8BAA8B,EAChD,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAEvD,QAAA,MAAM,oBAAoB,GAAG,MAAM,WAAW,CAC1C,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5C,iBAAiB,CAAC,qCAAqC,EACvD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,cAAc,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CACrD,IAAI,CAAC,eAAe,EACpB,cAAc,CAAC,iBAAiB,CACnC,CAAC;AACF,QAAA,MAAM,cAAc,GAAG,MAAM,WAAW,CACpC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+BAA+B,EACjD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,cAAc,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,oBAAoB,EAAE;AAC5D,YAAA,MAAM,EAAE,cAAc;AACzB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B;AACI,YAAA,oBAAoB,EAAE,oBAAoB;AAC1C,YAAA,uBAAuB,EAAE,cAAc;AAC1C,SAAA,EACD,IAAI,CAAC,aAAa,CACrB,CAAC;KACL;AAED;;;;AAIG;IACK,wBAAwB,GAAA;AAC5B,QAAA,IAAI,cAAc,GACd,IAAI,CAAC,YAAY,CAAC,2BAA2B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAExE,IAAI,CAAC,cAAc,EAAE;AACjB,YAAA,cAAc,GAAG;AACb,gBAAA,OAAO,EAAE,EAAE;gBACX,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,iBAAiB,EAAE,IAAI,CAAC,eAAe;gBACvC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB;AAC5C,gBAAA,sBAAsB,EAAE,EAAE;AAC1B,gBAAA,cAAc,EAAE,EAAE;AAClB,gBAAA,oBAAoB,EAAE,EAAE;AACxB,gBAAA,MAAM,EAAE,EAAE;AACV,gBAAA,kBAAkB,EAAE,KAAK;AACzB,gBAAA,oBAAoB,EAAE,KAAK;AAC3B,gBAAA,SAAS,EAAEE,kCAA+C,EAAE;AAC5D,gBAAA,QAAQ,EAAE,EAAE;aACf,CAAC;AACL,SAAA;AACD,QAAA,OAAO,cAAc,CAAC;KACzB;AAED;;;;;;AAMG;AACK,IAAA,oBAAoB,CACxB,cAAuC,EACvC,oBAAoD,EACpD,sBAGQ,EAAA;AAER,QAAA,IACI,oBAAoB,KAAK,uBAAuB,CAAC,KAAK;AACtD,YAAA,sBAAsB,EAAE,MAAM,KAAK,uBAAuB,CAAC,KAAK,EAClE;;AAEE,YAAA,cAAc,CAAC,SAAS;gBACpBA,kCAA+C,EAAE,CAAC;AACtD,YAAA,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAChE,SAAA;AAED,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,iCAAiC,CAChE,cAAc,CAAC,eAAe,CACjC,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;KAClC;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,cAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,+BAA+B,EACjD,IAAI,CAAC,aAAa,CACrB,CAAC;QAEF,MAAM,aAAa,GACf,IAAI,CAAC,sCAAsC,CAAC,cAAc,CAAC,CAAC;;AAGhE,QAAA,IAAI,aAAa,EAAE;YACf,IACI,aAAa,CAAC,MAAM;gBACpB,uBAAuB,CAAC,gBAAgB,EAC1C;;AAEE,gBAAA,IACI,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,WAAW,EAC7D;oBACE,IAAI,aAAa,CAAC,QAAQ,EAAE;AACxB,wBAAA,MAAM,iBAAiB,GAAG,MAAM,WAAW,CACvC,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAC3C,IAAI,CACP,EACD,iBAAiB,CAAC,8CAA8C,EAChE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;wBAC1BC,+BAA4C,CACxC,cAAc,EACd,iBAAiB,EACjB,KAAK,CACR,CAAC;AACF,wBAAA,cAAc,CAAC,mBAAmB;4BAC9B,IAAI,CAAC,kBAAkB,CAAC;AAC/B,qBAAA;AACJ,iBAAA;AACJ,aAAA;YACD,OAAO,aAAa,CAAC,MAAM,CAAC;AAC/B,SAAA;;AAGD,QAAA,IAAI,QAAQ,GAAG,MAAM,WAAW,CAC5B,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uCAAuC,EACzD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,EAAE,CAAC;AACJ,QAAA,IAAI,QAAQ,EAAE;;AAEV,YAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,WAAW,EAAE;AAC7D,gBAAA,QAAQ,GAAG,MAAM,WAAW,CACxB,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,EACrD,iBAAiB,CAAC,8CAA8C,EAChE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,QAAQ,CAAC,CAAC;AACf,aAAA;YAEDA,+BAA4C,CACxC,cAAc,EACd,QAAQ,EACR,IAAI,CACP,CAAC;YACF,OAAO,uBAAuB,CAAC,OAAO,CAAC;AAC1C,SAAA;AAAM,aAAA;;YAEH,MAAM,qBAAqB,CACvBC,iBAAsC,EACtC,IAAI,CAAC,kCAAkC,CAC1C,CAAC;AACL,SAAA;KACJ;AAED;;;;;AAKG;AACK,IAAA,sCAAsC,CAC1C,cAAuC,EAAA;AAKvC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kEAAkE,CACrE,CAAC;AACF,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,6BAA6B,EAAE,CAAC;AAC5D,QAAA,IAAI,cAAc,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;YACFD,+BAA4C,CACxC,cAAc,EACd,cAAc,EACd,KAAK,CACR,CAAC;YACF,OAAO;gBACH,MAAM,EAAE,uBAAuB,CAAC,MAAM;aACzC,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gHAAgH,CACnH,CAAC;;AAGF,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,EAAE;AAClD,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yJAAyJ,CAC5J,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,iBAAiB,GACnB,IAAI,CAAC,sCAAsC,EAAE,CAAC;AAClD,YAAA,IAAI,iBAAiB,EAAE;gBACnBA,+BAA4C,CACxC,cAAc,EACd,iBAAiB,EACjB,KAAK,CACR,CAAC;gBACF,OAAO;oBACH,MAAM,EAAE,uBAAuB,CAAC,gBAAgB;AAChD,oBAAA,QAAQ,EAAE,iBAAiB;iBAC9B,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4HAA4H,CAC/H,CAAC;AACL,aAAA;AACJ,SAAA;;QAGD,MAAM,qBAAqB,GACvBE,0BAAuC,CAAC,cAAc,CAAC,CAAC;AAC5D,QAAA,IACI,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC;AACxC,YAAA,cAAc,CAAC,oBAAoB;AACnC,YAAA,CAAC,qBAAqB,EACxB;;AAEE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;AAC7D,YAAA,OAAO,EAAE,MAAM,EAAE,uBAAuB,CAAC,KAAK,EAAE,CAAC;AACpD,SAAA;AAAM,aAAA,IAAI,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC1D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;;AAKG;AACK,IAAA,mBAAmB,CACvB,cAAuC,EAAA;QAEvC,MAAM,kBAAkB,GAAG,IAAI,SAAS,CACpC,cAAc,CAAC,mBAAmB,CACrC,CAAC;QACF,MAAM,WAAW,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC;QAEvE,QACI,WAAW,CAAC,MAAM;AAClB,YAAA,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,MAAM,EAC1D;KACL;AAED;;AAEG;IACK,6BAA6B,GAAA;AACjC,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE;YACzC,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAClB,CAAC;AAC7B,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,8BAA8B,CAChCC,wBAAsD,CACzD,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACK,IAAA,MAAM,8BAA8B,GAAA;AACxC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,uCAAuC,EACzD,IAAI,CAAC,aAAa,CACrB,CAAC;QAEF,MAAM,OAAO,GAAgB,EAAE,CAAC;AAEhC;;;AAGG;AAEH,QAAA,MAAM,2BAA2B,GAC7B,IAAI,CAAC,kCAAkC,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAyF,sFAAA,EAAA,2BAA2B,CAAE,CAAA,CACzH,CAAC;QAEF,IAAI;AACA,YAAA,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAC3C,2BAA2B,EAC3B,OAAO,CACV,CAAC;YACN,MAAM,eAAe,GAAG,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9D,YAAA,IAAI,eAAe,EAAE;gBACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;AACxB,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAA,0FAAA,CAA4F,CAC/F,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAA6C,0CAAA,EAAA,CAAC,CAAE,CAAA,CACnD,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED;;AAEG;IACK,sCAAsC,GAAA;AAC1C,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,EAAE;AAC1C,YAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACjD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;AAGG;IACK,MAAM,qCAAqC,CAC/C,QAA8B,EAAA;AAE9B,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,8CAA8C,EAChE,IAAI,CAAC,aAAa,CACrB,CAAC;QAEF,MAAM,yBAAyB,GAC3B,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,WAAW,CAAC;AAEhE,QAAA,IAAI,yBAAyB,EAAE;AAC3B,YAAA,IACI,yBAAyB;gBACzB,SAAS,CAAC,+BAA+B,EAC3C;gBACE,IAAI,CAAC,uBAAuB,CAAC,cAAc;oBACvC,uBAAuB,CAAC,4BAA4B,CAAC;gBACzD,IAAI,CAAC,uBAAuB,CAAC,WAAW;AACpC,oBAAA,yBAAyB,CAAC;gBAC9B,OAAO,SAAS,CAAC,8BAA8B,CAC3C,QAAQ,EACR,yBAAyB,CAC5B,CAAC;AACL,aAAA;AAED,YAAA,MAAM,sBAAsB,GAAG,MAAM,WAAW,CAC5C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAC5D,iBAAiB,CAAC,2BAA2B,EAC7C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CACG,IAAI,CAAC,gBAAgB,CAAC,wBAAwB;AAC1C,kBAAE,iBAAiB,EACvB,IAAI,CAAC,uBAAuB,CAC/B,CAAC;AAEF,YAAA,IAAI,sBAAsB,EAAE;gBACxB,IAAI,CAAC,uBAAuB,CAAC,cAAc;oBACvC,uBAAuB,CAAC,mCAAmC,CAAC;gBAChE,IAAI,CAAC,uBAAuB,CAAC,WAAW;AACpC,oBAAA,sBAAsB,CAAC;gBAC3B,OAAO,SAAS,CAAC,8BAA8B,CAC3C,QAAQ,EACR,sBAAsB,CACzB,CAAC;AACL,aAAA;YAED,IAAI,CAAC,uBAAuB,CAAC,cAAc;gBACvC,uBAAuB,CAAC,+BAA+B,CAAC;AAC/D,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB;AAED;;;;;AAKG;IACK,MAAM,4BAA4B,CACtC,cAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,qCAAqC,EACvD,IAAI,CAAC,aAAa,CACrB,CAAC;QACF,MAAM,mBAAmB,GACrB,IAAI,CAAC,4CAA4C,CAAC,cAAc,CAAC,CAAC;AACtE,QAAA,IAAI,mBAAmB,EAAE;AACrB,YAAA,OAAO,mBAAmB,CAAC;AAC9B,SAAA;;AAGD,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAC9B,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,IAAI,CAAC,EACpD,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,EAAE,CAAC;AAEJ,QAAA,IAAI,QAAQ,EAAE;YACVC,4BAAyC,CACrC,cAAc,EACd,QAAQ,EACR,IAAI,CACP,CAAC;YACF,OAAO,uBAAuB,CAAC,OAAO,CAAC;AAC1C,SAAA;;AAGD,QAAA,MAAM,8BAA8B,CAChCC,kBAAgD,CACnD,CAAC;KACL;AAEO,IAAA,4CAA4C,CAChD,cAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0EAA0E,CAC7E,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,sBACI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;AACtC,YAAA,SAAS,CAAC,cACd,CAAE,CAAA,CACL,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,uBACI,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;AACvC,YAAA,SAAS,CAAC,cACd,CAAE,CAAA,CACL,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CACI,qBAAA,EAAA,cAAc,CAAC,mBAAmB,IAAI,SAAS,CAAC,cACpD,CAAA,CAAE,CACL,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,mCAAmC,EAAE,CAAC;AAC5D,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2DAA2D,CAC9D,CAAC;YACFD,4BAAyC,CACrC,cAAc,EACd,QAAQ,EACR,KAAK,CACR,CAAC;YACF,OAAO,uBAAuB,CAAC,MAAM,CAAC;AACzC,SAAA;;AAGD,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8HAA8H,CACjI,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;AACzC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gLAAgL,CACnL,CAAC;AACL,SAAA;AAAM,aAAA;YACH,MAAM,iBAAiB,GACnB,4CAA4C,CACxC,IAAI,CAAC,eAAe,CACvB,CAAC;AACN,YAAA,IAAI,iBAAiB,EAAE;AACnB,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uDAAuD,CAC1D,CAAC;gBACFA,4BAAyC,CACrC,cAAc,EACd,iBAAiB,EACjB,KAAK,CACR,CAAC;gBACF,OAAO,uBAAuB,CAAC,gBAAgB,CAAC;AACnD,aAAA;AAED,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0IAA0I,CAC7I,CAAC;AACL,SAAA;QAED,MAAM,qBAAqB,GACvBF,0BAAuC,CAAC,cAAc,CAAC,CAAC;AAC5D,QAAA,IACI,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC;AACxC,YAAA,cAAc,CAAC,kBAAkB;AACjC,YAAA,CAAC,qBAAqB,EACxB;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;;YAEpE,OAAO,uBAAuB,CAAC,KAAK,CAAC;AACxC,SAAA;AAAM,aAAA,IAAI,qBAAqB,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC1D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;AAEG;IACK,mCAAmC,GAAA;;AAEvC,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI,EAAE;AAC3C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qGAAqG,CACxG,CAAC;YACF,OAAO,SAAS,CAAC,oCAAoC,CACjD,IAAI,CAAC,eAAe,CACvB,CAAC;AACL,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;AAC9C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sFAAsF,CACzF,CAAC;YACF,IAAI;AACA,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mDAAmD,CACtD,CAAC;AACF,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC7B,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CACb,CAAC;AACpC,gBAAA,MAAM,QAAQ,GAAG,4CAA4C,CACzD,cAAc,CAAC,QAAQ,EACvB,IAAI,CAAC,eAAe,CACvB,CAAC;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5D,gBAAA,IAAI,QAAQ,EAAE;AACV,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+EAA+E,CAClF,CAAC;AACF,oBAAA,OAAO,QAAQ,CAAC;AACnB,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uEAAuE,CAC1E,CAAC;AACL,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gGAAgG,CACnG,CAAC;AACF,gBAAA,MAAM,8BAA8B,CAChCI,6BAA2D,CAC9D,CAAC;AACL,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gGAAgG,CACnG,CAAC;YACF,OAAO,SAAS,CAAC,oCAAoC,CACjD,IAAI,CAAC,eAAe,CACvB,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACK,IAAA,MAAM,oCAAoC,GAAA;AAC9C,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,aAAa,CACrB,CAAC;QACF,MAAM,yBAAyB,GAAG,CAAA,EAAG,SAAS,CAAC,4BAA4B,CAAA,EAAG,IAAI,CAAC,kBAAkB,CAAA,qBAAA,CAAuB,CAAC;QAC7H,MAAM,OAAO,GAAgB,EAAE,CAAC;AAEhC;;;AAGG;QAEH,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI;AACA,YAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAG9D,yBAAyB,EAAE,OAAO,CAAC,CAAC;AACtC,YAAA,IAAI,iBAEqC,CAAC;AAC1C,YAAA,IAAI,QAAuC,CAAC;AAC5C,YAAA,IAAI,gCAAgC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACjD,iBAAiB;oBACb,QAAQ,CAAC,IAAsC,CAAC;AACpD,gBAAA,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;gBAEtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CAAiC,8BAAA,EAAA,iBAAiB,CAAC,yBAAyB,CAAE,CAAA,CACjF,CAAC;AACL,aAAA;AAAM,iBAAA,IAAI,qCAAqC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC7D,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAsH,mHAAA,EAAA,QAAQ,CAAC,MAAM,CAAE,CAAA,CAC1I,CAAC;gBAEF,iBAAiB;oBACb,QAAQ,CAAC,IAA2C,CAAC;AACzD,gBAAA,IAAI,iBAAiB,CAAC,KAAK,KAAK,SAAS,CAAC,gBAAgB,EAAE;AACxD,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,oEAAoE,CACvE,CAAC;AACF,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;gBAED,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAoD,iDAAA,EAAA,iBAAiB,CAAC,KAAK,CAAE,CAAA,CAChF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAgE,6DAAA,EAAA,iBAAiB,CAAC,iBAAiB,CAAE,CAAA,CACxG,CAAC;AAEF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2FAA2F,CAC9F,CAAC;gBACF,QAAQ,GAAG,EAAE,CAAC;AACjB,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,4FAA4F,CAC/F,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AAED,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wIAAwI,CAC3I,CAAC;YACF,KAAK,GAAG,4CAA4C,CAChD,QAAQ,EACR,IAAI,CAAC,eAAe,CACvB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,KAAK,YAAY,SAAS,EAAE;AAC5B,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAoG,iGAAA,EAAA,KAAK,CAAC,SAAS,wBAAwB,KAAK,CAAC,YAAY,CAAA,CAAE,CAClK,CAAC;AACL,aAAA;AAAM,iBAAA;gBACH,MAAM,UAAU,GAAG,KAAc,CAAC;AAClC,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAwG,qGAAA,EAAA,UAAU,CAAC,IAAI,wBAAwB,UAAU,CAAC,OAAO,CAAA,CAAE,CACtK,CAAC;AACL,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;QAGD,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sHAAsH,CACzH,CAAC;AACF,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uDAAuD,CAC1D,CAAC;YAEF,KAAK,GAAG,SAAS,CAAC,oCAAoC,CAClD,IAAI,CAAC,eAAe,CACvB,CAAC;AACL,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED;;AAEG;IACK,oBAAoB,GAAA;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CACzD,CAAC,SAAS,KAAI;AACV,YAAA,QACI,SAAS;AACT,gBAAA,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;oBAC/C,IAAI,CAAC,eAAe,EAC1B;AACN,SAAC,CACJ,CAAC;AACF,QAAA,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KAC7B;AAED;;;;AAIG;AACH,IAAA,OAAO,iBAAiB,CACpB,eAAuB,EACvB,iBAAqC,EAAA;AAErC,QAAA,IAAI,2BAA2B,CAAC;AAEhC,QAAA,IACI,iBAAiB;AACjB,YAAA,iBAAiB,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,IAAI,EAClE;AACE,YAAA,MAAM,MAAM,GAAG,iBAAiB,CAAC,MAAM;kBACjC,iBAAiB,CAAC,MAAM;AAC1B,kBAAE,SAAS,CAAC,qBAAqB,CAAC;YACtC,2BAA2B,GAAG,GAAG,iBAAiB,CAAC,kBAAkB,CAAI,CAAA,EAAA,MAAM,GAAG,CAAC;AACtF,SAAA;AAED,QAAA,OAAO,2BAA2B;AAC9B,cAAE,2BAA2B;cAC3B,eAAe,CAAC;KACzB;AAED;;;AAGG;IACH,OAAO,oCAAoC,CACvC,IAAY,EAAA;QAEZ,OAAO;AACH,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,eAAe,EAAE,IAAI;YACrB,OAAO,EAAE,CAAC,IAAI,CAAC;SAClB,CAAC;KACL;AAED;;AAEG;IACH,iBAAiB,GAAA;QACb,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO,SAAS,CAAC,sBAAsB,CAAC;AAC3C,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACjC,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;AACxC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,qBAAqB,CACvBT,uBAA4C,CAC/C,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,OAAO,CAAC,IAAY,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;KACnD;AAED;;;AAGG;AACH,IAAA,gCAAgC,CAAC,IAAY,EAAA;AACzC,QAAA,OAAO,gCAAgC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACrD;AAED;;;;;AAKG;IACH,OAAO,sBAAsB,CAAC,IAAY,EAAA;QACtC,OAAO,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3D;AAED;;;;;AAKG;AACH,IAAA,OAAO,4BAA4B,CAC/B,IAAY,EACZ,MAAc,EACd,WAAoB,EAAA;;AAGpB,QAAA,MAAM,oBAAoB,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QACjD,oBAAoB,CAAC,aAAa,EAAE,CAAC;AAErC,QAAA,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;QAElE,IAAI,eAAe,GAAG,CAAG,EAAA,MAAM,IAAI,iBAAiB,CAAC,eAAe,CAAA,CAAE,CAAC;QAEvE,IAAI,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE;YAChE,eAAe,GAAG,GAAG,MAAM,CAAA,CAAA,EAAI,SAAS,CAAC,iCAAiC,EAAE,CAAC;AAChF,SAAA;;AAGD,QAAA,MAAM,GAAG,GAAG,SAAS,CAAC,+BAA+B,CAAC;YAClD,GAAG,oBAAoB,CAAC,gBAAgB,EAAE;AAC1C,YAAA,eAAe,EAAE,eAAe;SACnC,CAAC,CAAC,SAAS,CAAC;;AAGb,QAAA,IAAI,WAAW;AAAE,YAAA,OAAO,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,WAAW,EAAE,CAAC;AAEhD,QAAA,OAAO,GAAG,CAAC;KACd;AAED;;;;;AAKG;AACH,IAAA,OAAO,8BAA8B,CACjC,QAA8B,EAC9B,WAAmB,EAAA;AAEnB,QAAA,MAAM,gBAAgB,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;AACzC,QAAA,gBAAgB,CAAC,sBAAsB;YACnC,SAAS,CAAC,4BAA4B,CAClC,gBAAgB,CAAC,sBAAsB,EACvC,WAAW,CACd,CAAC;AAEN,QAAA,gBAAgB,CAAC,cAAc;YAC3B,SAAS,CAAC,4BAA4B,CAClC,gBAAgB,CAAC,cAAc,EAC/B,WAAW,CACd,CAAC;QAEN,IAAI,gBAAgB,CAAC,oBAAoB,EAAE;AACvC,YAAA,gBAAgB,CAAC,oBAAoB;gBACjC,SAAS,CAAC,4BAA4B,CAClC,gBAAgB,CAAC,oBAAoB,EACrC,WAAW,CACd,CAAC;AACT,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC;KAC3B;AAED;;;;;;;;AAQG;IACH,OAAO,sBAAsB,CAAC,SAAiB,EAAA;QAC3C,IAAI,aAAa,GAAG,SAAS,CAAC;AAC9B,QAAA,MAAM,YAAY,GAAG,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;AAC9C,QAAA,MAAM,sBAAsB,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;;AAG/D,QAAA,IACI,sBAAsB,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;YAChD,sBAAsB,CAAC,eAAe,CAAC,QAAQ,CAC3C,SAAS,CAAC,aAAa,CAC1B,EACH;AACE,YAAA,MAAM,gBAAgB,GAClB,sBAAsB,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,aAAa,GAAG,CAAG,EAAA,aAAa,CAAG,EAAA,gBAAgB,GAAG,SAAS,CAAC,wBAAwB,CAAA,CAAE,CAAC;AAC9F,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB;;AA7rCD;AACe,SAAqB,CAAA,qBAAA,GAAgB,IAAI,GAAG,CAAC;IACxD,UAAU;IACV,YAAY;AACZ,IAAA,qBAAqB,CAAC,MAAM;AAC5B,IAAA,qBAAqB,CAAC,SAAS;AAC/B,IAAA,qBAAqB,CAAC,aAAa;AACtC,CAAA,CAAC,CAAC;AAyrCP;;AAEG;AACG,SAAU,4BAA4B,CACxC,SAAiB,EAAA;AAEjB,IAAA,MAAM,YAAY,GAAG,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;AAC9C,IAAA,MAAM,sBAAsB,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;AAC/D;;;;;;;AAOG;AACH,IAAA,MAAM,QAAQ,GACV,sBAAsB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;AAEpE,IAAA,QAAQ,QAAQ;QACZ,KAAK,qBAAqB,CAAC,MAAM,CAAC;QAClC,KAAK,qBAAqB,CAAC,aAAa,CAAC;QACzC,KAAK,qBAAqB,CAAC,SAAS;AAChC,YAAA,OAAO,SAAS,CAAC;AACrB,QAAA;AACI,YAAA,OAAO,QAAQ,CAAC;AACvB,KAAA;AACL,CAAC;AAEK,SAAU,kBAAkB,CAAC,YAAoB,EAAA;AACnD,IAAA,OAAO,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC;AACjD,UAAE,YAAY;UACZ,GAAG,YAAY,CAAA,EAAG,SAAS,CAAC,aAAa,EAAE,CAAC;AACtD,CAAC;AAEK,SAAU,2BAA2B,CACvC,WAAsC,EAAA;AAEtC,IAAA,MAAM,yBAAyB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IACrE,IAAI,sBAAsB,GACtB,SAAS,CAAC;AACd,IAAA,IAAI,yBAAyB,EAAE;QAC3B,IAAI;AACA,YAAA,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAClE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,8BAA8B,CAChCS,6BAA2D,CAC9D,CAAC;AACL,SAAA;AACJ,KAAA;IACD,OAAO;QACH,kBAAkB,EAAE,WAAW,CAAC,SAAS;AACrC,cAAE,kBAAkB,CAAC,WAAW,CAAC,SAAS,CAAC;AAC3C,cAAE,SAAS;QACf,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;AAC9C,QAAA,sBAAsB,EAAE,sBAAsB;KACjD,CAAC;AACN;;;;"}