/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { SignInResendCodeError } from '../error_type/SignInError.mjs';
import { SignInFailedState } from '../state/SignInFailedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
class SignInResendCodeResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of SignInResendCodeResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of SignInResendCodeResult with an error.
     * @param error The error that occurred.
     * @returns {SignInResendCodeResult} A new instance of SignInResendCodeResult with the error set.
     */
    static createWithError(error) {
        const result = new SignInResendCodeResult(new SignInFailedState());
        result.error = new SignInResendCodeError(SignInResendCodeResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignInFailedState;
    }
    /**
     * Checks if the result is in a code required state.
     */
    isCodeRequired() {
        /*
         * The instanceof operator couldn't be used here to check the state type since the circular dependency issue.
         * So we are using the constructor name to check the state type.
         */
        return this.state.constructor?.name === "SignInCodeRequiredState";
    }
}

export { SignInResendCodeResult };
//# sourceMappingURL=SignInResendCodeResult.mjs.map
