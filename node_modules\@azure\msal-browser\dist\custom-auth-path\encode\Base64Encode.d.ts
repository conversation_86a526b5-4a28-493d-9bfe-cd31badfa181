/**
 * Class which exposes APIs to encode plaintext to base64 encoded string. See here for implementation details:
 * https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#Solution_2_%E2%80%93_JavaScript's_UTF-16_%3E_UTF-8_%3E_base64
 */
/**
 * Returns URL Safe b64 encoded string from a plaintext string.
 * @param input
 */
export declare function urlEncode(input: string): string;
/**
 * Returns URL Safe b64 encoded string from an int8Array.
 * @param inputArr
 */
export declare function urlEncodeArr(inputArr: Uint8Array): string;
/**
 * Returns b64 encoded string from plaintext string.
 * @param input
 */
export declare function base64Encode(input: string): string;
//# sourceMappingURL=Base64Encode.d.ts.map