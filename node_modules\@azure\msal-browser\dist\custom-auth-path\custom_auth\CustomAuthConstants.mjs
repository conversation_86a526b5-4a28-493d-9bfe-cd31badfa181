/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { Constants } from '@azure/msal-common/browser';
import { version } from '../packageMetadata.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const GrantType = {
    PASSWORD: "password",
    OOB: "oob",
    CONTINUATION_TOKEN: "continuation_token",
    ATTRIBUTES: "attributes",
};
const ChallengeType = {
    PASSWORD: "password",
    OOB: "oob",
    REDIRECT: "redirect",
};
const DefaultScopes = [
    Constants.OPENID_SCOPE,
    Constants.PROFILE_SCOPE,
    Constants.OFFLINE_ACCESS_SCOPE,
];
const HttpHeaderKeys = {
    CONTENT_TYPE: "Content-Type",
    X_MS_REQUEST_ID: "x-ms-request-id",
};
const DefaultPackageInfo = {
    SKU: "msal.browser",
    VERSION: version,
    OS: "",
    CPU: "",
};
const ResetPasswordPollStatus = {
    IN_PROGRESS: "in_progress",
    SUCCEEDED: "succeeded",
    FAILED: "failed",
    NOT_STARTED: "not_started",
};
const DefaultCustomAuthApiCodeLength = -1; // Default value indicating that the code length is not specified
const DefaultCustomAuthApiCodeResendIntervalInSec = 300; // seconds
const PasswordResetPollingTimeoutInMs = 300000; // milliseconds

export { ChallengeType, DefaultCustomAuthApiCodeLength, DefaultCustomAuthApiCodeResendIntervalInSec, DefaultPackageInfo, DefaultScopes, GrantType, HttpHeaderKeys, PasswordResetPollingTimeoutInMs, ResetPasswordPollStatus };
//# sourceMappingURL=CustomAuthConstants.mjs.map
