{"version": 3, "file": "AuthorizationCodeClient.mjs", "sources": ["../../src/client/AuthorizationCodeClient.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.requestCannotBeMade", "TimeUtils.nowSeconds", "ClientConfigurationErrorCodes.logoutRequestEmpty", "RequestParameterBuilder.addClientId", "AADServerParamKeys.CLIENT_ID", "ClientConfigurationErrorCodes.redirectUriEmpty", "RequestParameterBuilder.addRedirectUri", "RequestParameterBuilder.addScopes", "RequestParameterBuilder.addAuthorizationCode", "RequestParameterBuilder.addLibraryInfo", "RequestParameterBuilder.addApplicationTelemetry", "RequestParameterBuilder.addThrottling", "RequestParameterBuilder.addServerTelemetry", "RequestParameterBuilder.addCodeVerifier", "RequestParameterBuilder.addClientSecret", "RequestParameterBuilder.addClientAssertion", "RequestParameterBuilder.addClientAssertionType", "RequestParameterBuilder.addGrantType", "RequestParameterBuilder.addClientInfo", "RequestParameterBuilder.addPopToken", "RequestParameterBuilder.addSshJwk", "ClientConfigurationErrorCodes.missingSshJwk", "RequestParameterBuilder.addClaims", "RequestParameterBuilder.addCcsOid", "RequestParameterBuilder.addCcsUpn", "RequestParameterBuilder.addBrokerParameters", "RequestParameterBuilder.addExtraQueryParameters", "AADServerParamKeys.RETURN_SPA_CODE", "RequestParameterBuilder.instrumentBrokerParams", "UrlUtils.mapToQueryString", "RequestParameterBuilder.addPostLogoutRedirectUri", "RequestParameterBuilder.addCorrelationId", "RequestParameterBuilder.addIdTokenHint", "RequestParameterBuilder.addState", "RequestParameterBuilder.addLogoutHint", "RequestParameterBuilder.addInstanceAware"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAgDH;;;AAGG;AACG,MAAO,uBAAwB,SAAQ,UAAU,CAAA;IAKnD,WACI,CAAA,aAAkC,EAClC,iBAAsC,EAAA;AAEtC,QAAA,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;;QAPlC,IAAkB,CAAA,kBAAA,GAAY,IAAI,CAAC;AAQzC,QAAA,IAAI,CAAC,iBAAiB;AAClB,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC;KAC5E;AAED;;;;AAIG;AACH,IAAA,MAAM,YAAY,CACd,OAAuC,EACvC,eAA0C,EAAA;AAE1C,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,sBAAsB,EACxC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACf,YAAA,MAAM,qBAAqB,CACvBA,mBAAwC,CAC3C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,YAAY,GAAGC,UAAoB,EAAE,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,6BAA6B,EAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;;QAG3B,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;AAElE,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAErD,OAAO,WAAW,CACd,eAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,EAC/D,iBAAiB,CAAC,yBAAyB,EAC3C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,eAAe,EACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACZ,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,aAAsC,EAAA;;QAE/C,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,MAAM,8BAA8B,CAChCC,kBAAgD,CACnD,CAAC;AACL,SAAA;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;;AAGnE,QAAA,OAAO,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,CAAC,kBAAkB,EACjC,WAAW,CACd,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,SAAoB,EACpB,OAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,6BAA6B,EAC/C,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;AAEF,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,CAAC,CAAC;QAEX,IAAI,aAAa,GAA8B,SAAS,CAAC;QACzD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,IAAI;AACA,gBAAA,MAAM,UAAU,GAAG,eAAe,CAC9B,OAAO,CAAC,UAAU,EAClB,IAAI,CAAC,WAAW,CAAC,YAAY,CAChC,CAAC;AACF,gBAAA,aAAa,GAAG;AACZ,oBAAA,UAAU,EAAE,CAAA,EAAG,UAAU,CAAC,GAAG,CAAA,EAAG,UAAU,CAAC,qBAAqB,CAAA,EAAG,UAAU,CAAC,IAAI,CAAE,CAAA;oBACpF,IAAI,EAAE,iBAAiB,CAAC,eAAe;iBAC1C,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8CAA8C,GAAG,CAAC,CACrD,CAAC;AACL,aAAA;AACJ,SAAA;AACD,QAAA,MAAM,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAClE,aAAa,IAAI,OAAO,CAAC,aAAa,CACzC,CAAC;AAEF,QAAA,MAAM,UAAU,GAAG,oBAAoB,CACnC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CACV,CAAC;QAEF,OAAO,WAAW,CACd,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1C,iBAAiB,CAAC,iDAAiD,EACnE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,iBAAiB,CAAC,iDAAiD,CACtE,CAAC;KACL;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAuC,EAAA;AAEvC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,gCAAgC,EAClD,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C,QAAAC,WAAmC,CAC/B,UAAU,EACV,OAAO,CAAC,gBAAgB;AACpB,YAAA,OAAO,CAAC,mBAAmB,GAAGC,SAA4B,CAAC;AAC3D,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACvC,CAAC;AAEF;;;AAGG;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;;AAE1B,YAAA,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AACtB,gBAAA,MAAM,8BAA8B,CAChCC,gBAA8C,CACjD,CAAC;AACL,aAAA;AACJ,SAAA;AAAM,aAAA;;YAEHC,cAAsC,CAClC,UAAU,EACV,OAAO,CAAC,WAAW,CACtB,CAAC;AACL,SAAA;;AAGD,QAAAC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,EACJ,IAAI,CAAC,iBAAiB,CACzB,CAAC;;QAGFC,oBAA4C,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;;QAGvEC,cAAsC,CAClC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAC1B,CAAC;AACF,QAAAC,uBAA+C,CAC3C,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AACF,QAAAC,aAAqC,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACjEC,kBAA0C,CACtC,UAAU,EACV,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AACL,SAAA;;QAGD,IAAI,OAAO,CAAC,YAAY,EAAE;YACtBC,eAAuC,CACnC,UAAU,EACV,OAAO,CAAC,YAAY,CACvB,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;AAC5C,YAAAC,eAAuC,CACnC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;YAC/C,MAAM,eAAe,GACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAElDC,kBAA0C,CACtC,UAAU,EACV,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;YACFC,sBAA8C,CAC1C,UAAU,EACV,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;QAEDC,YAAoC,CAChC,UAAU,EACV,SAAS,CAAC,wBAAwB,CACrC,CAAC;AACF,QAAAC,aAAqC,CAAC,UAAU,CAAC,CAAC;AAElD,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;AAC3D,YAAA,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC3C,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AACjD,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3D,aAAA;;AAGD,YAAAC,WAAmC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC/D,SAAA;AAAM,aAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;YAClE,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChBC,SAAiC,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACjE,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,aAAA;AACJ,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAAC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;QAED,IAAI,OAAO,GAA8B,SAAS,CAAC;QACnD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,IAAI;AACA,gBAAA,MAAM,UAAU,GAAG,eAAe,CAC9B,OAAO,CAAC,UAAU,EAClB,IAAI,CAAC,WAAW,CAAC,YAAY,CAChC,CAAC;AACF,gBAAA,OAAO,GAAG;AACN,oBAAA,UAAU,EAAE,CAAA,EAAG,UAAU,CAAC,GAAG,CAAA,EAAG,UAAU,CAAC,qBAAqB,CAAA,EAAG,UAAU,CAAC,IAAI,CAAE,CAAA;oBACpF,IAAI,EAAE,iBAAiB,CAAC,eAAe;iBAC1C,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8CAA8C,GAAG,CAAC,CACrD,CAAC;AACL,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC;AACnC,SAAA;;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,EAAE;YAC3D,QAAQ,OAAO,CAAC,IAAI;gBAChB,KAAK,iBAAiB,CAAC,eAAe;oBAClC,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,UAAU,CACrB,CAAC;AACF,wBAAAC,SAAiC,CAC7B,UAAU,EACV,UAAU,CACb,CAAC;AACL,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD;AAC9C,4BAAA,CAAC,CACR,CAAC;AACL,qBAAA;oBACD,MAAM;gBACV,KAAK,iBAAiB,CAAC,GAAG;oBACtBC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,UAAU,CACrB,CAAC;oBACF,MAAM;AACb,aAAA;AACJ,SAAA;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1BC,mBAA2C,CACvC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CACtC,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC7BC,uBAA+C,CAC3C,UAAU,EACV,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;;QAGD,IACI,OAAO,CAAC,0BAA0B;aACjC,CAAC,OAAO,CAAC,mBAAmB;gBACzB,CAAC,OAAO,CAAC,mBAAmB,CACxBC,eAAkC,CACrC,CAAC,EACR;AACE,YAAAD,uBAA+C,CAAC,UAAU,EAAE;AACxD,gBAAA,CAACC,eAAkC,GAAG,GAAG;AAC5C,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAAC,sBAA8C,CAC1C,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACF,QAAA,OAAOC,gBAAyB,CAAC,UAAU,CAAC,CAAC;KAChD;AAED;;;AAGG;AACK,IAAA,0BAA0B,CAC9B,OAAgC,EAAA;AAEhC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE7C,IAAI,OAAO,CAAC,qBAAqB,EAAE;YAC/BC,wBAAgD,CAC5C,UAAU,EACV,OAAO,CAAC,qBAAqB,CAChC,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,aAAa,EAAE;YACvBC,gBAAwC,CACpC,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;YACrBC,cAAsC,CAClC,UAAU,EACV,OAAO,CAAC,WAAW,CACtB,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;YACfC,QAAgC,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/D,SAAA;QAED,IAAI,OAAO,CAAC,UAAU,EAAE;YACpBC,aAAqC,CACjC,UAAU,EACV,OAAO,CAAC,UAAU,CACrB,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAC9BR,uBAA+C,CAC3C,UAAU,EACV,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE;AACvC,YAAAS,gBAAwC,CAAC,UAAU,CAAC,CAAC;AACxD,SAAA;AAED,QAAA,OAAON,gBAAyB,CAC5B,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,sBAAsB,EAC9C,OAAO,CAAC,oBAAoB,CAC/B,CAAC;KACL;AACJ;;;;"}