{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../../src/legacy/lroEngine/operation.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAG/E,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAEzC,MAAM,gBAAgB,GAGlB,GAAG,EAAE,CAAC,CAAC;IACT,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAQ;IAC3D,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;IAClD,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;IACjD,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IACrD,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;IAC/C,YAAY,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;IACnD,SAAS,EAAE,GAAG,EAAE;QACd,iBAAiB;IACnB,CAAC;IAED,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK;IAChC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM;IAClC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW;IAC1C,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK;IAClC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS;IACvC,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;CACzF,CAAC,CAAC;AAEH,MAAM,OAAO,oBAAoB;IAK/B,YACS,KAAuC,EACtC,GAAyB,EACzB,gBAAyB,EACzB,yBAAqD,EACrD,aAA2D,EAC3D,WAAgE,EAChE,MAA0D;QAN3D,UAAK,GAAL,KAAK,CAAkC;QACtC,QAAG,GAAH,GAAG,CAAsB;QACzB,qBAAgB,GAAhB,gBAAgB,CAAS;QACzB,8BAAyB,GAAzB,yBAAyB,CAA4B;QACrD,kBAAa,GAAb,aAAa,CAA8C;QAC3D,gBAAW,GAAX,WAAW,CAAqD;QAChE,WAAM,GAAN,MAAM,CAAoD;IACjE,CAAC;IAEG,eAAe,CAAC,YAA0B;QAC/C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAGZ;;QACC,MAAM,UAAU,GAAG,gBAAgB,EAAmB,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,mCACL,IAAI,CAAC,KAAK,GACV,CAAC,MAAM,iBAAiB,CAAC;gBAC1B,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,UAAU;gBACV,sBAAsB,EAAE,IAAI,CAAC,yBAAyB;gBACtD,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;aACxC,CAAC,CAAC,CACJ,CAAC;QACJ,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC9D,MAAM,iBAAiB,CAAC;gBACtB,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,UAAU;gBACV,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW;oBACtB,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC;oBAC7D,CAAC,CAAC,SAAS;gBACb,MAAM,EAAE,MAAM;oBACZ,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,YAAuB,EAAE,KAAK,CAAC;oBACrE,CAAC,CAAC,SAAS;gBACb,OAAO;gBACP,QAAQ,EAAE,CAAC,YAAY,EAAE,EAAE;oBACzB,IAAI,CAAC,YAAa,CAAC,YAAY,GAAG,YAAY,CAAC;gBACjD,CAAC;gBACD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;aACxC,CAAC,CAAC;QACL,CAAC;QACD,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,wDAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,MAAM;QACV,MAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LongRunningOperation, LroResourceLocationConfig, RawResponse } from \"../../http/models.js\";\nimport { PollOperation, PollOperationState } from \"../pollOperation.js\";\nimport { RestorableOperationState, StateProxy } from \"../../poller/models.js\";\nimport { initHttpOperation, pollHttpOperation } from \"../../http/operation.js\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { PollerConfig } from \"./models.js\";\nimport { logger } from \"../../logger.js\";\n\nconst createStateProxy: <TResult, TState extends PollOperationState<TResult>>() => StateProxy<\n  TState,\n  TResult\n> = () => ({\n  initState: (config) => ({ config, isStarted: true }) as any,\n  setCanceled: (state) => (state.isCancelled = true),\n  setError: (state, error) => (state.error = error),\n  setResult: (state, result) => (state.result = result),\n  setRunning: (state) => (state.isStarted = true),\n  setSucceeded: (state) => (state.isCompleted = true),\n  setFailed: () => {\n    /** empty body */\n  },\n\n  getError: (state) => state.error,\n  getResult: (state) => state.result,\n  isCanceled: (state) => !!state.isCancelled,\n  isFailed: (state) => !!state.error,\n  isRunning: (state) => !!state.isStarted,\n  isSucceeded: (state) => Boolean(state.isCompleted && !state.isCancelled && !state.error),\n});\n\nexport class GenericPollOperation<TResult, TState extends PollOperationState<TResult>>\n  implements PollOperation<TState, TResult>\n{\n  private pollerConfig?: PollerConfig;\n\n  constructor(\n    public state: RestorableOperationState<TState>,\n    private lro: LongRunningOperation,\n    private setErrorAsResult: boolean,\n    private lroResourceLocationConfig?: LroResourceLocationConfig,\n    private processResult?: (result: unknown, state: TState) => TResult,\n    private updateState?: (state: TState, lastResponse: RawResponse) => void,\n    private isDone?: (lastResponse: TResult, state: TState) => boolean,\n  ) {}\n\n  public setPollerConfig(pollerConfig: PollerConfig): void {\n    this.pollerConfig = pollerConfig;\n  }\n\n  async update(options?: {\n    abortSignal?: AbortSignalLike;\n    fireProgress?: (state: TState) => void;\n  }): Promise<PollOperation<TState, TResult>> {\n    const stateProxy = createStateProxy<TResult, TState>();\n    if (!this.state.isStarted) {\n      this.state = {\n        ...this.state,\n        ...(await initHttpOperation({\n          lro: this.lro,\n          stateProxy,\n          resourceLocationConfig: this.lroResourceLocationConfig,\n          processResult: this.processResult,\n          setErrorAsResult: this.setErrorAsResult,\n        })),\n      };\n    }\n    const updateState = this.updateState;\n    const isDone = this.isDone;\n\n    if (!this.state.isCompleted && this.state.error === undefined) {\n      await pollHttpOperation({\n        lro: this.lro,\n        state: this.state,\n        stateProxy,\n        processResult: this.processResult,\n        updateState: updateState\n          ? (state, { rawResponse }) => updateState(state, rawResponse)\n          : undefined,\n        isDone: isDone\n          ? ({ flatResponse }, state) => isDone(flatResponse as TResult, state)\n          : undefined,\n        options,\n        setDelay: (intervalInMs) => {\n          this.pollerConfig!.intervalInMs = intervalInMs;\n        },\n        setErrorAsResult: this.setErrorAsResult,\n      });\n    }\n    options?.fireProgress?.(this.state);\n    return this;\n  }\n\n  async cancel(): Promise<PollOperation<TState, TResult>> {\n    logger.error(\"`cancelOperation` is deprecated because it wasn't implemented\");\n    return this;\n  }\n\n  /**\n   * Serializes the Poller operation.\n   */\n  public toString(): string {\n    return JSON.stringify({\n      state: this.state,\n    });\n  }\n}\n"]}