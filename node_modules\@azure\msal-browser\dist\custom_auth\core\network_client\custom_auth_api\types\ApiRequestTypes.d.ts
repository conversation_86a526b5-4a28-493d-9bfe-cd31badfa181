import { ApiRequestBase } from "./ApiTypesBase.js";
export interface SignInInitiateRequest extends ApiRequestBase {
    challenge_type: string;
    username: string;
}
export interface SignInChallengeRequest extends ApiRequestBase {
    challenge_type: string;
    continuation_token: string;
}
interface SignInTokenRequestBase extends ApiRequestBase {
    continuation_token: string;
    scope: string;
}
export interface SignInPasswordTokenRequest extends SignInTokenRequestBase {
    password: string;
}
export interface SignInOobTokenRequest extends SignInTokenRequestBase {
    oob: string;
}
export interface SignInContinuationTokenRequest extends SignInTokenRequestBase {
    username: string;
}
export interface SignUpStartRequest extends ApiRequestBase {
    username: string;
    challenge_type: string;
    password?: string;
    attributes?: Record<string, string>;
}
export interface SignUpChallengeRequest extends ApiRequestBase {
    continuation_token: string;
    challenge_type: string;
}
interface SignUpContinueRequestBase extends ApiRequestBase {
    continuation_token: string;
}
export interface SignUpContinueWithOobRequest extends SignUpContinueRequestBase {
    oob: string;
}
export interface SignUpContinueWithPasswordRequest extends SignUpContinueRequestBase {
    password: string;
}
export interface SignUpContinueWithAttributesRequest extends SignUpContinueRequestBase {
    attributes: Record<string, string>;
}
export interface ResetPasswordStartRequest extends ApiRequestBase {
    challenge_type: string;
    username: string;
}
export interface ResetPasswordChallengeRequest extends ApiRequestBase {
    challenge_type: string;
    continuation_token: string;
}
export interface ResetPasswordContinueRequest extends ApiRequestBase {
    continuation_token: string;
    oob: string;
}
export interface ResetPasswordSubmitRequest extends ApiRequestBase {
    continuation_token: string;
    new_password: string;
}
export interface ResetPasswordPollCompletionRequest extends ApiRequestBase {
    continuation_token: string;
}
export {};
//# sourceMappingURL=ApiRequestTypes.d.ts.map