{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/generated/api/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,2DAI8B;AAH5B,oHAAA,cAAc,OAAA;AAIhB,iDA0ByB;AAzBvB,kHAAA,iBAAiB,OAAA;AACjB,+GAAA,cAAc,OAAA;AACd,wHAAA,uBAAuB,OAAA;AACvB,qHAAA,oBAAoB,OAAA;AACpB,kHAAA,iBAAiB,OAAA;AACjB,gHAAA,eAAe,OAAA;AACf,8GAAA,aAAa,OAAA;AACb,+GAAA,cAAc,OAAA;AACd,wGAAA,OAAO,OAAA;AACP,0GAAA,SAAS,OAAA;AACT,wGAAA,OAAO,OAAA;AACP,uGAAA,MAAM,OAAA;AACN,qGAAA,IAAI,OAAA;AACJ,wGAAA,OAAO,OAAA;AACP,wGAAA,OAAO,OAAA;AACP,2GAAA,UAAU,OAAA;AACV,0GAAA,SAAS,OAAA;AACT,wGAAA,OAAO,OAAA;AACP,+GAAA,cAAc,OAAA;AACd,uGAAA,MAAM,OAAA;AACN,0GAAA,SAAS,OAAA;AACT,0GAAA,SAAS,OAAA;AACT,0GAAA,SAAS,OAAA;AACT,0GAAA,SAAS,OAAA;AACT,0GAAA,SAAS,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport {\n  createKeyVault,\n  KeyVaultContext,\n  KeyVaultClientOptionalParams,\n} from \"./keyVaultContext.js\";\nexport {\n  getKeyAttestation,\n  getRandomBytes,\n  updateKeyRotationPolicy,\n  getKeyRotationPolicy,\n  recoverDeletedKey,\n  purgeDeletedKey,\n  getDeletedKey,\n  getDeletedKeys,\n  release,\n  unwrapKey,\n  wrapKey,\n  verify,\n  sign,\n  decrypt,\n  encrypt,\n  restoreKey,\n  backupKey,\n  getKeys,\n  getKeyVersions,\n  getKey,\n  updateKey,\n  deleteKey,\n  importKey,\n  rotateKey,\n  createKey,\n} from \"./operations.js\";\nexport {\n  GetKeyAttestationOptionalParams,\n  GetRandomBytesOptionalParams,\n  UpdateKeyRotationPolicyOptionalParams,\n  GetKeyRotationPolicyOptionalParams,\n  RecoverDeletedKeyOptionalParams,\n  PurgeDeletedKeyOptionalParams,\n  GetDeletedKeyOptionalParams,\n  GetDeletedKeysOptionalParams,\n  ReleaseOptionalParams,\n  UnwrapKeyOptionalParams,\n  WrapKeyOptionalParams,\n  VerifyOptionalParams,\n  SignOptionalParams,\n  DecryptOptionalParams,\n  EncryptOptionalParams,\n  RestoreKeyOptionalParams,\n  BackupKeyOptionalParams,\n  GetKeysOptionalParams,\n  GetKeyVersionsOptionalParams,\n  GetKeyOptionalParams,\n  UpdateKeyOptionalParams,\n  DeleteKeyOptionalParams,\n  ImportKeyOptionalParams,\n  RotateKeyOptionalParams,\n  CreateKeyOptionalParams,\n} from \"./options.js\";\n"]}