{"version": 3, "file": "PerformanceEvent.mjs", "sources": ["../../../src/telemetry/performance/PerformanceEvent.ts"], "sourcesContent": [null], "names": [], "mappings": ";;AAAA;;;AAGG;AAEH;;;;;AAKG;AACU,MAAA,iBAAiB,GAAG;AAC7B;;;AAGG;AACH,IAAA,kBAAkB,EAAE,oBAAoB;AAExC;;;AAGG;AACH,IAAA,0BAA0B,EAAE,4BAA4B;AAExD;;;AAGG;AACH,IAAA,kBAAkB,EAAE,oBAAoB;AAExC;;;AAGG;AACH,IAAA,uBAAuB,EAAE,yBAAyB;AAElD;;;AAGG;AACH,IAAA,iBAAiB,EAAE,mBAAmB;AAEtC;;;;AAIG;AACH,IAAA,uBAAuB,EAAE,yBAAyB;AAElD;;;;AAIG;AACH,IAAA,oBAAoB,EAAE,sBAAsB;AAE5C;;;AAGG;AACH,IAAA,gCAAgC,EAAE,kCAAkC;AAEpE;;;AAGG;AACH,IAAA,iBAAiB,EAAE,mBAAmB;AAEtC;;;AAGG;AACH,IAAA,6BAA6B,EAAE,+BAA+B;AAE9D;;;AAGG;AACH,IAAA,8BAA8B,EAAE,gCAAgC;AAChE,IAAA,qBAAqB,EAAE,uBAAuB;AAE9C;;;AAGG;AACH,IAAA,+BAA+B,EAAE,iCAAiC;AAElE;;;AAGG;AACH,IAAA,SAAS,EAAE,WAAW;AAEtB;;;AAGG;AACH,IAAA,+CAA+C,EAC3C,iDAAiD;AAErD;;;AAGG;AACH,IAAA,8BAA8B,EAAE,gCAAgC;AAEhE;;;AAGG;AACH,IAAA,mCAAmC,EAAE,qCAAqC;AAC1E;;AAEG;AACH,IAAA,mCAAmC,EAAE,qCAAqC;AAC1E;;AAEG;AACH,IAAA,iCAAiC,EAAE,mCAAmC;AACtE,IAAA,4CAA4C,EACxC,8CAA8C;AAClD,IAAA,iDAAiD,EAC7C,mDAAmD;AACvD;;AAEG;AACH,IAAA,gBAAgB,EAAE,iBAAiB;AACnC;;AAEG;AACH,IAAA,kCAAkC,EAAE,oCAAoC;AACxE;;AAEG;AACH,IAAA,oBAAoB,EAAE,sBAAsB;AAE5C;;AAEG;AACH,IAAA,qCAAqC,EACjC,uCAAuC;AAE3C;;AAEG;AACH,IAAA,8BAA8B,EAAE,gCAAgC;AAEhE;;AAEG;AACH,IAAA,oDAAoD,EAChD,sDAAsD;AAE1D;;AAEG;AACH,IAAA,4CAA4C,EACxC,8CAA8C;AAElD;;AAEG;AACH,IAAA,wCAAwC,EACpC,0CAA0C;AAE9C;;;AAGG;AACH,IAAA,qBAAqB,EAAE,uBAAuB;AAC9C,IAAA,kCAAkC,EAAE,oCAAoC;AACxE,IAAA,6CAA6C,EACzC,+CAA+C;AAEnD;;;AAGG;AACH,IAAA,0BAA0B,EAAE,4BAA4B;AAExD;;AAEG;AACH,IAAA,qBAAqB,EAAE,uBAAuB;AAE9C;;AAEG;AACH,IAAA,uBAAuB,EAAE,yBAAyB;AAElD,IAAA,2BAA2B,EAAE,6BAA6B;AAE1D,IAAA,eAAe,EAAE,iBAAiB;AAElC;;AAEG;AACH,IAAA,6BAA6B,EAAE,+BAA+B;AAE9D;;AAEG;AACH,IAAA,gCAAgC,EAAE,kCAAkC;AACpE,IAAA,iCAAiC,EAAE,mCAAmC;AACtE,IAAA,sBAAsB,EAAE,wBAAwB;AAChD,IAAA,0BAA0B,EAAE,4BAA4B;AAExD;;AAEG;AACH,IAAA,6CAA6C,EACzC,+CAA+C;AACnD,IAAA,+CAA+C,EAC3C,iDAAiD;AACrD,IAAA,uDAAuD,EACnD,yDAAyD;AAE7D;;AAEG;AACH,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,iBAAiB,EAAE,mBAAmB;AAEtC;;AAEG;AACH,IAAA,4BAA4B,EAAE,8BAA8B;AAC5D,IAAA,kBAAkB,EAAE,oBAAoB;AACxC,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,4BAA4B,EAAE,8BAA8B;AAC5D,IAAA,kBAAkB,EAAE,oBAAoB;AACxC,IAAA,4BAA4B,EAAE,8BAA8B;AAE5D;;AAEG;AACH,IAAA,sBAAsB,EAAE,wBAAwB;AAChD,IAAA,6BAA6B,EAAE,+BAA+B;AAC9D,IAAA,gCAAgC,EAAE,kCAAkC;AAEpE;;AAEG;AACH,IAAA,mBAAmB,EAAE,qBAAqB;AAC1C,IAAA,mBAAmB,EAAE,qBAAqB;AAE1C;;AAEG;AACH,IAAA,yBAAyB,EAAE,2BAA2B;AACtD,IAAA,mBAAmB,EAAE,qBAAqB;AAE1C;;AAEG;AACH,IAAA,wCAAwC,EACpC,0CAA0C;AAC9C,IAAA,8BAA8B,EAAE,gCAAgC;AAChE,IAAA,yCAAyC,EACrC,2CAA2C;AAC/C,IAAA,6CAA6C,EACzC,+CAA+C;AACnD,IAAA,qCAAqC,EACjC,uCAAuC;AAC3C,IAAA,uCAAuC,EACnC,yCAAyC;AAC7C,IAAA,+BAA+B,EAAE,iCAAiC;AAClE,IAAA,8CAA8C,EAC1C,gDAAgD;AAEpD;;AAEG;AACH,IAAA,2BAA2B,EAAE,6BAA6B;AAC1D,IAAA,gCAAgC,EAAE,kCAAkC;AACpE,IAAA,gCAAgC,EAAE,kCAAkC;AAEpE,IAAA,uBAAuB,EAAE,yBAAyB;AAElD,IAAA,8BAA8B,EAAE,gCAAgC;AAChE,IAAA,+CAA+C,EAC3C,iDAAiD;AAErD,IAAA,gCAAgC,EAAE,uBAAuB;AACzD,IAAA,sCAAsC,EAAE,6BAA6B;AAErE,IAAA,uCAAuC,EACnC,yCAAyC;AAE7C,IAAA,kCAAkC,EAAE,oCAAoC;AAExE,IAAA,6BAA6B,EAAE,+BAA+B;AAE9D,IAAA,wBAAwB,EAAE,0BAA0B;AAEpD,IAAA,kBAAkB,EAAE,oBAAoB;AAExC;;AAEG;AACH,IAAA,4BAA4B,EAAE,8BAA8B;AAC5D,IAAA,2BAA2B,EAAE,6BAA6B;AAC1D,IAAA,mBAAmB,EAAE,qBAAqB;AAC1C,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,mBAAmB,EAAE,qBAAqB;AAE1C;;AAEG;AACH,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,oBAAoB,EAAE,sBAAsB;AAC5C,IAAA,iCAAiC,EAAE,mCAAmC;AACtE,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,eAAe,EAAE,iBAAiB;AAClC,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,eAAe,EAAE,iBAAiB;AAClC,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,kBAAkB,EAAE,oBAAoB;EACjC;AAIE,MAAA,6BAA6B,GACtC,IAAI,GAAG,CAAC;AACJ,IAAA,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC;AAClD,IAAA,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,QAAQ,CAAC;AACxD,IAAA,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,CAAC;AAC7C,IAAA,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,UAAU,CAAC;AACvD,IAAA,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,SAAS,CAAC;AAChD,IAAA,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,YAAY,CAAC;AACtD,IAAA;AACI,QAAA,iBAAiB,CAAC,gCAAgC;QAClD,kBAAkB;AACrB,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,eAAe,CAAC;AACtD,IAAA,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,kBAAkB,CAAC;AACrE,IAAA,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,mBAAmB,CAAC;AACvE,IAAA,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,cAAc,CAAC;AACnE,IAAA,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC;AACvC,IAAA;AACI,QAAA,iBAAiB,CAAC,+CAA+C;QACjE,yBAAyB;AAC5B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,8BAA8B;QAChD,yBAAyB;AAC5B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,mCAAmC;QACrD,gBAAgB;AACnB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,mCAAmC;QACrD,0BAA0B;AAC7B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,iCAAiC;QACnD,mBAAmB;AACtB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,4CAA4C;QAC9D,kBAAkB;AACrB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,iDAAiD;QACnE,wBAAwB;AAC3B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;AACvD,IAAA;AACI,QAAA,iBAAiB,CAAC,kCAAkC;QACpD,gBAAgB;AACnB,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,YAAY,CAAC;AACtD,IAAA;AACI,QAAA,iBAAiB,CAAC,qCAAqC;QACvD,kBAAkB;AACrB,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,YAAY,CAAC;AAChE,IAAA;AACI,QAAA,iBAAiB,CAAC,oDAAoD;QACtE,wBAAwB;AAC3B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,4CAA4C;QAC9D,gBAAgB;AACnB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,wCAAwC;QAC1D,wBAAwB;AAC3B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,aAAa,CAAC;AACxD,IAAA;AACI,QAAA,iBAAiB,CAAC,kCAAkC;QACpD,uBAAuB;AAC1B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,6CAA6C;QAC/D,8BAA8B;AACjC,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,CAAC;AAC/D,IAAA,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,aAAa,CAAC;AACxD,IAAA,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,YAAY,CAAC;AACzD,IAAA;AACI,QAAA,iBAAiB,CAAC,2BAA2B;QAC7C,uBAAuB;AAC1B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,eAAe,EAAE,WAAW,CAAC;AAChD,IAAA,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,aAAa,CAAC;AACtD,IAAA,CAAC,iBAAiB,CAAC,WAAW,EAAE,aAAa,CAAC;AAC9C,IAAA,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,qBAAqB,CAAC;AAC9D,IAAA,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,iBAAiB,CAAC;AACpE,IAAA;AACI,QAAA,iBAAiB,CAAC,gCAAgC;QAClD,qBAAqB;AACxB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,iCAAiC;QACnD,gCAAgC;AACnC,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,mBAAmB,CAAC;AAC/D,IAAA,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,uBAAuB,CAAC;AAEvE,IAAA;AACI,QAAA,iBAAiB,CAAC,6CAA6C;QAC/D,kCAAkC;AACrC,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,+CAA+C;QACjE,2BAA2B;AAC9B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,uDAAuD;QACzE,yBAAyB;AAC5B,KAAA;AAED,IAAA,CAAC,iBAAiB,CAAC,cAAc,EAAE,gBAAgB,CAAC;AAEpD,IAAA;AACI,QAAA,iBAAiB,CAAC,4BAA4B;QAC9C,yBAAyB;AAC5B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AACxD,IAAA,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,eAAe,CAAC;AACtD,IAAA,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AACxD,IAAA;AACI,QAAA,iBAAiB,CAAC,4BAA4B;QAC9C,sBAAsB;AACzB,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,kBAAkB,CAAC;AAEpE,IAAA,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,cAAc,CAAC;AAC1D,IAAA,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,oBAAoB,CAAC;AACvE,IAAA;AACI,QAAA,iBAAiB,CAAC,gCAAgC;QAClD,0BAA0B;AAC7B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC;AACrD,IAAA,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC;AACrD,IAAA,CAAC,iBAAiB,CAAC,yBAAyB,EAAE,kBAAkB,CAAC;AACjE,IAAA,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,gBAAgB,CAAC;AACzD,IAAA;AACI,QAAA,iBAAiB,CAAC,wCAAwC;QAC1D,wBAAwB;AAC3B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,8BAA8B;QAChD,2BAA2B;AAC9B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,yCAAyC;QAC3D,+BAA+B;AAClC,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,6CAA6C;QAC/D,sBAAsB;AACzB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,qCAAqC;QACvD,eAAe;AAClB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,uCAAuC;QACzD,sBAAsB;AACzB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,+BAA+B;QACjD,qBAAqB;AACxB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,8CAA8C;QAChE,wBAAwB;AAC3B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,2BAA2B,EAAE,kBAAkB,CAAC;AACnE,IAAA;AACI,QAAA,iBAAiB,CAAC,gCAAgC;QAClD,uBAAuB;AAC1B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,gCAAgC;QAClD,sBAAsB;AACzB,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,eAAe,CAAC;AAC5D,IAAA;AACI,QAAA,iBAAiB,CAAC,8BAA8B;QAChD,wBAAwB;AAC3B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,+CAA+C;QACjE,kBAAkB;AACrB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,gCAAgC;QAClD,uBAAuB;AAC1B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,sCAAsC;QACxD,0BAA0B;AAC7B,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,uCAAuC;QACzD,cAAc;AACjB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,kCAAkC;QACpD,kBAAkB;AACrB,KAAA;AACD,IAAA;AACI,QAAA,iBAAiB,CAAC,6BAA6B;QAC/C,wBAAwB;AAC3B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,wBAAwB,EAAE,eAAe,CAAC;AAC7D,IAAA,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;AAC5D,IAAA;AACI,QAAA,iBAAiB,CAAC,4BAA4B;QAC9C,yBAAyB;AAC5B,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,2BAA2B,EAAE,mBAAmB,CAAC;AACpE,IAAA,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,cAAc,CAAC;AACrD,IAAA,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;AAC3D,IAAA;AACI,QAAA,iBAAiB,CAAC,iCAAiC;QACnD,8BAA8B;AACjC,KAAA;AACD,IAAA,CAAC,iBAAiB,CAAC,YAAY,EAAE,cAAc,CAAC;AAChD,IAAA,CAAC,iBAAiB,CAAC,eAAe,EAAE,iBAAiB,CAAC;AACtD,IAAA,CAAC,iBAAiB,CAAC,YAAY,EAAE,SAAS,CAAC;AAC3C,IAAA,CAAC,iBAAiB,CAAC,eAAe,EAAE,YAAY,CAAC;AACjD,IAAA,CAAC,iBAAiB,CAAC,YAAY,EAAE,WAAW,CAAC;AAC7C,IAAA,CAAC,iBAAiB,CAAC,YAAY,EAAE,WAAW,CAAC;AAC7C,IAAA,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC;AACtC,IAAA,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC;AACtC,IAAA,CAAC,iBAAiB,CAAC,cAAc,EAAE,WAAW,CAAC;AAC/C,IAAA,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AAC3D,CAAA,EAAE;AAEP;;;;;AAKG;AACU,MAAA,sBAAsB,GAAG;AAClC,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,SAAS,EAAE,CAAC;EACL;AAyVE,MAAA,SAAS,GAAwB,IAAI,GAAG,CAAC;IAClD,iBAAiB;IACjB,YAAY;IACZ,aAAa;IACb,kBAAkB;IAClB,gBAAgB;IAChB,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,uBAAuB;IACvB,4BAA4B;AAC/B,CAAA;;;;"}