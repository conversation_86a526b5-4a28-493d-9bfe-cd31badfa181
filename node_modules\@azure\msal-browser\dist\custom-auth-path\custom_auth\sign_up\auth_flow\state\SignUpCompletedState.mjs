/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { SignInContinuationState } from '../../../sign_in/auth_flow/state/SignInContinuationState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Represents the state of a sign-up operation that has been completed scuccessfully.
 */
class SignUpCompletedState extends SignInContinuationState {
}

export { SignUpCompletedState };
//# sourceMappingURL=SignUpCompletedState.mjs.map
