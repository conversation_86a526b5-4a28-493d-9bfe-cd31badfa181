/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const CONTINUATION_TOKEN_MISSING = "continuation_token_missing";
const INVALID_RESPONSE_BODY = "invalid_response_body";
const UNSUPPORTED_CHALLENGE_TYPE = "unsupported_challenge_type";
const ACCESS_TOKEN_MISSING = "access_token_missing";
const ID_TOKEN_MISSING = "id_token_missing";
const REFRESH_TOKEN_MISSING = "refresh_token_missing";
const INVALID_EXPIRES_IN = "invalid_expires_in";
const INVALID_TOKEN_TYPE = "invalid_token_type";
const HTTP_REQUEST_FAILED = "http_request_failed";
const INVALID_REQUEST = "invalid_request";
const USER_NOT_FOUND = "user_not_found";
const INVALID_GRANT = "invalid_grant";
const CREDENTIAL_REQUIRED = "credential_required";
const ATTRIBUTES_REQUIRED = "attributes_required";
const USER_ALREADY_EXISTS = "user_already_exists";
const INVALID_POLL_STATUS = "invalid_poll_status";
const PASSWORD_CHANGE_FAILED = "password_change_failed";
const PASSWORD_RESET_TIMEOUT = "password_reset_timeout";
const CLIENT_INFO_MISSING = "client_info_missing";
const EXPIRED_TOKEN = "expired_token";

export { ACCESS_TOKEN_MISSING, ATTRIBUTES_REQUIRED, CLIENT_INFO_MISSING, CONTINUATION_TOKEN_MISSING, CREDENTIAL_REQUIRED, EXPIRED_TOKEN, HTTP_REQUEST_FAILED, ID_TOKEN_MISSING, INVALID_EXPIRES_IN, INVALID_GRANT, INVALID_POLL_STATUS, INVALID_REQUEST, INVALID_RESPONSE_BODY, INVALID_TOKEN_TYPE, PASSWORD_CHANGE_FAILED, PASSWORD_RESET_TIMEOUT, REFRESH_TOKEN_MISSING, UNSUPPORTED_CHALLENGE_TYPE, USER_ALREADY_EXISTS, USER_NOT_FOUND };
//# sourceMappingURL=ApiErrorCodes.mjs.map
