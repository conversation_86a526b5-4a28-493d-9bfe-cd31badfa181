/*! @azure/msal-common v15.9.0 2025-07-23 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const CLIENT_ID = "client_id";
const REDIRECT_URI = "redirect_uri";
const RESPONSE_TYPE = "response_type";
const RESPONSE_MODE = "response_mode";
const GRANT_TYPE = "grant_type";
const CLAIMS = "claims";
const SCOPE = "scope";
const ERROR = "error";
const ERROR_DESCRIPTION = "error_description";
const ACCESS_TOKEN = "access_token";
const ID_TOKEN = "id_token";
const REFRESH_TOKEN = "refresh_token";
const EXPIRES_IN = "expires_in";
const REFRESH_TOKEN_EXPIRES_IN = "refresh_token_expires_in";
const STATE = "state";
const NONCE = "nonce";
const PROMPT = "prompt";
const SESSION_STATE = "session_state";
const CLIENT_INFO = "client_info";
const CODE = "code";
const CODE_CHALLENGE = "code_challenge";
const CODE_CHALLENGE_METHOD = "code_challenge_method";
const CODE_VERIFIER = "code_verifier";
const CLIENT_REQUEST_ID = "client-request-id";
const X_CLIENT_SKU = "x-client-SKU";
const X_CLIENT_VER = "x-client-VER";
const X_CLIENT_OS = "x-client-OS";
const X_CLIENT_CPU = "x-client-CPU";
const X_CLIENT_CURR_TELEM = "x-client-current-telemetry";
const X_CLIENT_LAST_TELEM = "x-client-last-telemetry";
const X_MS_LIB_CAPABILITY = "x-ms-lib-capability";
const X_APP_NAME = "x-app-name";
const X_APP_VER = "x-app-ver";
const POST_LOGOUT_URI = "post_logout_redirect_uri";
const ID_TOKEN_HINT = "id_token_hint";
const DEVICE_CODE = "device_code";
const CLIENT_SECRET = "client_secret";
const CLIENT_ASSERTION = "client_assertion";
const CLIENT_ASSERTION_TYPE = "client_assertion_type";
const TOKEN_TYPE = "token_type";
const REQ_CNF = "req_cnf";
const OBO_ASSERTION = "assertion";
const REQUESTED_TOKEN_USE = "requested_token_use";
const ON_BEHALF_OF = "on_behalf_of";
const FOCI = "foci";
const CCS_HEADER = "X-AnchorMailbox";
const RETURN_SPA_CODE = "return_spa_code";
const NATIVE_BROKER = "nativebroker";
const LOGOUT_HINT = "logout_hint";
const SID = "sid";
const LOGIN_HINT = "login_hint";
const DOMAIN_HINT = "domain_hint";
const X_CLIENT_EXTRA_SKU = "x-client-xtra-sku";
const BROKER_CLIENT_ID = "brk_client_id";
const BROKER_REDIRECT_URI = "brk_redirect_uri";
const INSTANCE_AWARE = "instance_aware";
const EAR_JWK = "ear_jwk";
const EAR_JWE_CRYPTO = "ear_jwe_crypto";

export { ACCESS_TOKEN, BROKER_CLIENT_ID, BROKER_REDIRECT_URI, CCS_HEADER, CLAIMS, CLIENT_ASSERTION, CLIENT_ASSERTION_TYPE, CLIENT_ID, CLIENT_INFO, CLIENT_REQUEST_ID, CLIENT_SECRET, CODE, CODE_CHALLENGE, CODE_CHALLENGE_METHOD, CODE_VERIFIER, DEVICE_CODE, DOMAIN_HINT, EAR_JWE_CRYPTO, EAR_JWK, ERROR, ERROR_DESCRIPTION, EXPIRES_IN, FOCI, GRANT_TYPE, ID_TOKEN, ID_TOKEN_HINT, INSTANCE_AWARE, LOGIN_HINT, LOGOUT_HINT, NATIVE_BROKER, NONCE, OBO_ASSERTION, ON_BEHALF_OF, POST_LOGOUT_URI, PROMPT, REDIRECT_URI, REFRESH_TOKEN, REFRESH_TOKEN_EXPIRES_IN, REQUESTED_TOKEN_USE, REQ_CNF, RESPONSE_MODE, RESPONSE_TYPE, RETURN_SPA_CODE, SCOPE, SESSION_STATE, SID, STATE, TOKEN_TYPE, X_APP_NAME, X_APP_VER, X_CLIENT_CPU, X_CLIENT_CURR_TELEM, X_CLIENT_EXTRA_SKU, X_CLIENT_LAST_TELEM, X_CLIENT_OS, X_CLIENT_SKU, X_CLIENT_VER, X_MS_LIB_CAPABILITY };
//# sourceMappingURL=AADServerParamKeys.mjs.map
