/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { SignInSubmitCodeError } from '../error_type/SignInError.mjs';
import { SignInCompletedState } from '../state/SignInCompletedState.mjs';
import { SignInFailedState } from '../state/SignInFailedState.mjs';
import { SignInSubmitCredentialResult } from './SignInSubmitCredentialResult.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a sign-in submit code operation.
 */
class SignInSubmitCodeResult extends SignInSubmitCredentialResult {
    /**
     * Creates a new instance of SignInSubmitCodeResult with error data.
     * @param error The error that occurred.
     * @returns {SignInSubmitCodeResult} A new instance of SignInSubmitCodeResult with the error set.
     */
    static createWithError(error) {
        const result = new SignInSubmitCodeResult(new SignInFailedState());
        result.error = new SignInSubmitCodeError(SignInSubmitCodeResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignInFailedState;
    }
    /**
     * Checks if the result is in a completed state.
     */
    isCompleted() {
        return this.state instanceof SignInCompletedState;
    }
}

export { SignInSubmitCodeResult };
//# sourceMappingURL=SignInSubmitCodeResult.mjs.map
