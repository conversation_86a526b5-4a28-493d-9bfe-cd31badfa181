/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const SIGN_IN_CODE_SEND_RESULT_TYPE = "SignInCodeSendResult";
const SIGN_IN_PASSWORD_REQUIRED_RESULT_TYPE = "SignInPasswordRequiredResult";
const SIGN_IN_COMPLETED_RESULT_TYPE = "SignInCompletedResult";
function createSignInCompleteResult(input) {
    return {
        type: SIGN_IN_COMPLETED_RESULT_TYPE,
        ...input,
    };
}
function createSignInPasswordRequiredResult(input) {
    return {
        type: SIGN_IN_PASSWORD_REQUIRED_RESULT_TYPE,
        ...input,
    };
}
function createSignInCodeSendResult(input) {
    return {
        type: SIGN_IN_CODE_SEND_RESULT_TYPE,
        ...input,
    };
}

export { SIGN_IN_CODE_SEND_RESULT_TYPE, SIGN_IN_COMPLETED_RESULT_TYPE, SIGN_IN_PASSWORD_REQUIRED_RESULT_TYPE, createSignInCodeSendResult, createSignInCompleteResult, createSignInPasswordRequiredResult };
//# sourceMappingURL=SignInActionResult.mjs.map
