{"version": 3, "file": "transformations.js", "sourceRoot": "", "sources": ["../../src/transformations.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAWlC,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AAY7D;;;GAGG;AACH,MAAM,UAAU,mBAAmB,CACjC,MAAoC;IAEpC,MAAM,SAAS,GAAG,MAAmB,CAAC;IACtC,MAAM,gBAAgB,GAAG,MAA0B,CAAC;IAEpD,MAAM,QAAQ,GAAG,0BAA0B,CAAC,SAAS,CAAC,GAAI,CAAC,GAAI,CAAC,CAAC;IAEjE,MAAM,UAAU,GAAkB,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC;IAE7D,MAAM,YAAY,GAA6B;QAC7C,GAAG,EAAE,SAAS,CAAC,GAAG;QAClB,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;QACjD,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,aAAa,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;QAC/D,OAAO,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;QACtD,UAAU,EAAE;YACV,IAAI,EAAE,SAAS,CAAC,IAAI;YAEpB,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,SAAS,EAAE,UAAU,CAAC,OAAO;YAC7B,SAAS,EAAE,UAAU,CAAC,OAAO;YAC7B,SAAS,EAAE,UAAU,CAAC,OAAO;YAC7B,eAAe,EAAE,UAAU,CAAC,eAAe;YAC3C,aAAa,EAAE,UAAU,CAAC,aAAa;YACvC,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,aAAa,EAAE,SAAS,CAAC,aAAa;YACtC,WAAW,EAAE,UAAU,CAAC,WAAW;YAEnC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,OAAO,EAAE,SAAS,CAAC,OAAO;YAE1B,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;SAClD;KACF,CAAC;IAEF,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAC/B,YAAoB,CAAC,UAAU,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;QACzE,YAAoB,CAAC,UAAU,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;QACzF,YAAoB,CAAC,UAAU,CAAC,SAAS,GAAG,gBAAgB,CAAC,WAAW,CAAC;IAC5E,CAAC;IAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,YAAY,CAAC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,+BAA+B,CAAC,OAAuB;IACrE,MAAM,gBAAgB,GAAG,2BAA2B,CAAC,OAAO,CAAC,CAAC;IAE9D,OAAO;QACL,GAAG,EAAE;YACH,GAAG,EAAE,OAAO,CAAC,GAAG;SACjB;QACD,EAAE,EAAE,OAAO,CAAC,GAAG;QACf,IAAI,EAAE,gBAAgB,CAAC,IAAI;QAC3B,UAAU,kCACL,gBAAgB,KACnB,UAAU,EAAE,OAAO,CAAC,UAAU,EAC9B,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAC9C,SAAS,EAAE,OAAO,CAAC,WAAW,GAC/B;KACF,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,2BAA2B,CAAC,OAAgB;IAC1D,MAAM,QAAQ,GAAG,0BAA0B,CAAC,OAAO,CAAC,GAAI,CAAC,CAAC;IAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;IAE5C,MAAM,YAAY,GAAkB;QAClC,SAAS,EAAE,UAAU,CAAC,OAAO;QAC7B,OAAO,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO;QAC5B,SAAS,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO;QAC9B,EAAE,EAAE,OAAO,CAAC,GAAG;QACf,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,SAAS,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,SAAS;QAChC,eAAe,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,eAAe;QAC5C,aAAa,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,aAAa;QACxC,WAAW,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,WAAW;QACpC,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,SAAS,EAAE,UAAU,CAAC,OAAO;QAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;KAC1B,CAAC;IAEF,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,MAAM,gCAAgC,GAA4C;IAChF,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACjB,CAAC;AAEF,SAAS,uBAAuB,CAAC,yBAAiC;IAChE,MAAM,MAAM,GAAG,gCAAgC,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC,CAAC;IACzF,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,6BAA6B,yBAAyB,EAAE,CAAC,CAAC;AAC5E,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG;IACxC,qBAAqB,EAAE,UACrB,UAAuC;;QAEvC,MAAM,MAAM,GAAoB;YAC9B,UAAU,EAAE;gBACV,UAAU,EAAE,UAAU,CAAC,SAAS;aACjC;YACD,eAAe,EAAE,MAAA,UAAU,CAAC,eAAe,0CAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,MAAM,eAAe,GAAoB;oBACvC,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE;oBAC/B,OAAO,EAAE,EAAE;iBACZ,CAAC;gBAEF,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC3B,eAAe,CAAC,OAAQ,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;gBACpE,CAAC;gBAED,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBAC5B,eAAe,CAAC,OAAQ,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;gBACtE,CAAC;gBAED,OAAO,eAAe,CAAC;YACzB,CAAC,CAAC;SACH,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,iBAAiB,CAAC,SAA0B;;QAC1C,MAAM,MAAM,GAAsB;YAChC,EAAE,EAAE,SAAS,CAAC,EAAE;YAChB,SAAS,EAAE,MAAA,SAAS,CAAC,UAAU,0CAAE,OAAO;YACxC,SAAS,EAAE,MAAA,SAAS,CAAC,UAAU,0CAAE,OAAO;YACxC,SAAS,EAAE,MAAA,SAAS,CAAC,UAAU,0CAAE,UAAU;YAC3C,eAAe,EAAE,MAAA,SAAS,CAAC,eAAe,0CAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;;gBACzD,OAAO;oBACL,MAAM,EAAE,uBAAuB,CAAC,MAAM,CAAC,MAAO,CAAC,IAAK,CAAC;oBACrD,eAAe,EAAE,MAAA,MAAM,CAAC,OAAO,0CAAE,eAAe;oBAChD,gBAAgB,EAAE,MAAA,MAAM,CAAC,OAAO,0CAAE,gBAAgB;iBACnD,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,UAAU,qBAAqB,CAKnC,OAAiB,EACjB,SAAwE,EACxE,MAAkC;IAElC,IAAI,IAAI,GAA6C,SAAS,CAAC;IAC/D,OAAO;QACL,KAAK,CAAC,IAAI;YACR,IAAI,aAAJ,IAAI,cAAJ,IAAI,IAAJ,IAAI,GAAK,SAAS,iCAAM,OAAO,KAAE,UAAU,EAAE,SAAS,IAAG,EAAC;YAC1D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAEjC,uCACK,MAAM,KACT,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAC3C;QACJ,CAAC;QACD,CAAC,MAAM,CAAC,aAAa,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACM,MAAM,CAA4D,QAAoB;;;gBAC3F,8DAA8D;gBAC9D,MAAM,cAAc,GAAG,SAAS,iCAAM,OAAO,KAAE,UAAU,EAAE,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,WAAW,IAAG,CAAC,MAAM,CACxF,QAAQ,CACT,CAAC;;oBACF,KAAyB,eAAA,mBAAA,cAAA,cAAc,CAAA,oBAAA,6GAAE,CAAC;wBAAjB,8BAAc;wBAAd,WAAc;wBAA5B,MAAM,IAAI,KAAA,CAAA;wBACnB,oBAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA,CAAC;oBACzB,CAAC;;;;;;;;;YACH,CAAC;SAAA;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  DeletedKeyBundle,\n  DeletedKeyItem,\n  KeyRotationPolicy as GeneratedPolicy,\n  KeyAttributes,\n  KeyBundle,\n  KeyItem,\n  LifetimeActions,\n} from \"./generated/models/index.js\";\nimport { parseKeyVaultKeyIdentifier } from \"./identifier.js\";\nimport type {\n  DeletedKey,\n  KeyProperties,\n  KeyRotationPolicy,\n  KeyRotationPolicyAction,\n  KeyRotationPolicyProperties,\n  KeyVaultKey,\n} from \"./keysModels.js\";\nimport type { PagedAsyncIterableIterator, PageSettings } from \"./generated/index.js\";\nimport type { OperationOptions } from \"@azure-rest/core-client\";\n\n/**\n * @internal\n * Shapes the exposed {@link KeyVaultKey} based on either a received key bundle or deleted key bundle.\n */\nexport function getKeyFromKeyBundle(\n  bundle: KeyBundle | DeletedKeyBundle,\n): KeyVaultKey | DeletedKey {\n  const keyBundle = bundle as KeyBundle;\n  const deletedKeyBundle = bundle as DeletedKeyBundle;\n\n  const parsedId = parseKeyVaultKeyIdentifier(keyBundle.key!.kid!);\n\n  const attributes: KeyAttributes = keyBundle.attributes || {};\n\n  const resultObject: KeyVaultKey | DeletedKey = {\n    key: keyBundle.key,\n    id: keyBundle.key ? keyBundle.key.kid : undefined,\n    name: parsedId.name,\n    keyOperations: keyBundle.key ? keyBundle.key.keyOps : undefined,\n    keyType: keyBundle.key ? keyBundle.key.kty : undefined,\n    properties: {\n      tags: keyBundle.tags,\n\n      enabled: attributes.enabled,\n      notBefore: attributes.notBefore,\n      expiresOn: attributes.expires,\n      createdOn: attributes.created,\n      updatedOn: attributes.updated,\n      recoverableDays: attributes.recoverableDays,\n      recoveryLevel: attributes.recoveryLevel,\n      exportable: attributes.exportable,\n      releasePolicy: keyBundle.releasePolicy,\n      hsmPlatform: attributes.hsmPlatform,\n\n      vaultUrl: parsedId.vaultUrl,\n      version: parsedId.version,\n      name: parsedId.name,\n      managed: keyBundle.managed,\n\n      id: keyBundle.key ? keyBundle.key.kid : undefined,\n    },\n  };\n\n  if (deletedKeyBundle.recoveryId) {\n    (resultObject as any).properties.recoveryId = deletedKeyBundle.recoveryId;\n    (resultObject as any).properties.scheduledPurgeDate = deletedKeyBundle.scheduledPurgeDate;\n    (resultObject as any).properties.deletedOn = deletedKeyBundle.deletedDate;\n  }\n\n  if (attributes.attestation) {\n    resultObject.properties.attestation = attributes.attestation;\n  }\n\n  return resultObject;\n}\n\n/**\n * @internal\n * Shapes the exposed {@link DeletedKey} based on a received KeyItem.\n */\nexport function getDeletedKeyFromDeletedKeyItem(keyItem: DeletedKeyItem): DeletedKey {\n  const commonProperties = getKeyPropertiesFromKeyItem(keyItem);\n\n  return {\n    key: {\n      kid: keyItem.kid,\n    },\n    id: keyItem.kid,\n    name: commonProperties.name,\n    properties: {\n      ...commonProperties,\n      recoveryId: keyItem.recoveryId,\n      scheduledPurgeDate: keyItem.scheduledPurgeDate,\n      deletedOn: keyItem.deletedDate,\n    },\n  };\n}\n\n/**\n * @internal\n * Shapes the exposed {@link KeyProperties} based on a received KeyItem.\n */\nexport function getKeyPropertiesFromKeyItem(keyItem: KeyItem): KeyProperties {\n  const parsedId = parseKeyVaultKeyIdentifier(keyItem.kid!);\n  const attributes = keyItem.attributes || {};\n\n  const resultObject: KeyProperties = {\n    createdOn: attributes.created,\n    enabled: attributes?.enabled,\n    expiresOn: attributes?.expires,\n    id: keyItem.kid,\n    managed: keyItem.managed,\n    name: parsedId.name,\n    notBefore: attributes?.notBefore,\n    recoverableDays: attributes?.recoverableDays,\n    recoveryLevel: attributes?.recoveryLevel,\n    hsmPlatform: attributes?.hsmPlatform,\n    tags: keyItem.tags,\n    updatedOn: attributes.updated,\n    vaultUrl: parsedId.vaultUrl,\n    version: parsedId.version,\n  };\n\n  return resultObject;\n}\n\nconst actionTypeCaseInsensitiveMapping: Record<string, KeyRotationPolicyAction> = {\n  rotate: \"Rotate\",\n  notify: \"Notify\",\n};\n\nfunction getNormalizedActionType(caseInsensitiveActionType: string): KeyRotationPolicyAction {\n  const result = actionTypeCaseInsensitiveMapping[caseInsensitiveActionType.toLowerCase()];\n  if (result) {\n    return result;\n  }\n\n  throw new Error(`Unrecognized action type: ${caseInsensitiveActionType}`);\n}\n\n/**\n * @internal\n */\nexport const keyRotationTransformations = {\n  propertiesToGenerated: function (\n    parameters: KeyRotationPolicyProperties,\n  ): Partial<GeneratedPolicy> {\n    const policy: GeneratedPolicy = {\n      attributes: {\n        expiryTime: parameters.expiresIn,\n      },\n      lifetimeActions: parameters.lifetimeActions?.map((action) => {\n        const generatedAction: LifetimeActions = {\n          action: { type: action.action },\n          trigger: {},\n        };\n\n        if (action.timeAfterCreate) {\n          generatedAction.trigger!.timeAfterCreate = action.timeAfterCreate;\n        }\n\n        if (action.timeBeforeExpiry) {\n          generatedAction.trigger!.timeBeforeExpiry = action.timeBeforeExpiry;\n        }\n\n        return generatedAction;\n      }),\n    };\n    return policy;\n  },\n  generatedToPublic(generated: GeneratedPolicy): KeyRotationPolicy {\n    const policy: KeyRotationPolicy = {\n      id: generated.id,\n      createdOn: generated.attributes?.created,\n      updatedOn: generated.attributes?.updated,\n      expiresIn: generated.attributes?.expiryTime,\n      lifetimeActions: generated.lifetimeActions?.map((action) => {\n        return {\n          action: getNormalizedActionType(action.action!.type!),\n          timeAfterCreate: action.trigger?.timeAfterCreate,\n          timeBeforeExpiry: action.trigger?.timeBeforeExpiry,\n        };\n      }),\n    };\n    return policy;\n  },\n};\n\n/**\n * A helper supporting compatibility between modular and legacy paged async iterables.\n *\n * Provides the following compatibility:\n * 1. Maps the values of the paged async iterable using the provided mapper function.\n * 2. Supports `maxPageSize` operation on the paged async iterable.\n *\n * TODO: move this to keyvault-common once everything is merged\n */\nexport function mapPagedAsyncIterable<\n  TGenerated,\n  TPublic,\n  TOptions extends OperationOptions & { maxresults?: number },\n>(\n  options: TOptions,\n  operation: (options: TOptions) => PagedAsyncIterableIterator<TGenerated>,\n  mapper: (x: TGenerated) => TPublic,\n): PagedAsyncIterableIterator<TPublic> {\n  let iter: ReturnType<typeof operation> | undefined = undefined;\n  return {\n    async next() {\n      iter ??= operation({ ...options, maxresults: undefined });\n      const result = await iter.next();\n\n      return {\n        ...result,\n        value: result.value && mapper(result.value),\n      };\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n    async *byPage<TSettings extends PageSettings & { maxPageSize?: number }>(settings?: TSettings) {\n      // Pass the maxPageSize value to the underlying page operation\n      const iteratorByPage = operation({ ...options, maxresults: settings?.maxPageSize }).byPage(\n        settings,\n      );\n      for await (const page of iteratorByPage) {\n        yield page.map(mapper);\n      }\n    },\n  };\n}\n"]}