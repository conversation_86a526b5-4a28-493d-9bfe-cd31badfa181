{"version": 3, "file": "HttpClientWithRetries.mjs", "sources": ["../../src/network/HttpClientWithRetries.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;AAAA;;;AAGG;MAYU,qBAAqB,CAAA;AAK9B,IAAA,WAAA,CACI,mBAAmC,EACnC,WAA6B,EAC7B,MAAc,EAAA;AAEd,QAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAC/C,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAEO,IAAA,MAAM,6BAA6B,CACvC,UAAsB,EACtB,GAAW,EACX,OAA+B,EAAA;AAE/B,QAAA,IAAI,UAAU,KAAK,UAAU,CAAC,GAAG,EAAE;YAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrE,SAAA;AAAM,aAAA;YACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACtE,SAAA;KACJ;AAEO,IAAA,MAAM,uBAAuB,CACjC,UAAsB,EACtB,GAAW,EACX,OAA+B,EAAA;;AAG/B,QAAA,IAAI,QAAQ,GACR,MAAM,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAEvE,QAAA,IAAI,cAAc,IAAI,IAAI,CAAC,WAAW,EAAE;AACpC,YAAA,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;AACxC,SAAA;QAED,IAAI,YAAY,GAAW,CAAC,CAAC;QAC7B,OACI,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAChC,QAAQ,CAAC,MAAM,EACf,YAAY,EACZ,IAAI,CAAC,MAAM,EACX,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAC5C,EACH;AACE,YAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAC/C,UAAU,EACV,GAAG,EACH,OAAO,CACV,CAAC;AACF,YAAA,YAAY,EAAE,CAAC;AAClB,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB;AAEM,IAAA,MAAM,mBAAmB,CAC5B,GAAW,EACX,OAA+B,EAAA;AAE/B,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;KACrE;AAEM,IAAA,MAAM,oBAAoB,CAC7B,GAAW,EACX,OAA+B,EAAA;AAE/B,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;KACtE;AACJ;;;;"}