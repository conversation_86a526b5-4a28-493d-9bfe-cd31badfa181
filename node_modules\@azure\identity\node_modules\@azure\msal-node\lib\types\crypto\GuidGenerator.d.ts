import { IGuidGenerator } from "@azure/msal-common/node";
export declare class GuidGenerator implements IGuidGenerator {
    /**
     *
     * RFC4122: The version 4 UUID is meant for generating UUIDs from truly-random or pseudo-random numbers.
     * uuidv4 generates guids from cryprtographically-string random
     */
    generateGuid(): string;
    /**
     * verifies if a string is  GUID
     * @param guid
     */
    isGuid(guid: string): boolean;
}
//# sourceMappingURL=GuidGenerator.d.ts.map