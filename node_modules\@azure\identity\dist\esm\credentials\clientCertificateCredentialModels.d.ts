/**
 * Required configuration options for the {@link ClientCertificateCredential}, with the string contents of a PEM certificate
 */
export interface ClientCertificatePEMCertificate {
    /**
     * The PEM-encoded public/private key certificate on the filesystem.
     * Ensure that certificate is in PEM format and contains both the public and private keys.
     */
    certificate: string;
    /**
     * The password for the certificate file.
     */
    certificatePassword?: string;
}
/**
 * Required configuration options for the {@link ClientCertificateCredential}, with the path to a PEM certificate.
 */
export interface ClientCertificatePEMCertificatePath {
    /**
     * The path to the PEM-encoded public/private key certificate on the filesystem.
     */
    certificatePath: string;
    /**
     * The password for the certificate file.
     */
    certificatePassword?: string;
}
/**
 * Required configuration options for the {@link ClientCertificateCredential}, with either the string contents of a PEM certificate, or the path to a PEM certificate.
 */
export type ClientCertificateCredentialPEMConfiguration = ClientCertificatePEMCertificate | ClientCertificatePEMCertificatePath;
//# sourceMappingURL=clientCertificateCredentialModels.d.ts.map