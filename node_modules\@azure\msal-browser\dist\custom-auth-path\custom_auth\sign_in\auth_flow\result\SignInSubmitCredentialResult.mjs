/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a sign-in submit credential operation.
 */
class SignInSubmitCredentialResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of SignInSubmitCredentialResult.
     * @param state The state of the result.
     * @param resultData The result data.
     */
    constructor(state, resultData) {
        super(state, resultData);
    }
}

export { SignInSubmitCredentialResult };
//# sourceMappingURL=SignInSubmitCredentialResult.mjs.map
