{"version": 3, "file": "aesCryptographyProvider.js", "sourceRoot": "", "sources": ["../../../src/cryptography/aesCryptographyProvider.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAGlC,4DAAsC;AAoBtC,2CAAgE;AAEhE;;;GAGG;AACH,MAAa,uBAAuB;IAElC,YAAY,GAAe;QA6D3B;;;;;;WAMG;QACK,wBAAmB,GAAmE;YAC5F,UAAU,EAAE;gBACV,SAAS,EAAE,aAAa;gBACxB,cAAc,EAAE,GAAG,IAAI,CAAC;aACzB;YACD,UAAU,EAAE;gBACV,SAAS,EAAE,aAAa;gBACxB,cAAc,EAAE,GAAG,IAAI,CAAC;aACzB;YACD,UAAU,EAAE;gBACV,SAAS,EAAE,aAAa;gBACxB,cAAc,EAAE,GAAG,IAAI,CAAC;aACzB;SACF,CAAC;QAEM,wBAAmB,GAAoC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAlFpF,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IACD,OAAO,CACL,iBAA0C,EAC1C,QAAyB;QAEzB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC5F,MAAM,EAAE,GAAG,iBAAiB,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAE1D,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAEjC,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7F,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;QACxE,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEvD,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,SAAS,EAAE,iBAAiB,CAAC,SAAS;YACtC,MAAM,EAAE,SAAS;YACjB,EAAE,EAAE,EAAE;SACP,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CACL,iBAA0C,EAC1C,QAAyB;QAEzB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAE5F,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAEjC,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CACtC,SAAS,EACT,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,cAAc,CAAC,EACvC,iBAAiB,CAAC,EAAE,CACrB,CAAC;QACF,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;QACrE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE7C,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,SAAS,EAAE,iBAAiB,CAAC,SAAS;YACtC,MAAM,EAAE,GAAG;SACZ,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,SAAiB,EAAE,SAAwC;QACrE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IA0BD,OAAO,CACL,UAA4B,EAC5B,UAAsB,EACtB,QAAyB;QAEzB,MAAM,IAAI,6CAAiC,CACzC,mEAAmE,CACpE,CAAC;IACJ,CAAC;IAED,SAAS,CACP,UAA4B,EAC5B,aAAyB,EACzB,QAA2B;QAE3B,MAAM,IAAI,6CAAiC,CACzC,qEAAqE,CACtE,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,UAAkB,EAAE,OAAmB,EAAE,QAAsB;QAClE,MAAM,IAAI,6CAAiC,CACzC,4DAA4D,CAC7D,CAAC;IACJ,CAAC;IAED,QAAQ,CAAC,UAAkB,EAAE,KAAiB,EAAE,QAAsB;QACpE,MAAM,IAAI,6CAAiC,CACzC,4DAA4D,CAC7D,CAAC;IACJ,CAAC;IAED,MAAM,CACJ,UAAkB,EAClB,OAAmB,EACnB,UAAsB,EACtB,QAAwB;QAExB,MAAM,IAAI,6CAAiC,CACzC,8DAA8D,CAC/D,CAAC;IACJ,CAAC;IACD,UAAU,CACR,UAAkB,EAClB,KAAiB,EACjB,UAAsB,EACtB,eAAiC;QAEjC,MAAM,IAAI,6CAAiC,CACzC,8DAA8D,CAC/D,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,cAAsB;;QACxC,IACE,IAAI,CAAC,GAAG;YACR,CAAA,MAAA,IAAI,CAAC,GAAG,CAAC,GAAG,0CAAE,WAAW,EAAE,MAAK,KAAK;YACrC,CAAA,MAAA,IAAI,CAAC,GAAG,CAAC,GAAG,0CAAE,WAAW,EAAE,MAAK,SAAS,EACzC,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,cAAc,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,wBAAwB,cAAc,IAAI,CAAC,OAAO,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;CACF;AA7JD,0DA6JC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions } from \"@azure-rest/core-client\";\nimport * as crypto from \"node:crypto\";\nimport type {\n  AesCbcEncryptParameters,\n  DecryptOptions,\n  DecryptResult,\n  EncryptOptions,\n  EncryptResult,\n  JsonWebKey,\n  KeyWrapAlgorithm,\n  SignOptions,\n  SignResult,\n  UnwrapKeyOptions,\n  UnwrapResult,\n  VerifyOptions,\n  VerifyResult,\n  WrapKeyOptions,\n  WrapResult,\n} from \"../index.js\";\nimport type { AesCbcDecryptParameters } from \"../cryptographyClientModels.js\";\nimport type { CryptographyProvider, CryptographyProviderOperation } from \"./models.js\";\nimport { LocalCryptographyUnsupportedError } from \"./models.js\";\n\n/**\n * An AES cryptography provider supporting AES algorithms.\n * @internal\n */\nexport class AesCryptographyProvider implements CryptographyProvider {\n  private key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;\n  constructor(key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {\n    this.key = key;\n  }\n  encrypt(\n    encryptParameters: AesCbcEncryptParameters,\n    _options?: EncryptOptions,\n  ): Promise<EncryptResult> {\n    const { algorithm, keySizeInBytes } = this.supportedAlgorithms[encryptParameters.algorithm];\n    const iv = encryptParameters.iv || crypto.randomBytes(16);\n\n    this.ensureValid(keySizeInBytes);\n\n    const cipher = crypto.createCipheriv(algorithm, this.key.k!.subarray(0, keySizeInBytes), iv);\n    let encrypted = cipher.update(Buffer.from(encryptParameters.plaintext));\n    encrypted = Buffer.concat([encrypted, cipher.final()]);\n\n    return Promise.resolve({\n      algorithm: encryptParameters.algorithm,\n      result: encrypted,\n      iv: iv,\n    });\n  }\n\n  decrypt(\n    decryptParameters: AesCbcDecryptParameters,\n    _options?: DecryptOptions,\n  ): Promise<DecryptResult> {\n    const { algorithm, keySizeInBytes } = this.supportedAlgorithms[decryptParameters.algorithm];\n\n    this.ensureValid(keySizeInBytes);\n\n    const decipher = crypto.createDecipheriv(\n      algorithm,\n      this.key.k!.subarray(0, keySizeInBytes),\n      decryptParameters.iv,\n    );\n    let dec = decipher.update(Buffer.from(decryptParameters.ciphertext));\n    dec = Buffer.concat([dec, decipher.final()]);\n\n    return Promise.resolve({\n      algorithm: decryptParameters.algorithm,\n      result: dec,\n    });\n  }\n\n  isSupported(algorithm: string, operation: CryptographyProviderOperation): boolean {\n    if (!this.key.k) {\n      return false;\n    }\n\n    if (!Object.keys(this.supportedAlgorithms).includes(algorithm)) {\n      return false;\n    }\n\n    if (!this.supportedOperations.includes(operation)) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * The set of algorithms this provider supports.\n   * For AES encryption, the values include the underlying algorithm used in crypto\n   * as well as the key size in bytes.\n   *\n   * We start with support for A[SIZE]CBCPAD which uses the PKCS padding (the default padding scheme in node crypto)\n   */\n  private supportedAlgorithms: { [s: string]: { algorithm: string; keySizeInBytes: number } } = {\n    A128CBCPAD: {\n      algorithm: \"aes-128-cbc\",\n      keySizeInBytes: 128 >> 3,\n    },\n    A192CBCPAD: {\n      algorithm: \"aes-192-cbc\",\n      keySizeInBytes: 192 >> 3,\n    },\n    A256CBCPAD: {\n      algorithm: \"aes-256-cbc\",\n      keySizeInBytes: 256 >> 3,\n    },\n  };\n\n  private supportedOperations: CryptographyProviderOperation[] = [\"encrypt\", \"decrypt\"];\n\n  wrapKey(\n    _algorithm: KeyWrapAlgorithm,\n    _keyToWrap: Uint8Array,\n    _options?: WrapKeyOptions,\n  ): Promise<WrapResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Wrapping a key using a local JsonWebKey is not supported for AES.\",\n    );\n  }\n\n  unwrapKey(\n    _algorithm: KeyWrapAlgorithm,\n    _encryptedKey: Uint8Array,\n    _options?: UnwrapKeyOptions,\n  ): Promise<UnwrapResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Unwrapping a key using a local JsonWebKey is not supported for AES.\",\n    );\n  }\n\n  sign(_algorithm: string, _digest: Uint8Array, _options?: SignOptions): Promise<SignResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Signing using a local JsonWebKey is not supported for AES.\",\n    );\n  }\n\n  signData(_algorithm: string, _data: Uint8Array, _options?: SignOptions): Promise<SignResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Signing using a local JsonWebKey is not supported for AES.\",\n    );\n  }\n\n  verify(\n    _algorithm: string,\n    _digest: Uint8Array,\n    _signature: Uint8Array,\n    _options?: VerifyOptions,\n  ): Promise<VerifyResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Verifying using a local JsonWebKey is not supported for AES.\",\n    );\n  }\n  verifyData(\n    _algorithm: string,\n    _data: Uint8Array,\n    _signature: Uint8Array,\n    _updatedOptions: OperationOptions,\n  ): Promise<VerifyResult> {\n    throw new LocalCryptographyUnsupportedError(\n      \"Verifying using a local JsonWebKey is not supported for AES.\",\n    );\n  }\n\n  private ensureValid(keySizeInBytes: number): void {\n    if (\n      this.key &&\n      this.key.kty?.toUpperCase() !== \"OCT\" &&\n      this.key.kty?.toUpperCase() !== \"OCT-HSM\"\n    ) {\n      throw new Error(\"Key type does not match the key type oct or oct-hsm\");\n    }\n\n    if (!this.key.k) {\n      throw new Error(\"Symmetric key is required\");\n    }\n\n    if (this.key.k.length < keySizeInBytes) {\n      throw new Error(`Key must be at least ${keySizeInBytes << 3} bits`);\n    }\n  }\n}\n"]}