{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../../src/generated/models/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAsB1E,MAAM,UAAU,6BAA6B,CAAC,IAAyB;IACrE,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;QACzB,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC;QACvC,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;gBAC5B,OAAO,CAAC,CAAC;YACX,CAAC,CAAC;QACN,UAAU,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC;QAClB,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACtD,CAAC;AACJ,CAAC;AAED,mHAAmH;AACnH,MAAM,CAAN,IAAY,mBAaX;AAbD,WAAY,mBAAmB;IAC7B,sBAAsB;IACtB,gCAAS,CAAA;IACT,oEAAoE;IACpE,uCAAgB,CAAA;IAChB,gDAAgD;IAChD,kCAAW,CAAA;IACX,yDAAyD;IACzD,yCAAkB,CAAA;IAClB,wDAAwD;IACxD,kCAAW,CAAA;IACX,iFAAiF;IACjF,yCAAkB,CAAA;AACpB,CAAC,EAbW,mBAAmB,KAAnB,mBAAmB,QAa9B;AAgBD,8EAA8E;AAC9E,MAAM,CAAN,IAAY,wBAiBX;AAjBD,WAAY,wBAAwB;IAClC,qDAAqD;IACrD,+CAAmB,CAAA;IACnB,qDAAqD;IACrD,+CAAmB,CAAA;IACnB,kDAAkD;IAClD,yCAAa,CAAA;IACb,oDAAoD;IACpD,6CAAiB,CAAA;IACjB,8DAA8D;IAC9D,+CAAmB,CAAA;IACnB,gEAAgE;IAChE,mDAAuB,CAAA;IACvB,8DAA8D;IAC9D,6CAAiB,CAAA;IACjB,uEAAuE;IACvE,6CAAiB,CAAA;AACnB,CAAC,EAjBW,wBAAwB,KAAxB,wBAAwB,QAiBnC;AA0CD,MAAM,UAAU,uBAAuB,CAAC,IAAmB;IACzD,OAAO;QACL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;QACxB,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC;YACrB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;YACnB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAC5C,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACnB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAC1C,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC;KAC/B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,IAAS;IACjD,OAAO;QACL,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;QACxB,SAAS,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACpE,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAClE,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QACpC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QACpC,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC;QACxC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC;QACpC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC;QAC9B,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC;QAChC,WAAW,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;YACrB,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KACpD,CAAC;AACJ,CAAC;AAED,+RAA+R;AAC/R,MAAM,CAAN,IAAY,0BAeX;AAfD,WAAY,0BAA0B;IACpC,gVAAgV;IAChV,qDAAuB,CAAA;IACvB,sXAAsX;IACtX,4EAA8C,CAAA;IAC9C,8VAA8V;IAC9V,yDAA2B,CAAA;IAC3B,0TAA0T;IAC1T,oGAAsE,CAAA;IACtE,oVAAoV;IACpV,gGAAkE,CAAA;IAClE,4TAA4T;IAC5T,6EAA+C,CAAA;IAC/C,waAAwa;IACxa,wHAA0F,CAAA;AAC5F,CAAC,EAfW,0BAA0B,KAA1B,0BAA0B,QAerC;AA6BD,MAAM,UAAU,0BAA0B,CAAC,IAAS;IAClD,OAAO;QACL,kBAAkB,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC7C,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5B,CAAC,CAAC,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,QAAQ;gBAC9C,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,WAAW,CAAC;gBAC7D,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;QAChC,qBAAqB,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC;YACnD,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;YAC/B,CAAC,CAAC,OAAO,IAAI,CAAC,uBAAuB,CAAC,KAAK,QAAQ;gBACjD,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,WAAW,CAAC;gBAChE,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACnC,oBAAoB,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC;YACjD,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC;YAC9B,CAAC,CAAC,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,QAAQ;gBAChD,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE,WAAW,CAAC;gBAC/D,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC;QAClC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;KACzB,CAAC;AACJ,CAAC;AAED,sEAAsE;AACtE,MAAM,CAAN,IAAY,wBASX;AATD,WAAY,wBAAwB;IAClC,+DAA+D;IAC/D,0CAAc,CAAA;IACd,+DAA+D;IAC/D,0CAAc,CAAA;IACd,+DAA+D;IAC/D,0CAAc,CAAA;IACd,yCAAyC;IACzC,4CAAgB,CAAA;AAClB,CAAC,EATW,wBAAwB,KAAxB,wBAAwB,QASnC;AAwBD,MAAM,UAAU,0BAA0B,CAAC,IAAsB;IAC/D,OAAO;QACL,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC;QAChC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC;QAC5B,IAAI,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YAC1B,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,WAAW,CAAC;KAC3D,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,4BAA4B,CAAC,IAAS;IACpD,OAAO;QACL,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC;QAChC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC;QAC5B,aAAa,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YAC1B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;YACd,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;gBAChC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC;gBAC/C,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;KACnB,CAAC;AACJ,CAAC;AAgBD,MAAM,UAAU,qBAAqB,CAAC,IAAS;IAC7C,OAAO;QACL,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;QACxB,aAAa,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACxB,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;KACzD,CAAC;AACJ,CAAC;AAsCD,MAAM,UAAU,oBAAoB,CAAC,IAAgB;IACnD,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;gBAC5B,OAAO,CAAC,CAAC;YACX,CAAC,CAAC;QACN,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;QAC1E,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;QAC1E,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;QAC1E,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACjB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QAC9C,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;QACtE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;KACvE,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,IAAS;IAC9C,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,MAAM,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACtB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;gBAC7B,OAAO,CAAC,CAAC;YACX,CAAC,CAAC;QACN,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ;gBAC9B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAChB,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ;gBAC9B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAChB,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ;gBAC9B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAChB,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;gBACnC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC;gBAClD,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;QACrB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACX,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC7B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;KAChB,CAAC;AACJ,CAAC;AAQD,MAAM,UAAU,yBAAyB,CAAC,IAAS;IACjD,OAAO;QACL,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;YACf,CAAC,CAAC,+BAA+B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACnD,CAAC;AACJ,CAAC;AAmBD,MAAM,UAAU,+BAA+B,CAC7C,IAAS;IAET,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;QACxB,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,+BAA+B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KACxD,CAAC;AACJ,CAAC;AAgBD,MAAM,UAAU,6BAA6B,CAAC,IAAyB;IACrE,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,GAAG,EAAE,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,UAAU,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACtD,CAAC;AACJ,CAAC;AAsBD,MAAM,UAAU,4BAA4B,CAAC,IAAS;IACpD,OAAO;QACL,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;QACxB,aAAa,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACxB,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxD,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC;QAC9B,kBAAkB,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC7C,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5B,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC;QAC/C,WAAW,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;YACrB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACzC,CAAC;AACJ,CAAC;AAcD,MAAM,UAAU,6BAA6B,CAAC,IAAyB;IACrE,OAAO;QACL,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACtB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;gBAC5B,OAAO,CAAC,CAAC;YACX,CAAC,CAAC;QACN,UAAU,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACvB,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACtD,CAAC;AACJ,CAAC;AAUD,MAAM,UAAU,0BAA0B,CAAC,IAAS;IAClD,OAAO;QACL,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;YACf,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC;KAC3B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,MAAsB;IAC7D,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACzB,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC;AAcD,MAAM,UAAU,mBAAmB,CAAC,IAAS;IAC3C,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;KACzB,CAAC;AACJ,CAAC;AAQD,MAAM,UAAU,2BAA2B,CAAC,IAAS;IACnD,OAAO;QACL,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;YACf,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ;gBACjC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;gBAChD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;KACpB,CAAC;AACJ,CAAC;AAQD,MAAM,UAAU,8BAA8B,CAC5C,IAA0B;IAE1B,OAAO,EAAE,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC;AAC7E,CAAC;AAgBD,MAAM,UAAU,iCAAiC,CAC/C,IAA6B;IAE7B,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC;QACtB,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;QACrD,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;QAC1E,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACf,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACb,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC;QAChD,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACf,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACb,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC;KACjD,CAAC;AACJ,CAAC;AAED,uDAAuD;AACvD,MAAM,CAAN,IAAY,kCAmCX;AAnCD,WAAY,kCAAkC;IAC5C,2iBAA2iB;IAC3iB,0DAAoB,CAAA;IACpB,6IAA6I;IAC7I,iEAA2B,CAAA;IAC3B,4YAA4Y;IAC5Y,sDAAgB,CAAA;IAChB,uBAAuB;IACvB,yDAAmB,CAAA;IACnB,uBAAuB;IACvB,yDAAmB,CAAA;IACnB,uBAAuB;IACvB,yDAAmB,CAAA;IACnB,4BAA4B;IAC5B,uDAAiB,CAAA;IACjB,4BAA4B;IAC5B,uDAAiB,CAAA;IACjB,4BAA4B;IAC5B,uDAAiB,CAAA;IACjB,uBAAuB;IACvB,yDAAmB,CAAA;IACnB,uBAAuB;IACvB,yDAAmB,CAAA;IACnB,uBAAuB;IACvB,yDAAmB,CAAA;IACnB,yCAAyC;IACzC,+DAAyB,CAAA;IACzB,yCAAyC;IACzC,+DAAyB,CAAA;IACzB,yCAAyC;IACzC,+DAAyB,CAAA;IACzB,wBAAwB;IACxB,wEAAkC,CAAA;IAClC,qCAAqC;IACrC,+EAAyC,CAAA;AAC3C,CAAC,EAnCW,kCAAkC,KAAlC,kCAAkC,QAmC7C;AAyCD,MAAM,UAAU,8BAA8B,CAAC,IAAS;IACtD,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,MAAM,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;YACpB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;YACf,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ;gBACjC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;gBAChD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;QACnB,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ;gBAC9B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAChB,iBAAiB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACb,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,QAAQ;gBAC/B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC;gBAC9C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QACjB,2BAA2B,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACb,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,QAAQ;gBAC/B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC;gBAC9C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;KAClB,CAAC;AACJ,CAAC;AAUD,MAAM,UAAU,2BAA2B,CAAC,IAAuB;IACjE,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC;QACtB,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;KACtD,CAAC;AACJ,CAAC;AAED,yIAAyI;AACzI,MAAM,CAAN,IAAY,iCA6BX;AA7BD,WAAY,iCAAiC;IAC3C,0GAA0G;IAC1G,oDAAe,CAAA;IACf,0GAA0G;IAC1G,oDAAe,CAAA;IACf,0GAA0G;IAC1G,oDAAe,CAAA;IACf,2FAA2F;IAC3F,oDAAe,CAAA;IACf,2FAA2F;IAC3F,oDAAe,CAAA;IACf,2FAA2F;IAC3F,oDAAe,CAAA;IACf,+EAA+E;IAC/E,oDAAe,CAAA;IACf,8EAA8E;IAC9E,oDAAe,CAAA;IACf,8EAA8E;IAC9E,oDAAe,CAAA;IACf,eAAe;IACf,sDAAiB,CAAA;IACjB,0FAA0F;IAC1F,oDAAe,CAAA;IACf,yFAAyF;IACzF,oDAAe,CAAA;IACf,yFAAyF;IACzF,oDAAe,CAAA;IACf,0FAA0F;IAC1F,sDAAiB,CAAA;AACnB,CAAC,EA7BW,iCAAiC,KAAjC,iCAAiC,QA6B5C;AAkCD,MAAM,UAAU,6BAA6B,CAAC,IAAyB;IACrE,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC;QACtB,MAAM,EAAE,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC;QACvD,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,WAAW,CAAC;KAC1D,CAAC;AACJ,CAAC;AAQD,MAAM,UAAU,2BAA2B,CAAC,IAAS;IACnD,OAAO;QACL,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;KACrB,CAAC;AACJ,CAAC;AAYD,MAAM,UAAU,8BAA8B,CAC5C,IAA0B;IAE1B,OAAO;QACL,MAAM,EAAE,IAAI,CAAC,wBAAwB,CAAC;QACtC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;QACpB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;KACjB,CAAC;AACJ,CAAC;AAED,6EAA6E;AAC7E,MAAM,CAAN,IAAY,2BAOX;AAPD,WAAY,2BAA2B;IACrC,mDAAmD;IACnD,wEAAyC,CAAA;IACzC,mDAAmD;IACnD,wEAAyC,CAAA;IACzC,mDAAmD;IACnD,wEAAyC,CAAA;AAC3C,CAAC,EAPW,2BAA2B,KAA3B,2BAA2B,QAOtC;AAmBD,MAAM,UAAU,4BAA4B,CAAC,IAAS;IACpD,OAAO;QACL,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;KACrB,CAAC;AACJ,CAAC;AAUD,MAAM,UAAU,iCAAiC,CAC/C,IAAS;IAET,OAAO;QACL,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;YACf,CAAC,CAAC,+BAA+B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClD,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC;KAC3B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,+BAA+B,CAC7C,MAA6B;IAE7B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACzB,OAAO,0BAA0B,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC;AAoBD,MAAM,UAAU,0BAA0B,CAAC,IAAS;IAClD,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;QAChB,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;QACxB,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC;QAC9B,kBAAkB,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC7C,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5B,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC;QAC/C,WAAW,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;YACrB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACzC,CAAC;AACJ,CAAC;AAYD,MAAM,UAAU,2BAA2B,CAAC,IAAuB;IACjE,OAAO;QACL,eAAe,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACzB,CAAC,CAAC,8BAA8B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3D,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,qCAAqC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC9D,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,6BAA6B,CAAC,IAAS;IACrD,OAAO;QACL,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;QACd,eAAe,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACzB,CAAC,CAAC,gCAAgC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7D,UAAU,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACpB,CAAC,CAAC,uCAAuC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAChE,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,8BAA8B,CAC5C,MAA8B;IAE9B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACzB,OAAO,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,gCAAgC,CAC9C,MAA8B;IAE9B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACzB,OAAO,2BAA2B,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;AACL,CAAC;AAUD,MAAM,UAAU,yBAAyB,CAAC,IAAqB;IAC7D,OAAO;QACL,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACrB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChB,CAAC,CAAC,6BAA6B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAClD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,2BAA2B,CAAC,IAAS;IACnD,OAAO;QACL,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,kCAAkC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACrB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChB,CAAC,CAAC,+BAA+B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACpD,CAAC;AACJ,CAAC;AAUD,MAAM,UAAU,gCAAgC,CAC9C,IAA4B;IAE5B,OAAO;QACL,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC;QACxC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC;KAC3C,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kCAAkC,CAChD,IAAS;IAET,OAAO;QACL,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC;QACxC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC;KAC3C,CAAC;AACJ,CAAC;AAQD,MAAM,UAAU,6BAA6B,CAAC,IAAyB;IACrE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,+BAA+B,CAC7C,IAAS;IAET,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;KACnB,CAAC;AACJ,CAAC;AAeD,MAAM,UAAU,qCAAqC,CACnD,IAAiC;IAEjC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,uCAAuC,CACrD,IAAS;IAET,OAAO;QACL,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC;QAC9B,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QACpC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;KACrC,CAAC;AACJ,CAAC;AAQD,MAAM,UAAU,+BAA+B,CAC7C,IAA2B;IAE3B,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;AAClC,CAAC;AAQD,MAAM,UAAU,uBAAuB,CAAC,IAAS;IAC/C,OAAO;QACL,KAAK,EACH,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ;YAC/B,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;YAChD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;KACpB,CAAC;AACJ,CAAC;AAED,kCAAkC;AAClC,MAAM,CAAN,IAAY,aAOX;AAPD,WAAY,aAAa;IACvB,2BAA2B;IAC3B,4BAAW,CAAA;IACX,qCAAqC;IACrC,8CAA6B,CAAA;IAC7B,2BAA2B;IAC3B,4BAAW,CAAA;AACb,CAAC,EAPW,aAAa,KAAb,aAAa,QAOxB", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { uint8ArrayToString, stringToUint8Array } from \"@azure/core-util\";\n\n/** The key create parameters. */\nexport interface KeyCreateParameters {\n  /** The type of key to create. For valid values, see JsonWebKeyType. */\n  kty: JsonWebKeyType;\n  /** The key size in bits. For example: 2048, 3072, or 4096 for RSA. */\n  keySize?: number;\n  /** The public exponent for a RSA key. */\n  publicExponent?: number;\n  /** Json web key operations. For more information on possible key operations, see JsonWebKeyOperation. */\n  keyOps?: JsonWebKeyOperation[];\n  /** The attributes of a key managed by the key vault service. */\n  keyAttributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** Elliptic curve name. For valid values, see JsonWebKeyCurveName. */\n  curve?: JsonWebKeyCurveName;\n  /** The policy rules under which the key can be exported. */\n  releasePolicy?: KeyReleasePolicy;\n}\n\nexport function keyCreateParametersSerializer(item: KeyCreateParameters): any {\n  return {\n    kty: item[\"kty\"],\n    key_size: item[\"keySize\"],\n    public_exponent: item[\"publicExponent\"],\n    key_ops: !item[\"keyOps\"]\n      ? item[\"keyOps\"]\n      : item[\"keyOps\"].map((p: any) => {\n          return p;\n        }),\n    attributes: !item[\"keyAttributes\"]\n      ? item[\"keyAttributes\"]\n      : keyAttributesSerializer(item[\"keyAttributes\"]),\n    tags: item[\"tags\"],\n    crv: item[\"curve\"],\n    release_policy: !item[\"releasePolicy\"]\n      ? item[\"releasePolicy\"]\n      : keyReleasePolicySerializer(item[\"releasePolicy\"]),\n  };\n}\n\n/** JsonWebKey Key Type (kty), as defined in https://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-40. */\nexport enum KnownJsonWebKeyType {\n  /** Elliptic Curve. */\n  EC = \"EC\",\n  /** Elliptic Curve with a private key which is stored in the HSM. */\n  ECHSM = \"EC-HSM\",\n  /** RSA (https://tools.ietf.org/html/rfc3447) */\n  RSA = \"RSA\",\n  /** RSA with a private key which is stored in the HSM. */\n  RSAHSM = \"RSA-HSM\",\n  /** Octet sequence (used to represent symmetric keys) */\n  Oct = \"oct\",\n  /** Octet sequence (used to represent symmetric keys) which is stored the HSM. */\n  OctHSM = \"oct-HSM\",\n}\n\n/**\n * JsonWebKey Key Type (kty), as defined in https://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-40. \\\n * {@link KnownJsonWebKeyType} can be used interchangeably with JsonWebKeyType,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **EC**: Elliptic Curve. \\\n * **EC-HSM**: Elliptic Curve with a private key which is stored in the HSM. \\\n * **RSA**: RSA (https:\\//tools.ietf.org\\/html\\/rfc3447) \\\n * **RSA-HSM**: RSA with a private key which is stored in the HSM. \\\n * **oct**: Octet sequence (used to represent symmetric keys) \\\n * **oct-HSM**: Octet sequence (used to represent symmetric keys) which is stored the HSM.\n */\nexport type JsonWebKeyType = string;\n\n/** JSON web key operations. For more information, see JsonWebKeyOperation. */\nexport enum KnownJsonWebKeyOperation {\n  /** Indicates that the key can be used to encrypt. */\n  Encrypt = \"encrypt\",\n  /** Indicates that the key can be used to decrypt. */\n  Decrypt = \"decrypt\",\n  /** Indicates that the key can be used to sign. */\n  Sign = \"sign\",\n  /** Indicates that the key can be used to verify. */\n  Verify = \"verify\",\n  /** Indicates that the key can be used to wrap another key. */\n  WrapKey = \"wrapKey\",\n  /** Indicates that the key can be used to unwrap another key. */\n  UnwrapKey = \"unwrapKey\",\n  /** Indicates that the key can be imported during creation. */\n  Import = \"import\",\n  /** Indicates that the private component of the key can be exported. */\n  Export = \"export\",\n}\n\n/**\n * JSON web key operations. For more information, see JsonWebKeyOperation. \\\n * {@link KnownJsonWebKeyOperation} can be used interchangeably with JsonWebKeyOperation,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **encrypt**: Indicates that the key can be used to encrypt. \\\n * **decrypt**: Indicates that the key can be used to decrypt. \\\n * **sign**: Indicates that the key can be used to sign. \\\n * **verify**: Indicates that the key can be used to verify. \\\n * **wrapKey**: Indicates that the key can be used to wrap another key. \\\n * **unwrapKey**: Indicates that the key can be used to unwrap another key. \\\n * **import**: Indicates that the key can be imported during creation. \\\n * **export**: Indicates that the private component of the key can be exported.\n */\nexport type JsonWebKeyOperation = string;\n\n/** The attributes of a key managed by the key vault service. */\nexport interface KeyAttributes {\n  /** Determines whether the object is enabled. */\n  enabled?: boolean;\n  /** Not before date in UTC. */\n  notBefore?: Date;\n  /** Expiry date in UTC. */\n  expires?: Date;\n  /** Creation time in UTC. */\n  readonly created?: Date;\n  /** Last updated time in UTC. */\n  readonly updated?: Date;\n  /** softDelete data retention days. Value should be >=7 and <=90 when softDelete enabled, otherwise 0. */\n  readonly recoverableDays?: number;\n  /** Reflects the deletion recovery level currently in effect for keys in the current vault. If it contains 'Purgeable' the key can be permanently deleted by a privileged user; otherwise, only the system can purge the key, at the end of the retention interval. */\n  readonly recoveryLevel?: DeletionRecoveryLevel;\n  /** Indicates if the private key can be exported. Release policy must be provided when creating the first version of an exportable key. */\n  exportable?: boolean;\n  /** The underlying HSM Platform. */\n  readonly hsmPlatform?: string;\n  /** The key or key version attestation information. */\n  readonly attestation?: KeyAttestation;\n}\n\nexport function keyAttributesSerializer(item: KeyAttributes): any {\n  return {\n    enabled: item[\"enabled\"],\n    nbf: !item[\"notBefore\"]\n      ? item[\"notBefore\"]\n      : (item[\"notBefore\"].getTime() / 1000) | 0,\n    exp: !item[\"expires\"]\n      ? item[\"expires\"]\n      : (item[\"expires\"].getTime() / 1000) | 0,\n    exportable: item[\"exportable\"],\n  };\n}\n\nexport function keyAttributesDeserializer(item: any): KeyAttributes {\n  return {\n    enabled: item[\"enabled\"],\n    notBefore: !item[\"nbf\"] ? item[\"nbf\"] : new Date(item[\"nbf\"] * 1000),\n    expires: !item[\"exp\"] ? item[\"exp\"] : new Date(item[\"exp\"] * 1000),\n    created: !item[\"created\"]\n      ? item[\"created\"]\n      : new Date(item[\"created\"] * 1000),\n    updated: !item[\"updated\"]\n      ? item[\"updated\"]\n      : new Date(item[\"updated\"] * 1000),\n    recoverableDays: item[\"recoverableDays\"],\n    recoveryLevel: item[\"recoveryLevel\"],\n    exportable: item[\"exportable\"],\n    hsmPlatform: item[\"hsmPlatform\"],\n    attestation: !item[\"attestation\"]\n      ? item[\"attestation\"]\n      : keyAttestationDeserializer(item[\"attestation\"]),\n  };\n}\n\n/** Reflects the deletion recovery level currently in effect for certificates in the current vault. If it contains 'Purgeable', the certificate can be permanently deleted by a privileged user; otherwise, only the system can purge the certificate, at the end of the retention interval. */\nexport enum KnownDeletionRecoveryLevel {\n  /** Denotes a vault state in which deletion is an irreversible operation, without the possibility for recovery. This level corresponds to no protection being available against a Delete operation; the data is irretrievably lost upon accepting a Delete operation at the entity level or higher (vault, resource group, subscription etc.) */\n  Purgeable = \"Purgeable\",\n  /** Denotes a vault state in which deletion is recoverable, and which also permits immediate and permanent deletion (i.e. purge). This level guarantees the recoverability of the deleted entity during the retention interval (90 days), unless a Purge operation is requested, or the subscription is cancelled. System wil permanently delete it after 90 days, if not recovered */\n  RecoverablePurgeable = \"Recoverable+Purgeable\",\n  /** Denotes a vault state in which deletion is recoverable without the possibility for immediate and permanent deletion (i.e. purge). This level guarantees the recoverability of the deleted entity during the retention interval(90 days) and while the subscription is still available. System wil permanently delete it after 90 days, if not recovered */\n  Recoverable = \"Recoverable\",\n  /** Denotes a vault and subscription state in which deletion is recoverable within retention interval (90 days), immediate and permanent deletion (i.e. purge) is not permitted, and in which the subscription itself  cannot be permanently canceled. System wil permanently delete it after 90 days, if not recovered */\n  RecoverableProtectedSubscription = \"Recoverable+ProtectedSubscription\",\n  /** Denotes a vault state in which deletion is recoverable, and which also permits immediate and permanent deletion (i.e. purge when 7 <= SoftDeleteRetentionInDays < 90). This level guarantees the recoverability of the deleted entity during the retention interval, unless a Purge operation is requested, or the subscription is cancelled. */\n  CustomizedRecoverablePurgeable = \"CustomizedRecoverable+Purgeable\",\n  /** Denotes a vault state in which deletion is recoverable without the possibility for immediate and permanent deletion (i.e. purge when 7 <= SoftDeleteRetentionInDays < 90).This level guarantees the recoverability of the deleted entity during the retention interval and while the subscription is still available. */\n  CustomizedRecoverable = \"CustomizedRecoverable\",\n  /** Denotes a vault and subscription state in which deletion is recoverable, immediate and permanent deletion (i.e. purge) is not permitted, and in which the subscription itself cannot be permanently canceled when 7 <= SoftDeleteRetentionInDays < 90. This level guarantees the recoverability of the deleted entity during the retention interval, and also reflects the fact that the subscription itself cannot be cancelled. */\n  CustomizedRecoverableProtectedSubscription = \"CustomizedRecoverable+ProtectedSubscription\",\n}\n\n/**\n * Reflects the deletion recovery level currently in effect for certificates in the current vault. If it contains 'Purgeable', the certificate can be permanently deleted by a privileged user; otherwise, only the system can purge the certificate, at the end of the retention interval. \\\n * {@link KnownDeletionRecoveryLevel} can be used interchangeably with DeletionRecoveryLevel,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **Purgeable**: Denotes a vault state in which deletion is an irreversible operation, without the possibility for recovery. This level corresponds to no protection being available against a Delete operation; the data is irretrievably lost upon accepting a Delete operation at the entity level or higher (vault, resource group, subscription etc.) \\\n * **Recoverable+Purgeable**: Denotes a vault state in which deletion is recoverable, and which also permits immediate and permanent deletion (i.e. purge). This level guarantees the recoverability of the deleted entity during the retention interval (90 days), unless a Purge operation is requested, or the subscription is cancelled. System wil permanently delete it after 90 days, if not recovered \\\n * **Recoverable**: Denotes a vault state in which deletion is recoverable without the possibility for immediate and permanent deletion (i.e. purge). This level guarantees the recoverability of the deleted entity during the retention interval(90 days) and while the subscription is still available. System wil permanently delete it after 90 days, if not recovered \\\n * **Recoverable+ProtectedSubscription**: Denotes a vault and subscription state in which deletion is recoverable within retention interval (90 days), immediate and permanent deletion (i.e. purge) is not permitted, and in which the subscription itself  cannot be permanently canceled. System wil permanently delete it after 90 days, if not recovered \\\n * **CustomizedRecoverable+Purgeable**: Denotes a vault state in which deletion is recoverable, and which also permits immediate and permanent deletion (i.e. purge when 7 <= SoftDeleteRetentionInDays < 90). This level guarantees the recoverability of the deleted entity during the retention interval, unless a Purge operation is requested, or the subscription is cancelled. \\\n * **CustomizedRecoverable**: Denotes a vault state in which deletion is recoverable without the possibility for immediate and permanent deletion (i.e. purge when 7 <= SoftDeleteRetentionInDays < 90).This level guarantees the recoverability of the deleted entity during the retention interval and while the subscription is still available. \\\n * **CustomizedRecoverable+ProtectedSubscription**: Denotes a vault and subscription state in which deletion is recoverable, immediate and permanent deletion (i.e. purge) is not permitted, and in which the subscription itself cannot be permanently canceled when 7 <= SoftDeleteRetentionInDays < 90. This level guarantees the recoverability of the deleted entity during the retention interval, and also reflects the fact that the subscription itself cannot be cancelled.\n */\nexport type DeletionRecoveryLevel = string;\n\n/** The key attestation information. */\nexport interface KeyAttestation {\n  /** A base64url-encoded string containing certificates in PEM format, used for attestation validation. */\n  certificatePemFile?: Uint8Array;\n  /** The attestation blob bytes encoded as base64url string corresponding to a private key. */\n  privateKeyAttestation?: Uint8Array;\n  /** The attestation blob bytes encoded as base64url string corresponding to a public key in case of asymmetric key. */\n  publicKeyAttestation?: Uint8Array;\n  /** The version of the attestation. */\n  version?: string;\n}\n\nexport function keyAttestationDeserializer(item: any): KeyAttestation {\n  return {\n    certificatePemFile: !item[\"certificatePemFile\"]\n      ? item[\"certificatePemFile\"]\n      : typeof item[\"certificatePemFile\"] === \"string\"\n        ? stringToUint8Array(item[\"certificatePemFile\"], \"base64url\")\n        : item[\"certificatePemFile\"],\n    privateKeyAttestation: !item[\"privateKeyAttestation\"]\n      ? item[\"privateKeyAttestation\"]\n      : typeof item[\"privateKeyAttestation\"] === \"string\"\n        ? stringToUint8Array(item[\"privateKeyAttestation\"], \"base64url\")\n        : item[\"privateKeyAttestation\"],\n    publicKeyAttestation: !item[\"publicKeyAttestation\"]\n      ? item[\"publicKeyAttestation\"]\n      : typeof item[\"publicKeyAttestation\"] === \"string\"\n        ? stringToUint8Array(item[\"publicKeyAttestation\"], \"base64url\")\n        : item[\"publicKeyAttestation\"],\n    version: item[\"version\"],\n  };\n}\n\n/** Elliptic curve name. For valid values, see JsonWebKeyCurveName. */\nexport enum KnownJsonWebKeyCurveName {\n  /** The NIST P-256 elliptic curve, AKA SECG curve SECP256R1. */\n  P256 = \"P-256\",\n  /** The NIST P-384 elliptic curve, AKA SECG curve SECP384R1. */\n  P384 = \"P-384\",\n  /** The NIST P-521 elliptic curve, AKA SECG curve SECP521R1. */\n  P521 = \"P-521\",\n  /** The SECG SECP256K1 elliptic curve. */\n  P256K = \"P-256K\",\n}\n\n/**\n * Elliptic curve name. For valid values, see JsonWebKeyCurveName. \\\n * {@link KnownJsonWebKeyCurveName} can be used interchangeably with JsonWebKeyCurveName,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **P-256**: The NIST P-256 elliptic curve, AKA SECG curve SECP256R1. \\\n * **P-384**: The NIST P-384 elliptic curve, AKA SECG curve SECP384R1. \\\n * **P-521**: The NIST P-521 elliptic curve, AKA SECG curve SECP521R1. \\\n * **P-256K**: The SECG SECP256K1 elliptic curve.\n */\nexport type JsonWebKeyCurveName = string;\n\n/** The policy rules under which the key can be exported. */\nexport interface KeyReleasePolicy {\n  /** Content type and version of key release policy */\n  contentType?: string;\n  /** Defines the mutability state of the policy. Once marked immutable, this flag cannot be reset and the policy cannot be changed under any circumstances. */\n  immutable?: boolean;\n  /** Blob encoding the policy rules under which the key can be released. Blob must be base64 URL encoded. */\n  encodedPolicy?: Uint8Array;\n}\n\nexport function keyReleasePolicySerializer(item: KeyReleasePolicy): any {\n  return {\n    contentType: item[\"contentType\"],\n    immutable: item[\"immutable\"],\n    data: !item[\"encodedPolicy\"]\n      ? item[\"encodedPolicy\"]\n      : uint8ArrayToString(item[\"encodedPolicy\"], \"base64url\"),\n  };\n}\n\nexport function keyReleasePolicyDeserializer(item: any): KeyReleasePolicy {\n  return {\n    contentType: item[\"contentType\"],\n    immutable: item[\"immutable\"],\n    encodedPolicy: !item[\"data\"]\n      ? item[\"data\"]\n      : typeof item[\"data\"] === \"string\"\n        ? stringToUint8Array(item[\"data\"], \"base64url\")\n        : item[\"data\"],\n  };\n}\n\n/** A KeyBundle consisting of a WebKey plus its attributes. */\nexport interface KeyBundle {\n  /** The Json web key. */\n  key?: JsonWebKey;\n  /** The key management attributes. */\n  attributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** True if the key's lifetime is managed by key vault. If this is a key backing a certificate, then managed will be true. */\n  readonly managed?: boolean;\n  /** The policy rules under which the key can be exported. */\n  releasePolicy?: KeyReleasePolicy;\n}\n\nexport function keyBundleDeserializer(item: any): KeyBundle {\n  return {\n    key: !item[\"key\"] ? item[\"key\"] : jsonWebKeyDeserializer(item[\"key\"]),\n    attributes: !item[\"attributes\"]\n      ? item[\"attributes\"]\n      : keyAttributesDeserializer(item[\"attributes\"]),\n    tags: item[\"tags\"],\n    managed: item[\"managed\"],\n    releasePolicy: !item[\"release_policy\"]\n      ? item[\"release_policy\"]\n      : keyReleasePolicyDeserializer(item[\"release_policy\"]),\n  };\n}\n\n/** As of http://tools.ietf.org/html/draft-ietf-jose-json-web-key-18 */\nexport interface JsonWebKey {\n  /** Key identifier. */\n  kid?: string;\n  /** JsonWebKey Key Type (kty), as defined in https://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-40. */\n  kty?: JsonWebKeyType;\n  /** Json web key operations. For more information on possible key operations, see JsonWebKeyOperation. */\n  keyOps?: string[];\n  /** RSA modulus. */\n  n?: Uint8Array;\n  /** RSA public exponent. */\n  e?: Uint8Array;\n  /** RSA private exponent, or the D component of an EC private key. */\n  d?: Uint8Array;\n  /** RSA private key parameter. */\n  dp?: Uint8Array;\n  /** RSA private key parameter. */\n  dq?: Uint8Array;\n  /** RSA private key parameter. */\n  qi?: Uint8Array;\n  /** RSA secret prime. */\n  p?: Uint8Array;\n  /** RSA secret prime, with p < q. */\n  q?: Uint8Array;\n  /** Symmetric key. */\n  k?: Uint8Array;\n  /** Protected Key, used with 'Bring Your Own Key'. */\n  t?: Uint8Array;\n  /** Elliptic curve name. For valid values, see JsonWebKeyCurveName. */\n  crv?: JsonWebKeyCurveName;\n  /** X component of an EC public key. */\n  x?: Uint8Array;\n  /** Y component of an EC public key. */\n  y?: Uint8Array;\n}\n\nexport function jsonWebKeySerializer(item: JsonWebKey): any {\n  return {\n    kid: item[\"kid\"],\n    kty: item[\"kty\"],\n    key_ops: !item[\"keyOps\"]\n      ? item[\"keyOps\"]\n      : item[\"keyOps\"].map((p: any) => {\n          return p;\n        }),\n    n: !item[\"n\"] ? item[\"n\"] : uint8ArrayToString(item[\"n\"], \"base64url\"),\n    e: !item[\"e\"] ? item[\"e\"] : uint8ArrayToString(item[\"e\"], \"base64url\"),\n    d: !item[\"d\"] ? item[\"d\"] : uint8ArrayToString(item[\"d\"], \"base64url\"),\n    dp: !item[\"dp\"] ? item[\"dp\"] : uint8ArrayToString(item[\"dp\"], \"base64url\"),\n    dq: !item[\"dq\"] ? item[\"dq\"] : uint8ArrayToString(item[\"dq\"], \"base64url\"),\n    qi: !item[\"qi\"] ? item[\"qi\"] : uint8ArrayToString(item[\"qi\"], \"base64url\"),\n    p: !item[\"p\"] ? item[\"p\"] : uint8ArrayToString(item[\"p\"], \"base64url\"),\n    q: !item[\"q\"] ? item[\"q\"] : uint8ArrayToString(item[\"q\"], \"base64url\"),\n    k: !item[\"k\"] ? item[\"k\"] : uint8ArrayToString(item[\"k\"], \"base64url\"),\n    key_hsm: !item[\"t\"]\n      ? item[\"t\"]\n      : uint8ArrayToString(item[\"t\"], \"base64url\"),\n    crv: item[\"crv\"],\n    x: !item[\"x\"] ? item[\"x\"] : uint8ArrayToString(item[\"x\"], \"base64url\"),\n    y: !item[\"y\"] ? item[\"y\"] : uint8ArrayToString(item[\"y\"], \"base64url\"),\n  };\n}\n\nexport function jsonWebKeyDeserializer(item: any): JsonWebKey {\n  return {\n    kid: item[\"kid\"],\n    kty: item[\"kty\"],\n    keyOps: !item[\"key_ops\"]\n      ? item[\"key_ops\"]\n      : item[\"key_ops\"].map((p: any) => {\n          return p;\n        }),\n    n: !item[\"n\"]\n      ? item[\"n\"]\n      : typeof item[\"n\"] === \"string\"\n        ? stringToUint8Array(item[\"n\"], \"base64url\")\n        : item[\"n\"],\n    e: !item[\"e\"]\n      ? item[\"e\"]\n      : typeof item[\"e\"] === \"string\"\n        ? stringToUint8Array(item[\"e\"], \"base64url\")\n        : item[\"e\"],\n    d: !item[\"d\"]\n      ? item[\"d\"]\n      : typeof item[\"d\"] === \"string\"\n        ? stringToUint8Array(item[\"d\"], \"base64url\")\n        : item[\"d\"],\n    dp: !item[\"dp\"]\n      ? item[\"dp\"]\n      : typeof item[\"dp\"] === \"string\"\n        ? stringToUint8Array(item[\"dp\"], \"base64url\")\n        : item[\"dp\"],\n    dq: !item[\"dq\"]\n      ? item[\"dq\"]\n      : typeof item[\"dq\"] === \"string\"\n        ? stringToUint8Array(item[\"dq\"], \"base64url\")\n        : item[\"dq\"],\n    qi: !item[\"qi\"]\n      ? item[\"qi\"]\n      : typeof item[\"qi\"] === \"string\"\n        ? stringToUint8Array(item[\"qi\"], \"base64url\")\n        : item[\"qi\"],\n    p: !item[\"p\"]\n      ? item[\"p\"]\n      : typeof item[\"p\"] === \"string\"\n        ? stringToUint8Array(item[\"p\"], \"base64url\")\n        : item[\"p\"],\n    q: !item[\"q\"]\n      ? item[\"q\"]\n      : typeof item[\"q\"] === \"string\"\n        ? stringToUint8Array(item[\"q\"], \"base64url\")\n        : item[\"q\"],\n    k: !item[\"k\"]\n      ? item[\"k\"]\n      : typeof item[\"k\"] === \"string\"\n        ? stringToUint8Array(item[\"k\"], \"base64url\")\n        : item[\"k\"],\n    t: !item[\"key_hsm\"]\n      ? item[\"key_hsm\"]\n      : typeof item[\"key_hsm\"] === \"string\"\n        ? stringToUint8Array(item[\"key_hsm\"], \"base64url\")\n        : item[\"key_hsm\"],\n    crv: item[\"crv\"],\n    x: !item[\"x\"]\n      ? item[\"x\"]\n      : typeof item[\"x\"] === \"string\"\n        ? stringToUint8Array(item[\"x\"], \"base64url\")\n        : item[\"x\"],\n    y: !item[\"y\"]\n      ? item[\"y\"]\n      : typeof item[\"y\"] === \"string\"\n        ? stringToUint8Array(item[\"y\"], \"base64url\")\n        : item[\"y\"],\n  };\n}\n\n/** The key vault error exception. */\nexport interface KeyVaultError {\n  /** The key vault server error. */\n  readonly error?: ErrorModel;\n}\n\nexport function keyVaultErrorDeserializer(item: any): KeyVaultError {\n  return {\n    error: !item[\"error\"]\n      ? item[\"error\"]\n      : _keyVaultErrorErrorDeserializer(item[\"error\"]),\n  };\n}\n\n/** Alias for ErrorModel */\nexport type ErrorModel = {\n  code?: string;\n  message?: string;\n  innerError?: ErrorModel;\n} | null;\n\n/** model interface _KeyVaultErrorError */\nexport interface _KeyVaultErrorError {\n  /** The error code. */\n  readonly code?: string;\n  /** The error message. */\n  readonly message?: string;\n  /** The key vault server error. */\n  readonly innerError?: ErrorModel;\n}\n\nexport function _keyVaultErrorErrorDeserializer(\n  item: any,\n): _KeyVaultErrorError {\n  return {\n    code: item[\"code\"],\n    message: item[\"message\"],\n    innerError: !item[\"innererror\"]\n      ? item[\"innererror\"]\n      : _keyVaultErrorErrorDeserializer(item[\"innererror\"]),\n  };\n}\n\n/** The key import parameters. */\nexport interface KeyImportParameters {\n  /** Whether to import as a hardware key (HSM) or software key. */\n  hsm?: boolean;\n  /** The Json web key */\n  key: JsonWebKey;\n  /** The key management attributes. */\n  keyAttributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** The policy rules under which the key can be exported. */\n  releasePolicy?: KeyReleasePolicy;\n}\n\nexport function keyImportParametersSerializer(item: KeyImportParameters): any {\n  return {\n    Hsm: item[\"hsm\"],\n    key: jsonWebKeySerializer(item[\"key\"]),\n    attributes: !item[\"keyAttributes\"]\n      ? item[\"keyAttributes\"]\n      : keyAttributesSerializer(item[\"keyAttributes\"]),\n    tags: item[\"tags\"],\n    release_policy: !item[\"releasePolicy\"]\n      ? item[\"releasePolicy\"]\n      : keyReleasePolicySerializer(item[\"releasePolicy\"]),\n  };\n}\n\n/** A DeletedKeyBundle consisting of a WebKey plus its Attributes and deletion info */\nexport interface DeletedKeyBundle {\n  /** The Json web key. */\n  key?: JsonWebKey;\n  /** The key management attributes. */\n  attributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** True if the key's lifetime is managed by key vault. If this is a key backing a certificate, then managed will be true. */\n  readonly managed?: boolean;\n  /** The policy rules under which the key can be exported. */\n  releasePolicy?: KeyReleasePolicy;\n  /** The url of the recovery object, used to identify and recover the deleted key. */\n  recoveryId?: string;\n  /** The time when the key is scheduled to be purged, in UTC */\n  readonly scheduledPurgeDate?: Date;\n  /** The time when the key was deleted, in UTC */\n  readonly deletedDate?: Date;\n}\n\nexport function deletedKeyBundleDeserializer(item: any): DeletedKeyBundle {\n  return {\n    key: !item[\"key\"] ? item[\"key\"] : jsonWebKeyDeserializer(item[\"key\"]),\n    attributes: !item[\"attributes\"]\n      ? item[\"attributes\"]\n      : keyAttributesDeserializer(item[\"attributes\"]),\n    tags: item[\"tags\"],\n    managed: item[\"managed\"],\n    releasePolicy: !item[\"release_policy\"]\n      ? item[\"release_policy\"]\n      : keyReleasePolicyDeserializer(item[\"release_policy\"]),\n    recoveryId: item[\"recoveryId\"],\n    scheduledPurgeDate: !item[\"scheduledPurgeDate\"]\n      ? item[\"scheduledPurgeDate\"]\n      : new Date(item[\"scheduledPurgeDate\"] * 1000),\n    deletedDate: !item[\"deletedDate\"]\n      ? item[\"deletedDate\"]\n      : new Date(item[\"deletedDate\"] * 1000),\n  };\n}\n\n/** The key update parameters. */\nexport interface KeyUpdateParameters {\n  /** Json web key operations. For more information on possible key operations, see JsonWebKeyOperation. */\n  keyOps?: JsonWebKeyOperation[];\n  /** The attributes of a key managed by the key vault service. */\n  keyAttributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** The policy rules under which the key can be exported. */\n  releasePolicy?: KeyReleasePolicy;\n}\n\nexport function keyUpdateParametersSerializer(item: KeyUpdateParameters): any {\n  return {\n    key_ops: !item[\"keyOps\"]\n      ? item[\"keyOps\"]\n      : item[\"keyOps\"].map((p: any) => {\n          return p;\n        }),\n    attributes: !item[\"keyAttributes\"]\n      ? item[\"keyAttributes\"]\n      : keyAttributesSerializer(item[\"keyAttributes\"]),\n    tags: item[\"tags\"],\n    release_policy: !item[\"releasePolicy\"]\n      ? item[\"releasePolicy\"]\n      : keyReleasePolicySerializer(item[\"releasePolicy\"]),\n  };\n}\n\n/** The key list result. */\nexport interface _KeyListResult {\n  /** A response message containing a list of keys in the key vault along with a link to the next page of keys. */\n  readonly value?: KeyItem[];\n  /** The URL to get the next set of keys. */\n  readonly nextLink?: string;\n}\n\nexport function _keyListResultDeserializer(item: any): _KeyListResult {\n  return {\n    value: !item[\"value\"]\n      ? item[\"value\"]\n      : keyItemArrayDeserializer(item[\"value\"]),\n    nextLink: item[\"nextLink\"],\n  };\n}\n\nexport function keyItemArrayDeserializer(result: Array<KeyItem>): any[] {\n  return result.map((item) => {\n    return keyItemDeserializer(item);\n  });\n}\n\n/** The key item containing key metadata. */\nexport interface KeyItem {\n  /** Key identifier. */\n  kid?: string;\n  /** The key management attributes. */\n  attributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** True if the key's lifetime is managed by key vault. If this is a key backing a certificate, then managed will be true. */\n  readonly managed?: boolean;\n}\n\nexport function keyItemDeserializer(item: any): KeyItem {\n  return {\n    kid: item[\"kid\"],\n    attributes: !item[\"attributes\"]\n      ? item[\"attributes\"]\n      : keyAttributesDeserializer(item[\"attributes\"]),\n    tags: item[\"tags\"],\n    managed: item[\"managed\"],\n  };\n}\n\n/** The backup key result, containing the backup blob. */\nexport interface BackupKeyResult {\n  /** The backup blob containing the backed up key. */\n  readonly value?: Uint8Array;\n}\n\nexport function backupKeyResultDeserializer(item: any): BackupKeyResult {\n  return {\n    value: !item[\"value\"]\n      ? item[\"value\"]\n      : typeof item[\"value\"] === \"string\"\n        ? stringToUint8Array(item[\"value\"], \"base64url\")\n        : item[\"value\"],\n  };\n}\n\n/** The key restore parameters. */\nexport interface KeyRestoreParameters {\n  /** The backup blob associated with a key bundle. */\n  keyBundleBackup: Uint8Array;\n}\n\nexport function keyRestoreParametersSerializer(\n  item: KeyRestoreParameters,\n): any {\n  return { value: uint8ArrayToString(item[\"keyBundleBackup\"], \"base64url\") };\n}\n\n/** The key operations parameters. */\nexport interface KeyOperationsParameters {\n  /** algorithm identifier */\n  algorithm: JsonWebKeyEncryptionAlgorithm;\n  /** The value to operate on. */\n  value: Uint8Array;\n  /** Cryptographically random, non-repeating initialization vector for symmetric algorithms. */\n  iv?: Uint8Array;\n  /** Additional data to authenticate but not encrypt/decrypt when using authenticated crypto algorithms. */\n  aad?: Uint8Array;\n  /** The tag to authenticate when performing decryption with an authenticated algorithm. */\n  tag?: Uint8Array;\n}\n\nexport function keyOperationsParametersSerializer(\n  item: KeyOperationsParameters,\n): any {\n  return {\n    alg: item[\"algorithm\"],\n    value: uint8ArrayToString(item[\"value\"], \"base64url\"),\n    iv: !item[\"iv\"] ? item[\"iv\"] : uint8ArrayToString(item[\"iv\"], \"base64url\"),\n    aad: !item[\"aad\"]\n      ? item[\"aad\"]\n      : uint8ArrayToString(item[\"aad\"], \"base64url\"),\n    tag: !item[\"tag\"]\n      ? item[\"tag\"]\n      : uint8ArrayToString(item[\"tag\"], \"base64url\"),\n  };\n}\n\n/** An algorithm used for encryption and decryption. */\nexport enum KnownJsonWebKeyEncryptionAlgorithm {\n  /** [Not recommended] RSAES using Optimal Asymmetric Encryption Padding (OAEP), as described in https://tools.ietf.org/html/rfc3447, with the default parameters specified by RFC 3447 in Section A.2.1. Those default parameters are using a hash function of SHA-1 and a mask generation function of MGF1 with SHA-1. Microsoft recommends using RSA_OAEP_256 or stronger algorithms for enhanced security. Microsoft does *not* recommend RSA_OAEP, which is included solely for backwards compatibility. RSA_OAEP utilizes SHA1, which has known collision problems. */\n  RSAOaep = \"RSA-OAEP\",\n  /** RSAES using Optimal Asymmetric Encryption Padding with a hash function of SHA-256 and a mask generation function of MGF1 with SHA-256. */\n  RSAOaep256 = \"RSA-OAEP-256\",\n  /** [Not recommended] RSAES-PKCS1-V1_5 key encryption, as described in https://tools.ietf.org/html/rfc3447. Microsoft recommends using RSA_OAEP_256 or stronger algorithms for enhanced security. Microsoft does *not* recommend RSA_1_5, which is included solely for backwards compatibility. Cryptographic standards no longer consider RSA with the PKCS#1 v1.5 padding scheme secure for encryption. */\n  RSA15 = \"RSA1_5\",\n  /** 128-bit AES-GCM. */\n  A128GCM = \"A128GCM\",\n  /** 192-bit AES-GCM. */\n  A192GCM = \"A192GCM\",\n  /** 256-bit AES-GCM. */\n  A256GCM = \"A256GCM\",\n  /** 128-bit AES key wrap. */\n  A128KW = \"A128KW\",\n  /** 192-bit AES key wrap. */\n  A192KW = \"A192KW\",\n  /** 256-bit AES key wrap. */\n  A256KW = \"A256KW\",\n  /** 128-bit AES-CBC. */\n  A128CBC = \"A128CBC\",\n  /** 192-bit AES-CBC. */\n  A192CBC = \"A192CBC\",\n  /** 256-bit AES-CBC. */\n  A256CBC = \"A256CBC\",\n  /** 128-bit AES-CBC with PKCS padding. */\n  A128Cbcpad = \"A128CBCPAD\",\n  /** 192-bit AES-CBC with PKCS padding. */\n  A192Cbcpad = \"A192CBCPAD\",\n  /** 256-bit AES-CBC with PKCS padding. */\n  A256Cbcpad = \"A256CBCPAD\",\n  /** CKM AES key wrap. */\n  CkmAesKeyWrap = \"CKM_AES_KEY_WRAP\",\n  /** CKM AES key wrap with padding. */\n  CkmAesKeyWrapPad = \"CKM_AES_KEY_WRAP_PAD\",\n}\n\n/**\n * An algorithm used for encryption and decryption. \\\n * {@link KnownJsonWebKeyEncryptionAlgorithm} can be used interchangeably with JsonWebKeyEncryptionAlgorithm,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **RSA-OAEP**: [Not recommended] RSAES using Optimal Asymmetric Encryption Padding (OAEP), as described in https:\\//tools.ietf.org\\/html\\/rfc3447, with the default parameters specified by RFC 3447 in Section A.2.1. Those default parameters are using a hash function of SHA-1 and a mask generation function of MGF1 with SHA-1. Microsoft recommends using RSA_OAEP_256 or stronger algorithms for enhanced security. Microsoft does *not* recommend RSA_OAEP, which is included solely for backwards compatibility. RSA_OAEP utilizes SHA1, which has known collision problems. \\\n * **RSA-OAEP-256**: RSAES using Optimal Asymmetric Encryption Padding with a hash function of SHA-256 and a mask generation function of MGF1 with SHA-256. \\\n * **RSA1_5**: [Not recommended] RSAES-PKCS1-V1_5 key encryption, as described in https:\\//tools.ietf.org\\/html\\/rfc3447. Microsoft recommends using RSA_OAEP_256 or stronger algorithms for enhanced security. Microsoft does *not* recommend RSA_1_5, which is included solely for backwards compatibility. Cryptographic standards no longer consider RSA with the PKCS#1 v1.5 padding scheme secure for encryption. \\\n * **A128GCM**: 128-bit AES-GCM. \\\n * **A192GCM**: 192-bit AES-GCM. \\\n * **A256GCM**: 256-bit AES-GCM. \\\n * **A128KW**: 128-bit AES key wrap. \\\n * **A192KW**: 192-bit AES key wrap. \\\n * **A256KW**: 256-bit AES key wrap. \\\n * **A128CBC**: 128-bit AES-CBC. \\\n * **A192CBC**: 192-bit AES-CBC. \\\n * **A256CBC**: 256-bit AES-CBC. \\\n * **A128CBCPAD**: 128-bit AES-CBC with PKCS padding. \\\n * **A192CBCPAD**: 192-bit AES-CBC with PKCS padding. \\\n * **A256CBCPAD**: 256-bit AES-CBC with PKCS padding. \\\n * **CKM_AES_KEY_WRAP**: CKM AES key wrap. \\\n * **CKM_AES_KEY_WRAP_PAD**: CKM AES key wrap with padding.\n */\nexport type JsonWebKeyEncryptionAlgorithm = string;\n\n/** The key operation result. */\nexport interface KeyOperationResult {\n  /** Key identifier */\n  readonly kid?: string;\n  /** The result of the operation. */\n  readonly result?: Uint8Array;\n  /** Cryptographically random, non-repeating initialization vector for symmetric algorithms. */\n  readonly iv?: Uint8Array;\n  /** The tag to authenticate when performing decryption with an authenticated algorithm. */\n  readonly authenticationTag?: Uint8Array;\n  /** Additional data to authenticate but not encrypt/decrypt when using authenticated crypto algorithms. */\n  readonly additionalAuthenticatedData?: Uint8Array;\n}\n\nexport function keyOperationResultDeserializer(item: any): KeyOperationResult {\n  return {\n    kid: item[\"kid\"],\n    result: !item[\"value\"]\n      ? item[\"value\"]\n      : typeof item[\"value\"] === \"string\"\n        ? stringToUint8Array(item[\"value\"], \"base64url\")\n        : item[\"value\"],\n    iv: !item[\"iv\"]\n      ? item[\"iv\"]\n      : typeof item[\"iv\"] === \"string\"\n        ? stringToUint8Array(item[\"iv\"], \"base64url\")\n        : item[\"iv\"],\n    authenticationTag: !item[\"tag\"]\n      ? item[\"tag\"]\n      : typeof item[\"tag\"] === \"string\"\n        ? stringToUint8Array(item[\"tag\"], \"base64url\")\n        : item[\"tag\"],\n    additionalAuthenticatedData: !item[\"aad\"]\n      ? item[\"aad\"]\n      : typeof item[\"aad\"] === \"string\"\n        ? stringToUint8Array(item[\"aad\"], \"base64url\")\n        : item[\"aad\"],\n  };\n}\n\n/** The key operations parameters. */\nexport interface KeySignParameters {\n  /** The signing/verification algorithm identifier. For more information on possible algorithm types, see JsonWebKeySignatureAlgorithm. */\n  algorithm: JsonWebKeySignatureAlgorithm;\n  /** The value to operate on. */\n  value: Uint8Array;\n}\n\nexport function keySignParametersSerializer(item: KeySignParameters): any {\n  return {\n    alg: item[\"algorithm\"],\n    value: uint8ArrayToString(item[\"value\"], \"base64url\"),\n  };\n}\n\n/** The signing/verification algorithm identifier. For more information on possible algorithm types, see JsonWebKeySignatureAlgorithm. */\nexport enum KnownJsonWebKeySignatureAlgorithm {\n  /** RSASSA-PSS using SHA-256 and MGF1 with SHA-256, as described in https://tools.ietf.org/html/rfc7518 */\n  PS256 = \"PS256\",\n  /** RSASSA-PSS using SHA-384 and MGF1 with SHA-384, as described in https://tools.ietf.org/html/rfc7518 */\n  PS384 = \"PS384\",\n  /** RSASSA-PSS using SHA-512 and MGF1 with SHA-512, as described in https://tools.ietf.org/html/rfc7518 */\n  PS512 = \"PS512\",\n  /** RSASSA-PKCS1-v1_5 using SHA-256, as described in https://tools.ietf.org/html/rfc7518 */\n  RS256 = \"RS256\",\n  /** RSASSA-PKCS1-v1_5 using SHA-384, as described in https://tools.ietf.org/html/rfc7518 */\n  RS384 = \"RS384\",\n  /** RSASSA-PKCS1-v1_5 using SHA-512, as described in https://tools.ietf.org/html/rfc7518 */\n  RS512 = \"RS512\",\n  /** HMAC using SHA-256, as described in  https://tools.ietf.org/html/rfc7518 */\n  HS256 = \"HS256\",\n  /** HMAC using SHA-384, as described in https://tools.ietf.org/html/rfc7518 */\n  HS384 = \"HS384\",\n  /** HMAC using SHA-512, as described in https://tools.ietf.org/html/rfc7518 */\n  HS512 = \"HS512\",\n  /** Reserved */\n  Rsnull = \"RSNULL\",\n  /** ECDSA using P-256 and SHA-256, as described in https://tools.ietf.org/html/rfc7518. */\n  ES256 = \"ES256\",\n  /** ECDSA using P-384 and SHA-384, as described in https://tools.ietf.org/html/rfc7518 */\n  ES384 = \"ES384\",\n  /** ECDSA using P-521 and SHA-512, as described in https://tools.ietf.org/html/rfc7518 */\n  ES512 = \"ES512\",\n  /** ECDSA using P-256K and SHA-256, as described in https://tools.ietf.org/html/rfc7518 */\n  ES256K = \"ES256K\",\n}\n\n/**\n * The signing/verification algorithm identifier. For more information on possible algorithm types, see JsonWebKeySignatureAlgorithm. \\\n * {@link KnownJsonWebKeySignatureAlgorithm} can be used interchangeably with JsonWebKeySignatureAlgorithm,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **PS256**: RSASSA-PSS using SHA-256 and MGF1 with SHA-256, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **PS384**: RSASSA-PSS using SHA-384 and MGF1 with SHA-384, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **PS512**: RSASSA-PSS using SHA-512 and MGF1 with SHA-512, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **RS256**: RSASSA-PKCS1-v1_5 using SHA-256, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **RS384**: RSASSA-PKCS1-v1_5 using SHA-384, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **RS512**: RSASSA-PKCS1-v1_5 using SHA-512, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **HS256**: HMAC using SHA-256, as described in  https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **HS384**: HMAC using SHA-384, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **HS512**: HMAC using SHA-512, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **RSNULL**: Reserved \\\n * **ES256**: ECDSA using P-256 and SHA-256, as described in https:\\//tools.ietf.org\\/html\\/rfc7518. \\\n * **ES384**: ECDSA using P-384 and SHA-384, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **ES512**: ECDSA using P-521 and SHA-512, as described in https:\\//tools.ietf.org\\/html\\/rfc7518 \\\n * **ES256K**: ECDSA using P-256K and SHA-256, as described in https:\\//tools.ietf.org\\/html\\/rfc7518\n */\nexport type JsonWebKeySignatureAlgorithm = string;\n\n/** The key verify parameters. */\nexport interface KeyVerifyParameters {\n  /** The signing/verification algorithm. For more information on possible algorithm types, see JsonWebKeySignatureAlgorithm. */\n  algorithm: JsonWebKeySignatureAlgorithm;\n  /** The digest used for signing. */\n  digest: Uint8Array;\n  /** The signature to be verified. */\n  signature: Uint8Array;\n}\n\nexport function keyVerifyParametersSerializer(item: KeyVerifyParameters): any {\n  return {\n    alg: item[\"algorithm\"],\n    digest: uint8ArrayToString(item[\"digest\"], \"base64url\"),\n    value: uint8ArrayToString(item[\"signature\"], \"base64url\"),\n  };\n}\n\n/** The key verify result. */\nexport interface KeyVerifyResult {\n  /** True if the signature is verified, otherwise false. */\n  readonly value?: boolean;\n}\n\nexport function keyVerifyResultDeserializer(item: any): KeyVerifyResult {\n  return {\n    value: item[\"value\"],\n  };\n}\n\n/** The release key parameters. */\nexport interface KeyReleaseParameters {\n  /** The attestation assertion for the target of the key release. */\n  targetAttestationToken: string;\n  /** A client provided nonce for freshness. */\n  nonce?: string;\n  /** The encryption algorithm to use to protected the exported key material */\n  enc?: KeyEncryptionAlgorithm;\n}\n\nexport function keyReleaseParametersSerializer(\n  item: KeyReleaseParameters,\n): any {\n  return {\n    target: item[\"targetAttestationToken\"],\n    nonce: item[\"nonce\"],\n    enc: item[\"enc\"],\n  };\n}\n\n/** The encryption algorithm to use to protected the exported key material */\nexport enum KnownKeyEncryptionAlgorithm {\n  /** The CKM_RSA_AES_KEY_WRAP key wrap mechanism. */\n  CkmRsaAesKeyWrap = \"CKM_RSA_AES_KEY_WRAP\",\n  /** The RSA_AES_KEY_WRAP_256 key wrap mechanism. */\n  RsaAesKeyWrap256 = \"RSA_AES_KEY_WRAP_256\",\n  /** The RSA_AES_KEY_WRAP_384 key wrap mechanism. */\n  RsaAesKeyWrap384 = \"RSA_AES_KEY_WRAP_384\",\n}\n\n/**\n * The encryption algorithm to use to protected the exported key material \\\n * {@link KnownKeyEncryptionAlgorithm} can be used interchangeably with KeyEncryptionAlgorithm,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **CKM_RSA_AES_KEY_WRAP**: The CKM_RSA_AES_KEY_WRAP key wrap mechanism. \\\n * **RSA_AES_KEY_WRAP_256**: The RSA_AES_KEY_WRAP_256 key wrap mechanism. \\\n * **RSA_AES_KEY_WRAP_384**: The RSA_AES_KEY_WRAP_384 key wrap mechanism.\n */\nexport type KeyEncryptionAlgorithm = string;\n\n/** The release result, containing the released key. */\nexport interface KeyReleaseResult {\n  /** A signed object containing the released key. */\n  readonly value?: string;\n}\n\nexport function keyReleaseResultDeserializer(item: any): KeyReleaseResult {\n  return {\n    value: item[\"value\"],\n  };\n}\n\n/** A list of keys that have been deleted in this vault. */\nexport interface _DeletedKeyListResult {\n  /** A response message containing a list of deleted keys in the key vault along with a link to the next page of deleted keys. */\n  readonly value?: DeletedKeyItem[];\n  /** The URL to get the next set of deleted keys. */\n  readonly nextLink?: string;\n}\n\nexport function _deletedKeyListResultDeserializer(\n  item: any,\n): _DeletedKeyListResult {\n  return {\n    value: !item[\"value\"]\n      ? item[\"value\"]\n      : deletedKeyItemArrayDeserializer(item[\"value\"]),\n    nextLink: item[\"nextLink\"],\n  };\n}\n\nexport function deletedKeyItemArrayDeserializer(\n  result: Array<DeletedKeyItem>,\n): any[] {\n  return result.map((item) => {\n    return deletedKeyItemDeserializer(item);\n  });\n}\n\n/** The deleted key item containing the deleted key metadata and information about deletion. */\nexport interface DeletedKeyItem {\n  /** Key identifier. */\n  kid?: string;\n  /** The key management attributes. */\n  attributes?: KeyAttributes;\n  /** Application specific metadata in the form of key-value pairs. */\n  tags?: Record<string, string>;\n  /** True if the key's lifetime is managed by key vault. If this is a key backing a certificate, then managed will be true. */\n  readonly managed?: boolean;\n  /** The url of the recovery object, used to identify and recover the deleted key. */\n  recoveryId?: string;\n  /** The time when the key is scheduled to be purged, in UTC */\n  readonly scheduledPurgeDate?: Date;\n  /** The time when the key was deleted, in UTC */\n  readonly deletedDate?: Date;\n}\n\nexport function deletedKeyItemDeserializer(item: any): DeletedKeyItem {\n  return {\n    kid: item[\"kid\"],\n    attributes: !item[\"attributes\"]\n      ? item[\"attributes\"]\n      : keyAttributesDeserializer(item[\"attributes\"]),\n    tags: item[\"tags\"],\n    managed: item[\"managed\"],\n    recoveryId: item[\"recoveryId\"],\n    scheduledPurgeDate: !item[\"scheduledPurgeDate\"]\n      ? item[\"scheduledPurgeDate\"]\n      : new Date(item[\"scheduledPurgeDate\"] * 1000),\n    deletedDate: !item[\"deletedDate\"]\n      ? item[\"deletedDate\"]\n      : new Date(item[\"deletedDate\"] * 1000),\n  };\n}\n\n/** Management policy for a key. */\nexport interface KeyRotationPolicy {\n  /** The key policy id. */\n  readonly id?: string;\n  /** Actions that will be performed by Key Vault over the lifetime of a key. For preview, lifetimeActions can only have two items at maximum: one for rotate, one for notify. Notification time would be default to 30 days before expiry and it is not configurable. */\n  lifetimeActions?: LifetimeActions[];\n  /** The key rotation policy attributes. */\n  attributes?: KeyRotationPolicyAttributes;\n}\n\nexport function keyRotationPolicySerializer(item: KeyRotationPolicy): any {\n  return {\n    lifetimeActions: !item[\"lifetimeActions\"]\n      ? item[\"lifetimeActions\"]\n      : lifetimeActionsArraySerializer(item[\"lifetimeActions\"]),\n    attributes: !item[\"attributes\"]\n      ? item[\"attributes\"]\n      : keyRotationPolicyAttributesSerializer(item[\"attributes\"]),\n  };\n}\n\nexport function keyRotationPolicyDeserializer(item: any): KeyRotationPolicy {\n  return {\n    id: item[\"id\"],\n    lifetimeActions: !item[\"lifetimeActions\"]\n      ? item[\"lifetimeActions\"]\n      : lifetimeActionsArrayDeserializer(item[\"lifetimeActions\"]),\n    attributes: !item[\"attributes\"]\n      ? item[\"attributes\"]\n      : keyRotationPolicyAttributesDeserializer(item[\"attributes\"]),\n  };\n}\n\nexport function lifetimeActionsArraySerializer(\n  result: Array<LifetimeActions>,\n): any[] {\n  return result.map((item) => {\n    return lifetimeActionsSerializer(item);\n  });\n}\n\nexport function lifetimeActionsArrayDeserializer(\n  result: Array<LifetimeActions>,\n): any[] {\n  return result.map((item) => {\n    return lifetimeActionsDeserializer(item);\n  });\n}\n\n/** Action and its trigger that will be performed by Key Vault over the lifetime of a key. */\nexport interface LifetimeActions {\n  /** The condition that will execute the action. */\n  trigger?: LifetimeActionsTrigger;\n  /** The action that will be executed. */\n  action?: LifetimeActionsType;\n}\n\nexport function lifetimeActionsSerializer(item: LifetimeActions): any {\n  return {\n    trigger: !item[\"trigger\"]\n      ? item[\"trigger\"]\n      : lifetimeActionsTriggerSerializer(item[\"trigger\"]),\n    action: !item[\"action\"]\n      ? item[\"action\"]\n      : lifetimeActionsTypeSerializer(item[\"action\"]),\n  };\n}\n\nexport function lifetimeActionsDeserializer(item: any): LifetimeActions {\n  return {\n    trigger: !item[\"trigger\"]\n      ? item[\"trigger\"]\n      : lifetimeActionsTriggerDeserializer(item[\"trigger\"]),\n    action: !item[\"action\"]\n      ? item[\"action\"]\n      : lifetimeActionsTypeDeserializer(item[\"action\"]),\n  };\n}\n\n/** A condition to be satisfied for an action to be executed. */\nexport interface LifetimeActionsTrigger {\n  /** Time after creation to attempt to rotate. It only applies to rotate. It will be in ISO 8601 duration format. Example: 90 days : \"P90D\" */\n  timeAfterCreate?: string;\n  /** Time before expiry to attempt to rotate or notify. It will be in ISO 8601 duration format. Example: 90 days : \"P90D\" */\n  timeBeforeExpiry?: string;\n}\n\nexport function lifetimeActionsTriggerSerializer(\n  item: LifetimeActionsTrigger,\n): any {\n  return {\n    timeAfterCreate: item[\"timeAfterCreate\"],\n    timeBeforeExpiry: item[\"timeBeforeExpiry\"],\n  };\n}\n\nexport function lifetimeActionsTriggerDeserializer(\n  item: any,\n): LifetimeActionsTrigger {\n  return {\n    timeAfterCreate: item[\"timeAfterCreate\"],\n    timeBeforeExpiry: item[\"timeBeforeExpiry\"],\n  };\n}\n\n/** The action that will be executed. */\nexport interface LifetimeActionsType {\n  /** The type of the action. The value should be compared case-insensitively. */\n  type?: KeyRotationPolicyAction;\n}\n\nexport function lifetimeActionsTypeSerializer(item: LifetimeActionsType): any {\n  return { type: item[\"type\"] };\n}\n\nexport function lifetimeActionsTypeDeserializer(\n  item: any,\n): LifetimeActionsType {\n  return {\n    type: item[\"type\"],\n  };\n}\n\n/** The type of the action. The value should be compared case-insensitively. */\nexport type KeyRotationPolicyAction = \"Rotate\" | \"Notify\";\n\n/** The key rotation policy attributes. */\nexport interface KeyRotationPolicyAttributes {\n  /** The expiryTime will be applied on the new key version. It should be at least 28 days. It will be in ISO 8601 Format. Examples: 90 days: P90D, 3 months: P3M, 48 hours: PT48H, 1 year and 10 days: P1Y10D */\n  expiryTime?: string;\n  /** The key rotation policy created time in UTC. */\n  readonly created?: Date;\n  /** The key rotation policy's last updated time in UTC. */\n  readonly updated?: Date;\n}\n\nexport function keyRotationPolicyAttributesSerializer(\n  item: KeyRotationPolicyAttributes,\n): any {\n  return { expiryTime: item[\"expiryTime\"] };\n}\n\nexport function keyRotationPolicyAttributesDeserializer(\n  item: any,\n): KeyRotationPolicyAttributes {\n  return {\n    expiryTime: item[\"expiryTime\"],\n    created: !item[\"created\"]\n      ? item[\"created\"]\n      : new Date(item[\"created\"] * 1000),\n    updated: !item[\"updated\"]\n      ? item[\"updated\"]\n      : new Date(item[\"updated\"] * 1000),\n  };\n}\n\n/** The get random bytes request object. */\nexport interface GetRandomBytesRequest {\n  /** The requested number of random bytes. */\n  count: number;\n}\n\nexport function getRandomBytesRequestSerializer(\n  item: GetRandomBytesRequest,\n): any {\n  return { count: item[\"count\"] };\n}\n\n/** The get random bytes response object containing the bytes. */\nexport interface RandomBytes {\n  /** The bytes encoded as a base64url string. */\n  value: Uint8Array;\n}\n\nexport function randomBytesDeserializer(item: any): RandomBytes {\n  return {\n    value:\n      typeof item[\"value\"] === \"string\"\n        ? stringToUint8Array(item[\"value\"], \"base64url\")\n        : item[\"value\"],\n  };\n}\n\n/** The available API versions. */\nexport enum KnownVersions {\n  /** The 7.5 API version. */\n  V75 = \"7.5\",\n  /** The 7.6-preview.2 API version. */\n  V76Preview2 = \"7.6-preview.2\",\n  /** The 7.6 API version. */\n  V76 = \"7.6\",\n}\n"]}