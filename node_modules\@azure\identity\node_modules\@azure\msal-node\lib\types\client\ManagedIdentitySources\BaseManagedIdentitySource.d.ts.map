{"version": 3, "file": "BaseManagedIdentitySource.d.ts", "sourceRoot": "", "sources": ["../../../../src/client/ManagedIdentitySources/BaseManagedIdentitySource.ts"], "names": [], "mappings": "AAKA,OAAO,EAEH,SAAS,EAIT,cAAc,EACd,MAAM,EACN,qBAAqB,EACrB,eAAe,EAEf,gCAAgC,EAGhC,oBAAoB,EAEvB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAC;AACtE,OAAO,EAAE,gCAAgC,EAAE,MAAM,kDAAkD,CAAC;AACpG,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAC;AACjF,OAAO,EAEH,qBAAqB,EAExB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,4BAA4B,EAAE,MAAM,gDAAgD,CAAC;AAC9F,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAQzD;;GAEG;AACH,eAAO,MAAM,gDAAgD;;;;;;CAMnD,CAAC;AACX,MAAM,MAAM,gDAAgD,GACxD,CAAC,OAAO,gDAAgD,CAAC,CAAC,MAAM,OAAO,gDAAgD,CAAC,CAAC;AAE7H,8BAAsB,yBAAyB;IAC3C,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IACzB,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,aAAa,CAAiB;IACtC,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,sBAAsB,CAAU;gBAGpC,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,cAAc,EAC7B,cAAc,EAAE,cAAc,EAC9B,sBAAsB,EAAE,OAAO;IASnC,QAAQ,CAAC,aAAa,CAClB,OAAO,EAAE,MAAM,EACf,iBAAiB,EAAE,iBAAiB,GACrC,gCAAgC;IAEtB,2BAA2B,CACpC,QAAQ,EAAE,eAAe,CAAC,4BAA4B,CAAC,EAEvD,cAAc,EAAE,cAAc,EAE9B,eAAe,EAAE,gCAAgC,EAEjD,sBAAsB,EAAE,qBAAqB,GAC9C,OAAO,CAAC,gCAAgC,CAAC;IAIrC,sBAAsB,CACzB,QAAQ,EAAE,eAAe,CAAC,4BAA4B,CAAC,GACxD,gCAAgC;IA+CtB,+BAA+B,CACxC,sBAAsB,EAAE,sBAAsB,EAC9C,iBAAiB,EAAE,iBAAiB,EACpC,aAAa,EAAE,SAAS,EACxB,kBAAkB,CAAC,EAAE,OAAO,GAC7B,OAAO,CAAC,oBAAoB,CAAC;IA8GzB,iDAAiD,CACpD,qBAAqB,EAAE,qBAAqB,EAC5C,MAAM,CAAC,EAAE,OAAO,EAChB,WAAW,CAAC,EAAE,OAAO,GACtB,MAAM;IAiCT,OAAc,gCAAgC,0BACnB,MAAM,eAChB,MAAM,cACP,MAAM,UACV,MAAM,KACf,MAAM,CAeP;CACL"}