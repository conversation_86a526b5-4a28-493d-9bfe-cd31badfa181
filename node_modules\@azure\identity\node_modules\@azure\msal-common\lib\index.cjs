/*! @azure/msal-common v15.9.0 2025-07-23 */
'use strict';
'use strict';

var indexNode = require('./index-node-C5c-lcoe.js');
var indexBrowser = require('./index-browser.cjs');



exports.AADAuthorityConstants = indexNode.AADAuthorityConstants;
exports.AADServerParamKeys = indexNode.AADServerParamKeys;
exports.AccountEntity = indexNode.AccountEntity;
exports.AuthError = indexNode.AuthError;
exports.AuthErrorCodes = indexNode.AuthErrorCodes;
exports.AuthErrorMessage = indexNode.AuthErrorMessage;
exports.AuthToken = indexNode.AuthToken;
exports.AuthenticationHeaderParser = indexNode.AuthenticationHeaderParser;
exports.AuthenticationScheme = indexNode.AuthenticationScheme;
exports.Authority = indexNode.Authority;
exports.AuthorityFactory = indexNode.AuthorityFactory;
exports.AuthorityType = indexNode.AuthorityType;
exports.AuthorizationCodeClient = indexNode.AuthorizationCodeClient;
exports.AuthorizeProtocol = indexNode.Authorize;
exports.AzureCloudInstance = indexNode.AzureCloudInstance;
exports.BaseClient = indexNode.BaseClient;
exports.CacheAccountType = indexNode.CacheAccountType;
exports.CacheError = indexNode.CacheError;
exports.CacheErrorCodes = indexNode.CacheErrorCodes;
exports.CacheHelpers = indexNode.CacheHelpers;
exports.CacheManager = indexNode.CacheManager;
exports.CacheOutcome = indexNode.CacheOutcome;
exports.CacheType = indexNode.CacheType;
exports.CcsCredentialType = indexNode.CcsCredentialType;
exports.ClaimsRequestKeys = indexNode.ClaimsRequestKeys;
exports.ClientAssertionUtils = indexNode.ClientAssertionUtils;
exports.ClientAuthError = indexNode.ClientAuthError;
exports.ClientAuthErrorCodes = indexNode.ClientAuthErrorCodes;
exports.ClientAuthErrorMessage = indexNode.ClientAuthErrorMessage;
exports.ClientConfigurationError = indexNode.ClientConfigurationError;
exports.ClientConfigurationErrorCodes = indexNode.ClientConfigurationErrorCodes;
exports.ClientConfigurationErrorMessage = indexNode.ClientConfigurationErrorMessage;
exports.CodeChallengeMethodValues = indexNode.CodeChallengeMethodValues;
exports.Constants = indexNode.Constants;
exports.CredentialType = indexNode.CredentialType;
exports.DEFAULT_CRYPTO_IMPLEMENTATION = indexNode.DEFAULT_CRYPTO_IMPLEMENTATION;
exports.DEFAULT_SYSTEM_OPTIONS = indexNode.DEFAULT_SYSTEM_OPTIONS;
exports.DEFAULT_TOKEN_RENEWAL_OFFSET_SEC = indexNode.DEFAULT_TOKEN_RENEWAL_OFFSET_SEC;
exports.DefaultStorageClass = indexNode.DefaultStorageClass;
exports.EncodingTypes = indexNode.EncodingTypes;
exports.Errors = indexNode.Errors;
exports.GrantType = indexNode.GrantType;
exports.HeaderNames = indexNode.HeaderNames;
exports.HttpMethod = indexNode.HttpMethod;
exports.HttpStatus = indexNode.HttpStatus;
exports.IntFields = indexNode.IntFields;
exports.InteractionRequiredAuthError = indexNode.InteractionRequiredAuthError;
exports.InteractionRequiredAuthErrorCodes = indexNode.InteractionRequiredAuthErrorCodes;
exports.InteractionRequiredAuthErrorMessage = indexNode.InteractionRequiredAuthErrorMessage;
exports.JsonWebTokenTypes = indexNode.JsonWebTokenTypes;
Object.defineProperty(exports, "LogLevel", {
	enumerable: true,
	get: function () { return indexNode.LogLevel; }
});
exports.Logger = indexNode.Logger;
exports.NetworkError = indexNode.NetworkError;
exports.OAuthResponseType = indexNode.OAuthResponseType;
exports.OIDC_DEFAULT_SCOPES = indexNode.OIDC_DEFAULT_SCOPES;
exports.ONE_DAY_IN_MS = indexNode.ONE_DAY_IN_MS;
exports.PasswordGrantConstants = indexNode.PasswordGrantConstants;
exports.PerformanceEventStatus = indexNode.PerformanceEventStatus;
exports.PerformanceEvents = indexNode.PerformanceEvents;
exports.PersistentCacheKeys = indexNode.PersistentCacheKeys;
exports.PopTokenGenerator = indexNode.PopTokenGenerator;
exports.PromptValue = indexNode.PromptValue;
exports.ProtocolMode = indexNode.ProtocolMode;
exports.ProtocolUtils = indexNode.ProtocolUtils;
exports.RefreshTokenClient = indexNode.RefreshTokenClient;
exports.RequestParameterBuilder = indexNode.RequestParameterBuilder;
exports.ResponseHandler = indexNode.ResponseHandler;
exports.ResponseMode = indexNode.ResponseMode;
exports.ScopeSet = indexNode.ScopeSet;
exports.ServerError = indexNode.ServerError;
exports.ServerResponseType = indexNode.ServerResponseType;
exports.ServerTelemetryManager = indexNode.ServerTelemetryManager;
exports.SilentFlowClient = indexNode.SilentFlowClient;
exports.StringUtils = indexNode.StringUtils;
exports.StubPerformanceClient = indexNode.StubPerformanceClient;
exports.StubbedNetworkModule = indexNode.StubbedNetworkModule;
exports.THE_FAMILY_ID = indexNode.THE_FAMILY_ID;
exports.ThrottlingConstants = indexNode.ThrottlingConstants;
exports.ThrottlingUtils = indexNode.ThrottlingUtils;
exports.TimeUtils = indexNode.TimeUtils;
exports.TokenCacheContext = indexNode.TokenCacheContext;
exports.UrlString = indexNode.UrlString;
exports.UrlUtils = indexNode.UrlUtils;
exports.buildAccountToCache = indexNode.buildAccountToCache;
exports.buildClientInfo = indexNode.buildClientInfo;
exports.buildClientInfoFromHomeAccountId = indexNode.buildClientInfoFromHomeAccountId;
exports.buildStaticAuthorityOptions = indexNode.buildStaticAuthorityOptions;
exports.buildTenantProfile = indexNode.buildTenantProfile;
exports.createAuthError = indexNode.createAuthError;
exports.createCacheError = indexNode.createCacheError;
exports.createClientAuthError = indexNode.createClientAuthError;
exports.createClientConfigurationError = indexNode.createClientConfigurationError;
exports.createInteractionRequiredAuthError = indexNode.createInteractionRequiredAuthError;
exports.createNetworkError = indexNode.createNetworkError;
exports.formatAuthorityUri = indexNode.formatAuthorityUri;
exports.getClientAssertion = indexNode.getClientAssertion;
exports.getRequestThumbprint = indexNode.getRequestThumbprint;
exports.getTenantIdFromIdTokenClaims = indexNode.getTenantIdFromIdTokenClaims;
exports.invoke = indexNode.invoke;
exports.invokeAsync = indexNode.invokeAsync;
exports.tenantIdMatchesHomeTenant = indexNode.tenantIdMatchesHomeTenant;
exports.updateAccountTenantProfileData = indexNode.updateAccountTenantProfileData;
exports.version = indexNode.version;
exports.JoseHeader = indexBrowser.JoseHeader;
exports.PerformanceClient = indexBrowser.PerformanceClient;
//# sourceMappingURL=index.cjs.map
