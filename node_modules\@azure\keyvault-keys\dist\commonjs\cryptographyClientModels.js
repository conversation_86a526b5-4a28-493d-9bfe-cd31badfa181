"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnownSignatureAlgorithms = exports.KnownKeyTypes = exports.KnownKeyExportEncryptionAlgorithm = exports.KnownKeyCurveNames = exports.KnownEncryptionAlgorithms = void 0;
const index_js_1 = require("./generated/models/index.js");
Object.defineProperty(exports, "KnownKeyCurveNames", { enumerable: true, get: function () { return index_js_1.KnownJsonWebKeyCurveName; } });
Object.defineProperty(exports, "KnownSignatureAlgorithms", { enumerable: true, get: function () { return index_js_1.KnownJsonWebKeySignatureAlgorithm; } });
Object.defineProperty(exports, "KnownEncryptionAlgorithms", { enumerable: true, get: function () { return index_js_1.KnownJsonWebKeyEncryptionAlgorithm; } });
Object.defineProperty(exports, "KnownKeyTypes", { enumerable: true, get: function () { return index_js_1.KnownJsonWebKeyType; } });
Object.defineProperty(exports, "KnownKeyExportEncryptionAlgorithm", { enumerable: true, get: function () { return index_js_1.KnownKeyEncryptionAlgorithm; } });
//# sourceMappingURL=cryptographyClientModels.js.map