{"version": 3, "file": "SignUpClient.mjs", "sources": ["../../../../../../src/custom_auth/sign_up/interaction_client/SignUpClient.ts"], "sourcesContent": [null], "names": ["PublicApiId.SIGN_UP_START", "PublicApiId.SIGN_UP_WITH_PASSWORD_START", "PublicApiId.SIGN_UP_SUBMIT_CODE", "CustomAuthApiErrorCode.UNSUPPORTED_CHALLENGE_TYPE", "PublicApiId.SIGN_UP_SUBMIT_PASSWORD", "PublicApiId.SIGN_UP_SUBMIT_ATTRIBUTES", "CustomAuthApiErrorCode.ATTRIBUTES_REQUIRED", "PublicApiId.SIGN_UP_RESEND_CODE", "CustomAuthApiErrorCode.CREDENTIAL_REQUIRED", "CustomAuthApiErrorCode.INVALID_RESPONSE_BODY", "CustomAuthApiErrorCode.CONTINUATION_TOKEN_MISSING"], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AA2CG,MAAO,YAAa,SAAQ,+BAA+B,CAAA;AAC7D;;;;AAIG;IACH,MAAM,KAAK,CACP,UAA6B,EAAA;AAE7B,QAAA,MAAM,KAAK,GAAG,CAAC,UAAU,CAAC,QAAQ;cAC5BA,aAAyB;AAC3B,cAAEC,2BAAuC,CAAC;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,YAAY,GAAuB;YACrC,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;YAChE,gBAAgB;YAChB,aAAa,EAAE,UAAU,CAAC,aAAa;SAC1C,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qCAAqC,EACrC,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAChE,YAAY,CACf,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oCAAoC,EACpC,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAA2B;AAC7C,YAAA,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,IAAI,EAAE;YAC1D,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;YAChE,gBAAgB;YAChB,aAAa,EAAE,aAAa,CAAC,cAAc;SAC9C,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;KACzD;AAED;;;;AAIG;IACH,MAAM,UAAU,CACZ,UAAkC,EAAA;AAMlC,QAAA,MAAM,KAAK,GAAGC,mBAA+B,CAAC;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,iBAAiB,GAAiC;YACpD,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,GAAG,EAAE,UAAU,CAAC,IAAI;YACpB,gBAAgB;YAChB,aAAa,EAAE,UAAU,CAAC,aAAa;SAC1C,CAAC;AAEF,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC5C,yBAAyB,EACzB,UAAU,EACV,gBAAgB,EAChB,MACI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,gBAAgB,CAC/C,iBAAiB,CACpB,EACL,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,iCAAiC,EAAE;AACnD,YAAA,MAAM,IAAI,kBAAkB,CACxBC,0BAAiD,EACjD,uEAAuE,EACvE,UAAU,CAAC,aAAa,CAC3B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;AAIG;IACH,MAAM,cAAc,CAChB,SAAqC,EAAA;AAMrC,QAAA,MAAM,KAAK,GAAGC,uBAAmC,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,gBAAgB,GAAsC;YACxD,kBAAkB,EAAE,SAAS,CAAC,iBAAiB;YAC/C,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,gBAAgB;YAChB,aAAa,EAAE,SAAS,CAAC,aAAa;SACzC,CAAC;AAEF,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC5C,6BAA6B,EAC7B,SAAS,EACT,gBAAgB,EAChB,MACI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,oBAAoB,CACnD,gBAAgB,CACnB,EACL,SAAS,CAAC,aAAa,CAC1B,CAAC;AAEF,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,qCAAqC,EAAE;AACvD,YAAA,MAAM,IAAI,kBAAkB,CACxBD,0BAAiD,EACjD,gFAAgF,EAChF,SAAS,CAAC,aAAa,CAC1B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;AAIG;IACH,MAAM,gBAAgB,CAClB,SAA2C,EAAA;AAM3C,QAAA,MAAM,KAAK,GAAGE,yBAAqC,CAAC;QACpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AACtE,QAAA,MAAM,WAAW,GAAwC;YACrD,kBAAkB,EAAE,SAAS,CAAC,iBAAiB;YAC/C,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,gBAAgB;YAChB,aAAa,EAAE,SAAS,CAAC,aAAa;SACzC,CAAC;AAEF,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC5C,+BAA+B,EAC/B,SAAS,EACT,gBAAgB,EAChB,MACI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,sBAAsB,CACrD,WAAW,CACd,EACL,SAAS,CAAC,aAAa,CAC1B,CAAC;AAEF,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,uCAAuC,EAAE;YACzD,MAAM,IAAI,kBAAkB,CACxBC,mBAA0C,EAC1C,0BAA0B,EAC1B,SAAS,CAAC,aAAa,EACvB,EAAE,EACF,EAAE,EACF,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,iBAAiB,CAC3B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;AAIG;IACH,MAAM,UAAU,CACZ,UAAkC,EAAA;AAElC,QAAA,MAAM,KAAK,GAAGC,mBAA+B,CAAC;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,gBAAgB,GAA2B;AAC7C,YAAA,kBAAkB,EAAE,UAAU,CAAC,iBAAiB,IAAI,EAAE;YACtD,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;YAChE,gBAAgB;YAChB,aAAa,EAAE,UAAU,CAAC,aAAa;SAC1C,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;AAEpE,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,qCAAqC,EAAE;AACvD,YAAA,MAAM,IAAI,kBAAkB,CACxBJ,0BAAiD,EACjD,4EAA4E,EAC5E,UAAU,CAAC,aAAa,CAC3B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;IAEO,MAAM,uBAAuB,CACjC,OAA+B,EAAA;QAE/B,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yCAAyC,EACzC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,iBAAiB,GACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wCAAwC,EACxC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,IAAI,iBAAiB,CAAC,cAAc,KAAK,aAAa,CAAC,GAAG,EAAE;;YAExD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oCAAoC,EACpC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,YAAA,OAAO,8BAA8B,CAAC;gBAClC,aAAa,EAAE,iBAAiB,CAAC,cAAc;AAC/C,gBAAA,iBAAiB,EAAE,iBAAiB,CAAC,kBAAkB,IAAI,EAAE;AAC7D,gBAAA,gBAAgB,EAAE,iBAAiB,CAAC,iBAAiB,IAAI,EAAE;AAC3D,gBAAA,oBAAoB,EAChB,iBAAiB,CAAC,sBAAsB,IAAI,EAAE;gBAClD,UAAU,EACN,iBAAiB,CAAC,WAAW;oBAC7B,8BAA8B;gBAClC,QAAQ,EACJ,iBAAiB,CAAC,QAAQ;oBAC1B,2CAA2C;AAC/C,gBAAA,aAAa,EAAE,iBAAiB,CAAC,cAAc,IAAI,EAAE;AACxD,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,iBAAiB,CAAC,cAAc,KAAK,aAAa,CAAC,QAAQ,EAAE;;YAE7D,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yCAAyC,EACzC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,YAAA,OAAO,kCAAkC,CAAC;gBACtC,aAAa,EAAE,iBAAiB,CAAC,cAAc;AAC/C,gBAAA,iBAAiB,EAAE,iBAAiB,CAAC,kBAAkB,IAAI,EAAE;AAChE,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA+B,4BAAA,EAAA,iBAAiB,CAAC,cAAc,gBAAgB,EAC/E,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,IAAI,kBAAkB,CACxBA,0BAAiD,EACjD,CAAA,4BAAA,EAA+B,iBAAiB,CAAC,cAAc,CAAI,EAAA,CAAA,EACnE,OAAO,CAAC,aAAa,CACxB,CAAC;KACL;IAEO,MAAM,sBAAsB,CAChC,UAAkB,EAClB,aAA+B,EAC/B,gBAAwC,EACxC,cAAqD,EACrD,oBAA4B,EAAA;QAO5B,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAG,EAAA,UAAU,CAA4C,0CAAA,CAAA,EACzD,oBAAoB,CACvB,CAAC;QAEF,IAAI;AACA,YAAA,MAAM,QAAQ,GAAG,MAAM,cAAc,EAAE,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAA+B,4BAAA,EAAA,UAAU,CAAe,aAAA,CAAA,EACxD,oBAAoB,CACvB,CAAC;AAEF,YAAA,OAAO,2BAA2B,CAAC;AAC/B,gBAAA,aAAa,EAAE,oBAAoB;AACnC,gBAAA,iBAAiB,EAAE,QAAQ,CAAC,kBAAkB,IAAI,EAAE;AACvD,aAAA,CAAC,CAAC;AACN,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,KAAK,YAAY,kBAAkB,EAAE;AACrC,gBAAA,OAAO,IAAI,CAAC,2BAA2B,CACnC,KAAK,EACL,KAAK,CAAC,aAAa,IAAI,oBAAoB,EAC3C,aAAa,EACb,gBAAgB,CACnB,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAAA,EAAG,UAAU,CAAA,yDAAA,EAA4D,KAAK,CAAA,CAAE,EAChF,oBAAoB,CACvB,CAAC;AAEF,gBAAA,MAAM,IAAI,eAAe,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;AAC1D,aAAA;AACJ,SAAA;KACJ;IAEO,MAAM,2BAA2B,CACrC,aAAiC,EACjC,aAAqB,EACrB,aAA+B,EAC/B,gBAAwC,EAAA;QAMxC,IACI,aAAa,CAAC,KAAK;AACf,YAAAK,mBAA0C;YAC9C,CAAC,CAAC,aAAa,CAAC,UAAU;AAC1B,YAAA,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC1C;;YAEE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iDAAiD,EACjD,aAAa,CAChB,CAAC;YAEF,MAAM,iBAAiB,GACnB,IAAI,CAAC,qCAAqC,CAAC,aAAa,CAAC,CAAC;;AAG9D,YAAA,MAAM,gBAAgB,GAA2B;AAC7C,gBAAA,kBAAkB,EAAE,iBAAiB;gBACrC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAClC,aAAa,CAAC,aAAa,CAC9B;gBACD,gBAAgB;gBAChB,aAAa;aAChB,CAAC;YAEF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACtD,gBAAgB,CACnB,CAAC;AAEF,YAAA,IACI,eAAe,CAAC,IAAI,KAAK,qCAAqC,EAChE;AACE,gBAAA,OAAO,kCAAkC,CAAC;AACtC,oBAAA,aAAa,EAAE,aAAa;oBAC5B,iBAAiB,EAAE,eAAe,CAAC,iBAAiB;AACvD,iBAAA,CAAC,CAAC;AACN,aAAA;AAED,YAAA,IAAI,eAAe,CAAC,IAAI,KAAK,iCAAiC,EAAE;AAC5D,gBAAA,OAAO,8BAA8B,CAAC;oBAClC,aAAa,EAAE,eAAe,CAAC,aAAa;oBAC5C,iBAAiB,EAAE,eAAe,CAAC,iBAAiB;oBACpD,gBAAgB,EAAE,eAAe,CAAC,gBAAgB;oBAClD,oBAAoB,EAAE,eAAe,CAAC,oBAAoB;oBAC1D,UAAU,EAAE,eAAe,CAAC,UAAU;oBACtC,QAAQ,EAAE,eAAe,CAAC,QAAQ;oBAClC,aAAa,EAAE,eAAe,CAAC,aAAa;AAC/C,iBAAA,CAAC,CAAC;AACN,aAAA;YAED,MAAM,IAAI,kBAAkB,CACxBL,0BAAiD,EACjD,sCAAsC,EACtC,aAAa,CAChB,CAAC;AACL,SAAA;QAED,IAAI,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE;;YAE9D,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8CAA8C,EAC9C,aAAa,CAChB,CAAC;YAEF,MAAM,iBAAiB,GACnB,IAAI,CAAC,qCAAqC,CAAC,aAAa,CAAC,CAAC;AAE9D,YAAA,OAAO,oCAAoC,CAAC;AACxC,gBAAA,aAAa,EAAE,aAAa;AAC5B,gBAAA,iBAAiB,EAAE,iBAAiB;AACpC,gBAAA,kBAAkB,EAAE,aAAa,CAAC,UAAU,IAAI,EAAE;AACrD,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,MAAM,aAAa,CAAC;KACvB;IAEO,yBAAyB,CAC7B,aAAiC,EACjC,aAAqB,EAAA;AAErB,QAAA,IACI,aAAa,CAAC,KAAK,KAAKG,mBAA0C,EACpE;YACE,IACI,CAAC,aAAa,CAAC,UAAU;AACzB,gBAAA,aAAa,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EACvC;gBACE,MAAM,IAAI,kBAAkB,CACxBG,qBAA4C,EAC5C,wFAAwF,EACxF,aAAa,CAChB,CAAC;AACL,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAEO,IAAA,qCAAqC,CACzC,aAAiC,EAAA;AAEjC,QAAA,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;AAClC,YAAA,MAAM,IAAI,kBAAkB,CACxBC,0BAAiD,EACjD,oDAAoD,EACpD,aAAa,CAAC,aAAa,CAC9B,CAAC;AACL,SAAA;QAED,OAAO,aAAa,CAAC,iBAAiB,CAAC;KAC1C;AACJ;;;;"}