"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createKey = exports.rotateKey = exports.importKey = exports.deleteKey = exports.updateKey = exports.getKey = exports.getKeyVersions = exports.getKeys = exports.backupKey = exports.restoreKey = exports.encrypt = exports.decrypt = exports.sign = exports.verify = exports.wrapKey = exports.unwrapKey = exports.release = exports.getDeletedKeys = exports.getDeletedKey = exports.purgeDeletedKey = exports.recoverDeletedKey = exports.getKeyRotationPolicy = exports.updateKeyRotationPolicy = exports.getRandomBytes = exports.getKeyAttestation = exports.createKeyVault = void 0;
var keyVaultContext_js_1 = require("./keyVaultContext.js");
Object.defineProperty(exports, "createKeyVault", { enumerable: true, get: function () { return keyVaultContext_js_1.createKeyVault; } });
var operations_js_1 = require("./operations.js");
Object.defineProperty(exports, "getKeyAttestation", { enumerable: true, get: function () { return operations_js_1.getKeyAttestation; } });
Object.defineProperty(exports, "getRandomBytes", { enumerable: true, get: function () { return operations_js_1.getRandomBytes; } });
Object.defineProperty(exports, "updateKeyRotationPolicy", { enumerable: true, get: function () { return operations_js_1.updateKeyRotationPolicy; } });
Object.defineProperty(exports, "getKeyRotationPolicy", { enumerable: true, get: function () { return operations_js_1.getKeyRotationPolicy; } });
Object.defineProperty(exports, "recoverDeletedKey", { enumerable: true, get: function () { return operations_js_1.recoverDeletedKey; } });
Object.defineProperty(exports, "purgeDeletedKey", { enumerable: true, get: function () { return operations_js_1.purgeDeletedKey; } });
Object.defineProperty(exports, "getDeletedKey", { enumerable: true, get: function () { return operations_js_1.getDeletedKey; } });
Object.defineProperty(exports, "getDeletedKeys", { enumerable: true, get: function () { return operations_js_1.getDeletedKeys; } });
Object.defineProperty(exports, "release", { enumerable: true, get: function () { return operations_js_1.release; } });
Object.defineProperty(exports, "unwrapKey", { enumerable: true, get: function () { return operations_js_1.unwrapKey; } });
Object.defineProperty(exports, "wrapKey", { enumerable: true, get: function () { return operations_js_1.wrapKey; } });
Object.defineProperty(exports, "verify", { enumerable: true, get: function () { return operations_js_1.verify; } });
Object.defineProperty(exports, "sign", { enumerable: true, get: function () { return operations_js_1.sign; } });
Object.defineProperty(exports, "decrypt", { enumerable: true, get: function () { return operations_js_1.decrypt; } });
Object.defineProperty(exports, "encrypt", { enumerable: true, get: function () { return operations_js_1.encrypt; } });
Object.defineProperty(exports, "restoreKey", { enumerable: true, get: function () { return operations_js_1.restoreKey; } });
Object.defineProperty(exports, "backupKey", { enumerable: true, get: function () { return operations_js_1.backupKey; } });
Object.defineProperty(exports, "getKeys", { enumerable: true, get: function () { return operations_js_1.getKeys; } });
Object.defineProperty(exports, "getKeyVersions", { enumerable: true, get: function () { return operations_js_1.getKeyVersions; } });
Object.defineProperty(exports, "getKey", { enumerable: true, get: function () { return operations_js_1.getKey; } });
Object.defineProperty(exports, "updateKey", { enumerable: true, get: function () { return operations_js_1.updateKey; } });
Object.defineProperty(exports, "deleteKey", { enumerable: true, get: function () { return operations_js_1.deleteKey; } });
Object.defineProperty(exports, "importKey", { enumerable: true, get: function () { return operations_js_1.importKey; } });
Object.defineProperty(exports, "rotateKey", { enumerable: true, get: function () { return operations_js_1.rotateKey; } });
Object.defineProperty(exports, "createKey", { enumerable: true, get: function () { return operations_js_1.createKey; } });
//# sourceMappingURL=index.js.map