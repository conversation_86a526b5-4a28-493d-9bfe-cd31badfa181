// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * The latest supported Key Vault service API version
 */
export const LATEST_API_VERSION = "7.6";
/** Known values of {@link KeyOperation} that the service accepts. */
export var KnownKeyOperations;
(function (KnownKeyOperations) {
    /** Key operation - encrypt */
    KnownKeyOperations["Encrypt"] = "encrypt";
    /** Key operation - decrypt */
    KnownKeyOperations["Decrypt"] = "decrypt";
    /** Key operation - sign */
    KnownKeyOperations["Sign"] = "sign";
    /** Key operation - verify */
    KnownKeyOperations["Verify"] = "verify";
    /** Key operation - wrapKey */
    KnownKeyOperations["WrapKey"] = "wrapKey";
    /** Key operation - unwrapKey */
    KnownKeyOperations["UnwrapKey"] = "unwrapKey";
    /** Key operation - import */
    KnownKeyOperations["Import"] = "import";
})(KnownKeyOperations || (KnownKeyOperations = {}));
//# sourceMappingURL=keysModels.js.map