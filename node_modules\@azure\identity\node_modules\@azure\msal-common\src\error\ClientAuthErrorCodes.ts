/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

export const clientInfoDecodingError = "client_info_decoding_error";
export const clientInfoEmptyError = "client_info_empty_error";
export const tokenParsingError = "token_parsing_error";
export const nullOrEmptyToken = "null_or_empty_token";
export const endpointResolutionError = "endpoints_resolution_error";
export const networkError = "network_error";
export const openIdConfigError = "openid_config_error";
export const hashNotDeserialized = "hash_not_deserialized";
export const invalidState = "invalid_state";
export const stateMismatch = "state_mismatch";
export const stateNotFound = "state_not_found";
export const nonceMismatch = "nonce_mismatch";
export const authTimeNotFound = "auth_time_not_found";
export const maxAgeTranspired = "max_age_transpired";
export const multipleMatchingTokens = "multiple_matching_tokens";
export const multipleMatchingAccounts = "multiple_matching_accounts";
export const multipleMatchingAppMetadata = "multiple_matching_appMetadata";
export const requestCannotBeMade = "request_cannot_be_made";
export const cannotRemoveEmptyScope = "cannot_remove_empty_scope";
export const cannotAppendScopeSet = "cannot_append_scopeset";
export const emptyInputScopeSet = "empty_input_scopeset";
export const deviceCodePollingCancelled = "device_code_polling_cancelled";
export const deviceCodeExpired = "device_code_expired";
export const deviceCodeUnknownError = "device_code_unknown_error";
export const noAccountInSilentRequest = "no_account_in_silent_request";
export const invalidCacheRecord = "invalid_cache_record";
export const invalidCacheEnvironment = "invalid_cache_environment";
export const noAccountFound = "no_account_found";
export const noCryptoObject = "no_crypto_object";
export const unexpectedCredentialType = "unexpected_credential_type";
export const invalidAssertion = "invalid_assertion";
export const invalidClientCredential = "invalid_client_credential";
export const tokenRefreshRequired = "token_refresh_required";
export const userTimeoutReached = "user_timeout_reached";
export const tokenClaimsCnfRequiredForSignedJwt =
    "token_claims_cnf_required_for_signedjwt";
export const authorizationCodeMissingFromServerResponse =
    "authorization_code_missing_from_server_response";
export const bindingKeyNotRemoved = "binding_key_not_removed";
export const endSessionEndpointNotSupported =
    "end_session_endpoint_not_supported";
export const keyIdMissing = "key_id_missing";
export const noNetworkConnectivity = "no_network_connectivity";
export const userCanceled = "user_canceled";
export const missingTenantIdError = "missing_tenant_id_error";
export const methodNotImplemented = "method_not_implemented";
export const nestedAppAuthBridgeDisabled = "nested_app_auth_bridge_disabled";
