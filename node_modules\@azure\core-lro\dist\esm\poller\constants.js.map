{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../../src/poller/constants.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,CAAC;AACxC;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The default time interval to wait before sending the next polling request.\n */\nexport const POLL_INTERVAL_IN_MS = 2000;\n/**\n * The closed set of terminal states.\n */\nexport const terminalStates = [\"succeeded\", \"canceled\", \"failed\"];\n"]}