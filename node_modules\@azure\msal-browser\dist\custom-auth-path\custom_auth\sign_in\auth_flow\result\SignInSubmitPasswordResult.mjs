/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { SignInSubmitPasswordError } from '../error_type/SignInError.mjs';
import { SignInCompletedState } from '../state/SignInCompletedState.mjs';
import { SignInFailedState } from '../state/SignInFailedState.mjs';
import { SignInSubmitCredentialResult } from './SignInSubmitCredentialResult.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a sign-in submit password operation.
 */
class SignInSubmitPasswordResult extends SignInSubmitCredentialResult {
    static createWithError(error) {
        const result = new SignInSubmitPasswordResult(new SignInFailedState());
        result.error = new SignInSubmitPasswordError(SignInSubmitPasswordResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignInFailedState;
    }
    /**
     * Checks if the result is in a completed state.
     */
    isCompleted() {
        return this.state instanceof SignInCompletedState;
    }
}

export { SignInSubmitPasswordResult };
//# sourceMappingURL=SignInSubmitPasswordResult.mjs.map
