{"version": 3, "file": "SignInClient.mjs", "sources": ["../../../../../../src/custom_auth/sign_in/interaction_client/SignInClient.ts"], "sourcesContent": [null], "names": ["PublicApiId.SIGN_IN_WITH_CODE_START", "PublicApiId.SIGN_IN_WITH_PASSWORD_START", "PublicApiId.SIGN_IN_RESEND_CODE", "CustomAuthApiErrorCode.UNSUPPORTED_CHALLENGE_TYPE", "PublicApiId.SIGN_IN_SUBMIT_CODE", "PublicApiId.SIGN_IN_SUBMIT_PASSWORD", "PublicApiId.SIGN_IN_AFTER_SIGN_UP", "PublicApiId.SIGN_IN_AFTER_PASSWORD_RESET"], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAuDG,MAAO,YAAa,SAAQ,+BAA+B,CAAA;AAG7D,IAAA,WAAA,CACI,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,iBAAqC,EACrC,mBAAyC,EACzC,mBAAwC,EAAA;AAExC,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,CACtB,CAAC;AAEF,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,eAAe,CAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,IAAI,CACP,CAAC;KACL;AAED;;;;AAIG;IACH,MAAM,KAAK,CACP,UAA6B,EAAA;AAE7B,QAAA,MAAM,KAAK,GAAG,CAAC,UAAU,CAAC,QAAQ;cAC5BA,uBAAmC;AACrC,cAAEC,2BAAuC,CAAC;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wCAAwC,EACxC,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,MAAM,OAAO,GAA0B;YACnC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;YAChE,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,aAAa,EAAE,UAAU,CAAC,aAAa;AACvC,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAClB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAE/D,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uCAAuC,EACvC,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,MAAM,YAAY,GAA2B;YACzC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;AAChE,YAAA,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB,IAAI,EAAE;YAC7D,aAAa,EAAE,gBAAgB,CAAC,cAAc;AAC9C,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;KACrD;AAED;;;;AAIG;IACH,MAAM,UAAU,CACZ,UAAkC,EAAA;AAElC,QAAA,MAAM,KAAK,GAAGC,mBAA+B,CAAC;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;AAEtE,QAAA,MAAM,YAAY,GAA2B;YACzC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC;AAChE,YAAA,kBAAkB,EAAE,UAAU,CAAC,iBAAiB,IAAI,EAAE;YACtD,aAAa,EAAE,UAAU,CAAC,aAAa;AACvC,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;AAEhE,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,qCAAqC,EAAE;YACvD,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,qFAAqF,EACrF,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,YAAA,MAAM,IAAI,kBAAkB,CACxBC,0BAAiD,EACjD,wCAAwC,EACxC,MAAM,CAAC,aAAa,CACvB,CAAC;AACL,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;AAIG;IACH,MAAM,UAAU,CACZ,UAAkC,EAAA;QAElC,8BAA8B,CAC1B,iBAAiB,EACjB,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,MAAM,KAAK,GAAGC,mBAA+B,CAAC;QAC9C,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAEjD,QAAA,MAAM,OAAO,GAA0B;YACnC,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,GAAG,EAAE,UAAU,CAAC,IAAI;AACpB,YAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YACvB,aAAa,EAAE,UAAU,CAAC,aAAa;AACvC,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB,CAC3B,MACI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,oBAAoB,CACnD,OAAO,CACV,EACL,MAAM,CACT,CAAC;KACL;AAED;;;;AAIG;IACH,MAAM,cAAc,CAChB,UAAsC,EAAA;QAEtC,8BAA8B,CAC1B,qBAAqB,EACrB,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,aAAa,CAC3B,CAAC;AAEF,QAAA,MAAM,KAAK,GAAGC,uBAAmC,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAEjD,QAAA,MAAM,OAAO,GAA+B;YACxC,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,QAAQ,EAAE,UAAU,CAAC,QAAQ;AAC7B,YAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YACvB,aAAa,EAAE,UAAU,CAAC,aAAa;AACvC,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB,CAC3B,MACI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,yBAAyB,CACxD,OAAO,CACV,EACL,MAAM,CACT,CAAC;KACL;AAED;;;;AAIG;IACH,MAAM,2BAA2B,CAC7B,UAAyC,EAAA;AAEzC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAC7C,UAAU,CAAC,cAAc,EACzB,UAAU,CAAC,aAAa,CAC3B,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;AAGjD,QAAA,MAAM,OAAO,GAAmC;YAC5C,kBAAkB,EAAE,UAAU,CAAC,iBAAiB;YAChD,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,aAAa,EAAE,UAAU,CAAC,aAAa;AACvC,YAAA,gBAAgB,EAAE,gBAAgB;AAClC,YAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;SAC1B,CAAC;;QAGF,OAAO,IAAI,CAAC,mBAAmB,CAC3B,MACI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,iCAAiC,CAChE,OAAO,CACV,EACL,MAAM,CACT,CAAC;KACL;AAEO,IAAA,MAAM,mBAAmB,CAC7B,mBAAuD,EACvD,aAAuB,EAAA;QAEvB,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qCAAqC,EACrC,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;AACnE,QAAA,MAAM,aAAa,GAAG,MAAM,mBAAmB,EAAE,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oCAAoC,EACpC,IAAI,CAAC,aAAa,CACrB,CAAC;;AAGF,QAAA,MAAM,MAAM,GACR,MAAM,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CACrD,aAAa,EACb,IAAI,CAAC,mBAAmB,EACxB,gBAAgB,EAChB;AACI,YAAA,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,kBAAkB;AACtD,YAAA,aAAa,EAAE,aAAa,CAAC,cAAc,IAAI,EAAE;AACjD,YAAA,MAAM,EAAE,aAAa;AACrB,YAAA,YAAY,EAAE;AACV,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,YAAY,EAAE,IAAI;AACrB,aAAA;AACJ,SAAA,CACJ,CAAC;AAEN,QAAA,OAAO,0BAA0B,CAAC;AAC9B,YAAA,aAAa,EAAE,aAAa,CAAC,cAAc,IAAI,EAAE;AACjD,YAAA,oBAAoB,EAAE,MAA8B;AACvD,SAAA,CAAC,CAAC;KACN;IAEO,MAAM,uBAAuB,CACjC,OAA+B,EAAA;QAE/B,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yCAAyC,EACzC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,iBAAiB,GACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wCAAwC,EACxC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,IAAI,iBAAiB,CAAC,cAAc,KAAK,aAAa,CAAC,GAAG,EAAE;;YAExD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oCAAoC,EACpC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,YAAA,OAAO,0BAA0B,CAAC;gBAC9B,aAAa,EAAE,iBAAiB,CAAC,cAAc;AAC/C,gBAAA,iBAAiB,EAAE,iBAAiB,CAAC,kBAAkB,IAAI,EAAE;AAC7D,gBAAA,gBAAgB,EAAE,iBAAiB,CAAC,iBAAiB,IAAI,EAAE;AAC3D,gBAAA,oBAAoB,EAChB,iBAAiB,CAAC,sBAAsB,IAAI,EAAE;gBAClD,UAAU,EACN,iBAAiB,CAAC,WAAW;oBAC7B,8BAA8B;AAClC,gBAAA,aAAa,EAAE,iBAAiB,CAAC,cAAc,IAAI,EAAE;AACxD,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,iBAAiB,CAAC,cAAc,KAAK,aAAa,CAAC,QAAQ,EAAE;;YAE7D,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yCAAyC,EACzC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,YAAA,OAAO,kCAAkC,CAAC;gBACtC,aAAa,EAAE,iBAAiB,CAAC,cAAc;AAC/C,gBAAA,iBAAiB,EAAE,iBAAiB,CAAC,kBAAkB,IAAI,EAAE;AAChE,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA+B,4BAAA,EAAA,iBAAiB,CAAC,cAAc,gBAAgB,EAC/E,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,IAAI,kBAAkB,CACxBF,0BAAiD,EACjD,CAAA,4BAAA,EAA+B,iBAAiB,CAAC,cAAc,CAAI,EAAA,CAAA,EACnE,iBAAiB,CAAC,cAAc,CACnC,CAAC;KACL;IAEO,8BAA8B,CAClC,QAA4B,EAC5B,aAAqB,EAAA;AAErB,QAAA,QAAQ,QAAQ;YACZ,KAAK,cAAc,CAAC,iBAAiB;gBACjC,OAAOG,qBAAiC,CAAC;YAC7C,KAAK,cAAc,CAAC,wBAAwB;gBACxC,OAAOC,4BAAwC,CAAC;AACpD,YAAA;gBACI,MAAM,IAAI,eAAe,CACrB,CAAA,8BAAA,EAAiC,QAAQ,CAAI,EAAA,CAAA,EAC7C,aAAa,CAChB,CAAC;AACT,SAAA;KACJ;AACJ;;;;"}