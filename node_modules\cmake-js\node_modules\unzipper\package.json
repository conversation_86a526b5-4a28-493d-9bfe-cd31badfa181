{"name": "unzipper", "version": "0.8.14", "description": "Unzip cross-platform streaming API ", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ZJONSSON/node-unzipper.git"}, "license": "MIT", "dependencies": {"big-integer": "^1.6.17", "binary": "~0.3.0", "bluebird": "~3.4.1", "buffer-indexof-polyfill": "~1.0.0", "duplexer2": "~0.1.4", "fstream": "~1.0.10", "listenercount": "~1.0.1", "readable-stream": "~2.1.5", "setimmediate": "~1.0.4"}, "devDependencies": {"aws-sdk": "^2.77.0", "request": "2.79.0", "tap": ">= 0.3.0 < 1", "temp": ">= 0.4.0 < 1", "dirdiff": ">= 0.0.1 < 1", "stream-buffers": ">= 0.2.5 < 1"}, "directories": {"example": "examples", "test": "test"}, "keywords": ["zip", "unzip", "zlib", "uncompress", "archive", "stream", "extract"], "main": "unzip.js", "scripts": {"test": "tap ./test/*.js"}}