{"version": 3, "file": "crypto-browser.mjs", "sourceRoot": "", "sources": ["../../../src/cryptography/crypto-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,iCAAiC,EAAE,MAAM,aAAa,CAAC;AAEhE;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,UAAkB,EAAE,KAAiB;IACpE,MAAM,IAAI,iCAAiC,CACzC,uDAAuD,CACxD,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,UAAkB,EAAE,KAAiB;IAChE,MAAM,IAAI,iCAAiC,CACzC,uDAAuD,CACxD,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,OAAe;IACzC,MAAM,IAAI,iCAAiC,CACzC,sDAAsD,CACvD,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { LocalCryptographyUnsupportedError } from \"./models.js\";\n\n/**\n * @internal\n * Use the platform-local hashing functionality\n */\nexport async function createHash(_algorithm: string, _data: Uint8Array): Promise<Buffer> {\n  throw new LocalCryptographyUnsupportedError(\n    \"Our libraries don't currently support browser hashing\",\n  );\n}\n\n/**\n * @internal\n * Use the platform-local verify functionality\n */\nexport function createVerify(_algorithm: string, _data: Uint8Array): never {\n  throw new LocalCryptographyUnsupportedError(\n    \"Our libraries don't currently support browser hashing\",\n  );\n}\n\n/**\n * @internal\n * Use the platform-local randomBytes functionality\n */\nexport function randomBytes(_length: number): Uint8Array {\n  throw new LocalCryptographyUnsupportedError(\n    \"Our libraries don't currently support browser crypto\",\n  );\n}\n"]}