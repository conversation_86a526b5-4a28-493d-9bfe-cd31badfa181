{"version": 3, "file": "ScopeSet.mjs", "sources": ["../../src/request/ScopeSet.ts"], "sourcesContent": [null], "names": ["ClientConfigurationErrorCodes.emptyInputScopesError", "ClientAuthErrorCodes.cannotAppendScopeSet", "ClientAuthErrorCodes.cannotRemoveEmptyScope", "ClientAuthErrorCodes.emptyInputScopeSet"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAaH;;;;AAIG;MACU,QAAQ,CAAA;AAIjB,IAAA,WAAA,CAAY,WAA0B,EAAA;;QAElC,MAAM,QAAQ,GAAG,WAAW;cACtB,WAAW,CAAC,gBAAgB,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;cAC9C,EAAE,CAAC;QACT,MAAM,aAAa,GAAG,QAAQ;AAC1B,cAAE,WAAW,CAAC,2BAA2B,CAAC,QAAQ,CAAC;cACjD,EAAE,CAAC;;AAGT,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AACzC,YAAA,MAAM,8BAA8B,CAChCA,qBAAmD,CACtD,CAAC;AACL,SAAA;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;AAChC,QAAA,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;KAC5D;AAED;;;;;AAKG;IACH,OAAO,UAAU,CAAC,gBAAwB,EAAA;AACtC,QAAA,MAAM,WAAW,GAAG,gBAAgB,IAAI,SAAS,CAAC,YAAY,CAAC;QAC/D,MAAM,WAAW,GAAkB,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;KACpC;AAED;;;;AAIG;IACH,OAAO,kBAAkB,CAAC,gBAA+B,EAAA;AACrD,QAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,EAAE;YACpC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;AAC/B,SAAA;AAAM,aAAA;AACH,YAAA,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;AACxD,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB;AAED;;;AAGG;AACH,IAAA,aAAa,CAAC,KAAa,EAAA;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/D,QAAA,MAAM,kBAAkB,GAAG,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC;;AAEzD,QAAA,OAAO,KAAK;cACN,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;cAClD,KAAK,CAAC;KACf;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,QAAkB,EAAA;QAC/B,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,QACI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI;AACxC,YAAA,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAChE;KACL;AAED;;AAEG;IACH,sBAAsB,GAAA;QAClB,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAC1B,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,YAAoB,KAAI;AACzC,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE;gBAClC,iBAAiB,IAAI,CAAC,CAAC;AAC1B,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,iBAAiB,CAAC;KACjD;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,QAAgB,EAAA;AACxB,QAAA,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AACpC,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,SAAwB,EAAA;QACjC,IAAI;AACA,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/D,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,qBAAqB,CACvBC,oBAAyC,CAC5C,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,KAAa,EAAA;QACrB,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,qBAAqB,CACvBC,sBAA2C,CAC9C,CAAC;AACL,SAAA;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;KACpC;AAED;;;AAGG;IACH,gBAAgB,GAAA;AACZ,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,YAAoB,KAAI;AACzC,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACrC,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,WAAqB,EAAA;QAChC,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,MAAM,qBAAqB,CACvBC,kBAAuC,CAC1C,CAAC;AACL,SAAA;AACD,QAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAC7B,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CACvC,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AACrE,QAAA,OAAO,WAAW,CAAC;KACtB;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAC,WAAqB,EAAA;QACvC,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,MAAM,qBAAqB,CACvBA,kBAAuC,CAC1C,CAAC;AACL,SAAA;;AAGD,QAAA,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,EAAE;YACvC,WAAW,CAAC,gBAAgB,EAAE,CAAC;AAClC,SAAA;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AACrD,QAAA,MAAM,eAAe,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;AACpD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AAC5C,QAAA,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC;AACzC,QAAA,OAAO,eAAe,GAAG,cAAc,GAAG,eAAe,CAAC;KAC7D;AAED;;AAEG;IACH,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;KAC3B;AAED;;AAEG;IACH,OAAO,GAAA;QACH,MAAM,KAAK,GAAkB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C,QAAA,OAAO,KAAK,CAAC;KAChB;AAED;;AAEG;IACH,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAChC,YAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAA;QACD,OAAO,SAAS,CAAC,YAAY,CAAC;KACjC;AAED;;AAEG;IACH,oBAAoB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,CAAC;KAC3C;AACJ;;;;"}