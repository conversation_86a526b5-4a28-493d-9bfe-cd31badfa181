{"version": 3, "file": "tracing.js", "sourceRoot": "", "sources": ["../../src/tracing.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,sDAA0D;AAC1D,iDAA6C;AAEhC,QAAA,aAAa,GAAG,IAAA,kCAAmB,EAAC;IAC/C,SAAS,EAAE,oBAAoB;IAC/B,WAAW,EAAE,sBAAsB;IACnC,cAAc,EAAE,0BAAW;CAC5B,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createTracingClient } from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"./constants.js\";\n\nexport const tracingClient = createTracingClient({\n  namespace: \"Microsoft.KeyVault\",\n  packageName: \"@azure/keyvault-keys\",\n  packageVersion: SDK_VERSION,\n});\n"]}