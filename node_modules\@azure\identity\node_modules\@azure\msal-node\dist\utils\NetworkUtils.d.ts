import { NetworkResponse } from "@azure/msal-common/node";
export type UrlToHttpRequestOptions = {
    protocol: string;
    hostname: string;
    hash: string;
    search: string;
    pathname: string;
    path: string;
    href: string;
    port?: number;
    auth?: string;
};
export declare class NetworkUtils {
    static getNetworkResponse<T>(headers: Record<string, string>, body: T, statusCode: number): NetworkResponse<T>;
    static urlToHttpOptions(url: URL): UrlToHttpRequestOptions;
}
//# sourceMappingURL=NetworkUtils.d.ts.map