{"version": 3, "file": "BrowserConstants.mjs", "sources": ["../../../../src/utils/BrowserConstants.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAMH;;AAEG;AACU,MAAA,gBAAgB,GAAG;AAC5B,IAIA;;AAEG;AACH,IAAA,mBAAmB,EAAE,eAAe;AACpC;;AAEG;AACH,IAAA,WAAW,EAAE,GAAG;AAChB;;AAEG;AACH,IAAA,YAAY,EAAE,GAAG;AACjB;;AAEG;AACH,IAAA,iBAAiB,EAAE,MAAM;AACzB;;AAEG;AACH,IAAA,wBAAwB,EAAE,EAAE;AAC5B;;AAEG;AACH,IAAA,QAAQ,EAAE,iBAAiB;EAC7B;AAEW,MAAA,qBAAqB,GAAG;AACjC,IAAA,UAAU,EAAE,sCAAsC;AAClD,IAAA,sBAAsB,EAAE,kCAAkC;AAC1D,IAAA,cAAc,EAAE,MAAM;AACtB,IAAA,wBAAwB,EAAE,gBAAgB;AAC1C,IAAA,YAAY,EAAE,SAAS;AACvB,IAAA,iBAAiB,EAAE,wBAAwB;AAC3C,IAAA,qBAAqB,EAAE,wBAAwB;AAC/C,IAAA,2BAA2B,EAAE,8BAA8B;EAC7D;AAEW,MAAA,qBAAqB,GAAG;AACjC,IAAA,gBAAgB,EAAE,WAAW;AAC7B,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,QAAQ,EAAE,UAAU;EACb;AAIE,MAAA,oBAAoB,GAAG;AAChC,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,aAAa,EAAE,eAAe;EACvB;AAIX;;AAEG;AACU,MAAA,iBAAiB,GAAG;AAC7B,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,IAAI,EAAE,MAAM;EACL;AAIE,MAAA,gBAAgB,GAAG;AAC5B,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,OAAO,EAAE,SAAS;EACX;AAIX;;AAEG;AACU,MAAA,kBAAkB,GAAG;AAC9B,IAAA,UAAU,EAAE,gBAAgB;AAC5B,IAAA,QAAQ,EAAE,SAAS;AACnB,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,QAAQ,EAAE,eAAe;AACzB,IAAA,sBAAsB,EAAE,oBAAoB;AAC5C,IAAA,cAAc,EAAE,gBAAgB;EACzB;AAIE,MAAA,eAAe,GAAG;AAC3B,IAAA,YAAY,EAAE,mBAAmB;AACjC,IAAA,UAAU,EAAE,iBAAiB;AAC7B,IAAA,OAAO,EAAE,cAAc;EAChB;AAIX;;AAEG;AACU,MAAA,iBAAiB,GAAG;AAC7B,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,WAAW,EAAE,iBAAiB;EACvB;AAIX;;;;;AAKG;AACU,MAAA,KAAK,GAAG;AACjB,IAAA,oBAAoB,EAAE,GAAG;AACzB,IAAA,iBAAiB,EAAE,GAAG;AACtB,IAAA,SAAS,EAAE,GAAG;AACd,IAAA,2BAA2B,EAAE,GAAG;AAChC,IAAA,qBAAqB,EAAE,GAAG;AAC1B,IAAA,kBAAkB,EAAE,GAAG;AACvB,IAAA,6BAA6B,EAAE,EAAE;AACjC,IAAA,MAAM,EAAE,GAAG;AACX,IAAA,WAAW,EAAE,GAAG;EACT;AAGX;;AAEG;IACS,gBAKX;AALD,CAAA,UAAY,eAAe,EAAA;AACvB,IAAA,eAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,eAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EALW,eAAe,KAAf,eAAe,GAK1B,EAAA,CAAA,CAAA,CAAA;AAuCY,MAAA,eAAe,GAAmC;AAC3D,IAAA,MAAM,EAAE,mBAAmB;EAC7B;AAEF;;AAEG;AACI,MAAM,cAAc,GAAG,MAAM;AASpC;AACO,MAAM,OAAO,GAAG,UAAU;AAC1B,MAAM,UAAU,GAAG,EAAE;AACf,MAAA,aAAa,GAAG,CAAG,EAAA,OAAO,QAAQ;AAElC,MAAA,iBAAiB,GAAG;AAC7B;;;;AAIG;AACH,IAAA,OAAO,EAAE,CAAC;AACV;;;AAGG;AACH,IAAA,WAAW,EAAE,CAAC;AACd;;;;AAIG;AACH,IAAA,0BAA0B,EAAE,CAAC;AAC7B;;;;AAIG;AACH,IAAA,YAAY,EAAE,CAAC;AACf;;;;AAIG;AACH,IAAA,sBAAsB,EAAE,CAAC;AACzB;;;AAGG;AACH,IAAA,IAAI,EAAE,CAAC;EACA;AAIE,MAAA,qBAAqB,GAAwB;AACtD,IAAA,iBAAiB,CAAC,OAAO;AACzB,IAAA,iBAAiB,CAAC,IAAI;AACtB,IAAA,iBAAiB,CAAC,sBAAsB;EAC1C;AAEK,MAAM,mBAAmB,GAAG,yBAAyB;AACrD,MAAM,iBAAiB,GAAG,uBAAuB;AAIjD,MAAM,yBAAyB,GAAG;;;;"}