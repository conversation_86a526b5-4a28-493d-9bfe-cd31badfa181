{"version": 3, "file": "subscriptionUtils.js", "sourceRoot": "", "sources": ["../../../src/util/subscriptionUtils.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAQlC,8CAUC;AAfD,6CAA2C;AAE3C;;GAEG;AACH,SAAgB,iBAAiB,CAAC,MAAwB,EAAE,YAAoB;IAC9E,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,iBAAiB,YAAY,4EAA4E;YACvG,8FAA8F;YAC9F,2EAA2E,CAC9E,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,IAAA,wBAAW,EAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CredentialLogger } from \"./logging.js\";\nimport { formatError } from \"./logging.js\";\n\n/**\n * @internal\n */\nexport function checkSubscription(logger: CredentialLogger, subscription: string): void {\n  if (!subscription.match(/^[0-9a-zA-Z-._ ]+$/)) {\n    const error = new Error(\n      `Subscription '${subscription}' contains invalid characters. If this is the name of a subscription, use ` +\n        `its ID instead. You can locate your subscription by following the instructions listed here: ` +\n        `https://learn.microsoft.com/azure/azure-portal/get-subscription-tenant-id`,\n    );\n    logger.info(formatError(\"\", error));\n    throw error;\n  }\n}\n"]}