{"version": 3, "file": "ProtocolUtils.mjs", "sources": ["../../src/utils/ProtocolUtils.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.noCryptoObject", "ClientAuthErrorCodes.invalidState"], "mappings": ";;;;;;AAAA;;;AAGG;AA6BH;;AAEG;MACU,aAAa,CAAA;AACtB;;;;AAIG;AACH,IAAA,OAAO,eAAe,CAClB,SAAkB,EAClB,SAAkB,EAClB,IAA6B,EAAA;QAE7B,MAAM,YAAY,GAAG,aAAa,CAAC,oBAAoB,CACnD,SAAS,EACT,IAAI,CACP,CAAC;AACF,QAAA,OAAO,SAAS;cACV,GAAG,YAAY,CAAA,EAAG,SAAS,CAAC,cAAc,CAAG,EAAA,SAAS,CAAE,CAAA;cACxD,YAAY,CAAC;KACtB;AAED;;;;AAIG;AACH,IAAA,OAAO,oBAAoB,CACvB,SAAkB,EAClB,IAA6B,EAAA;QAE7B,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,MAAM,qBAAqB,CAACA,cAAmC,CAAC,CAAC;AACpE,SAAA;;AAGD,QAAA,MAAM,QAAQ,GAAuB;AACjC,YAAA,EAAE,EAAE,SAAS,CAAC,aAAa,EAAE;SAChC,CAAC;AAEF,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,SAAA;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAE7C,QAAA,OAAO,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KAC9C;AAED;;;;AAIG;AACH,IAAA,OAAO,iBAAiB,CACpB,SAAkB,EAClB,KAAa,EAAA;QAEb,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,MAAM,qBAAqB,CAACA,cAAmC,CAAC,CAAC;AACpE,SAAA;QAED,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,qBAAqB,CAACC,YAAiC,CAAC,CAAC;AAClE,SAAA;QAED,IAAI;;YAEA,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AACzD,YAAA,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,YAAA,MAAM,SAAS,GACX,UAAU,CAAC,MAAM,GAAG,CAAC;AACjB,kBAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;AACpD,kBAAE,SAAS,CAAC,YAAY,CAAC;YACjC,MAAM,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAChE,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAC9B,kBAAkB,CACC,CAAC;YACxB,OAAO;AACH,gBAAA,gBAAgB,EAAE,SAAS,IAAI,SAAS,CAAC,YAAY;AACrD,gBAAA,YAAY,EAAE,eAAe;aAChC,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,qBAAqB,CAACA,YAAiC,CAAC,CAAC;AAClE,SAAA;KACJ;AACJ;;;;"}