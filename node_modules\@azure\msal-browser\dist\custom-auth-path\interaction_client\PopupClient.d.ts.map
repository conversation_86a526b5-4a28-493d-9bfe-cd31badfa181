{"version": 3, "file": "PopupClient.d.ts", "sourceRoot": "", "sources": ["../../../src/interaction_client/PopupClient.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,uBAAuB,EACvB,uBAAuB,EAKvB,kBAAkB,EAClB,MAAM,EACN,OAAO,EAKP,SAAS,EACT,6BAA6B,EAEhC,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,yBAAyB,EAAE,MAAM,gCAAgC,CAAC;AAO3E,OAAO,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAG9E,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAK1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AACvE,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AAE5E,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAM3E,OAAO,EAAE,oBAAoB,EAAE,MAAM,gDAAgD,CAAC;AAGtF,MAAM,MAAM,WAAW,GAAG;IACtB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;IAClB,qBAAqB,EAAE,qBAAqB,CAAC;IAC7C,iBAAiB,EAAE,MAAM,CAAC;CAC7B,CAAC;AAEF,qBAAa,WAAY,SAAQ,yBAAyB;IACtD,OAAO,CAAC,aAAa,CAAqB;IAC1C,SAAS,CAAC,aAAa,EAAE,mBAAmB,CAAC;gBAGzC,MAAM,EAAE,oBAAoB,EAC5B,WAAW,EAAE,mBAAmB,EAChC,aAAa,EAAE,OAAO,EACtB,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,YAAY,EAC1B,gBAAgB,EAAE,iBAAiB,EACnC,iBAAiB,EAAE,kBAAkB,EACrC,iBAAiB,EAAE,mBAAmB,EACtC,mBAAmB,CAAC,EAAE,oBAAoB,EAC1C,aAAa,CAAC,EAAE,MAAM;IAmB1B;;;;OAIG;IACH,YAAY,CACR,OAAO,EAAE,YAAY,EACrB,SAAS,CAAC,EAAE,SAAS,GACtB,OAAO,CAAC,oBAAoB,CAAC;IAuDhC;;;OAGG;IACH,MAAM,CAAC,aAAa,CAAC,EAAE,sBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;IA6C7D;;;;;;;OAOG;cACa,sBAAsB,CAClC,OAAO,EAAE,YAAY,EACrB,WAAW,EAAE,WAAW,EACxB,SAAS,CAAC,EAAE,SAAS,GACtB,OAAO,CAAC,oBAAoB,CAAC;IAkChC;;;;;;OAMG;IACG,eAAe,CACjB,OAAO,EAAE,6BAA6B,EACtC,WAAW,EAAE,WAAW,EACxB,SAAS,CAAC,EAAE,SAAS,GACtB,OAAO,CAAC,oBAAoB,CAAC;IA2HhC;;;OAGG;IACG,cAAc,CAChB,OAAO,EAAE,6BAA6B,EACtC,WAAW,EAAE,WAAW,GACzB,OAAO,CAAC,oBAAoB,CAAC;IAkF1B,uBAAuB,CACzB,OAAO,EAAE,6BAA6B,EACtC,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,uBAAuB,EACnC,YAAY,EAAE,MAAM,GACrB,OAAO,CAAC,oBAAoB,CAAC;IAyEhC;;;;;;;;OAQG;cACa,gBAAgB,CAC5B,YAAY,EAAE,uBAAuB,EACrC,WAAW,EAAE,WAAW,EACxB,gBAAgB,CAAC,EAAE,MAAM,EACzB,qBAAqB,CAAC,EAAE,MAAM,GAC/B,OAAO,CAAC,IAAI,CAAC;IAsJhB;;;OAGG;IACH,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,GAAG,MAAM;IAepE;;;;OAIG;IACH,mBAAmB,CACf,WAAW,EAAE,MAAM,EACnB,iBAAiB,EAAE,MAAM,GAC1B,OAAO,CAAC,MAAM,CAAC;IA2DlB;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,GAAG,MAAM;IA4ChE;;;;;;OAMG;IACH,cAAc,CACV,WAAW,EAAE,MAAM,EACnB,EAAE,SAAS,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,EAAE,WAAW,GACrE,MAAM,GAAG,IAAI;IAsEhB;;OAEG;IACH,YAAY,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;IAQ5B;;;OAGG;IACH,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,GAAG,IAAI;IAWhE;;;;OAIG;IACH,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,MAAM;IAMnE;;;;OAIG;IACH,uBAAuB,CAAC,OAAO,EAAE,uBAAuB,GAAG,MAAM;CAIpE"}