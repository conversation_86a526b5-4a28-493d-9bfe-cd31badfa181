{"version": 3, "file": "SilentCacheClient.mjs", "sources": ["../../../../src/interaction_client/SilentCacheClient.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.cryptoKeyNotFound"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAiBG,MAAO,iBAAkB,SAAQ,yBAAyB,CAAA;AAC5D;;;AAGG;IACH,MAAM,YAAY,CACd,aAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,6BAA6B,EAC/C,aAAa,CAAC,aAAa,CAC9B,CAAC;;QAEF,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,6BAA6B,CACtC,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,MAAM,WAAW,CAClC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;YACE,sBAAsB;YACtB,gBAAgB,EAAE,aAAa,CAAC,SAAS;YACzC,wBAAwB,EAAE,aAAa,CAAC,iBAAiB;YACzD,OAAO,EAAE,aAAa,CAAC,OAAO;AACjC,SAAA,CAAC,CAAC;QACH,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CACzC,YAAY,EACZ,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAElD,IAAI;AACA,YAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAC9B,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC1D,iBAAiB,CAAC,kCAAkC,EACpD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAAC,aAAa,CAC9B,CAAC,aAAa,CAAC,CAAC;AACjB,YAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAyB,CAAC;AAEzD,YAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC5B;AACI,gBAAA,SAAS,EAAE,IAAI;AAClB,aAAA,EACD,aAAa,CAAC,aAAa,CAC9B,CAAC;AACF,YAAA,OAAO,YAAY,CAAC;AACvB,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IACI,KAAK,YAAY,gBAAgB;AACjC,gBAAA,KAAK,CAAC,SAAS,KAAKA,iBAAuC,EAC7D;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sHAAsH,CACzH,CAAC;AACL,aAAA;AACD,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,MAAM,CAAC,aAAiC,EAAA;AACpC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;AACvE,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAC1B,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,EAAE,OAAO,CAC9B,CAAC;KACL;AACJ;;;;"}