{"version": 3, "file": "CustomAuthConstants.mjs", "sources": ["../../../../src/custom_auth/CustomAuthConstants.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAKU,MAAA,SAAS,GAAG;AACrB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,kBAAkB,EAAE,oBAAoB;AACxC,IACA,UAAU,EAAE,YAAY;EACjB;AAEE,MAAA,aAAa,GAAG;AACzB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,QAAQ,EAAE,UAAU;EACb;AAEE,MAAA,aAAa,GAAG;AACzB,IAAA,SAAS,CAAC,YAAY;AACtB,IAAA,SAAS,CAAC,aAAa;AACvB,IAAA,SAAS,CAAC,oBAAoB;EACvB;AAEE,MAAA,cAAc,GAAG;AAC1B,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,eAAe,EAAE,iBAAiB;EAC3B;AAEE,MAAA,kBAAkB,GAAG;AAC9B,IAAA,GAAG,EAAE,cAAc;AACnB,IAAA,OAAO,EAAE,OAAO;AAChB,IAAA,EAAE,EAAE,EAAE;AACN,IAAA,GAAG,EAAE,EAAE;EACA;AAEE,MAAA,uBAAuB,GAAG;AACnC,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,WAAW,EAAE,aAAa;EACnB;MAEE,8BAA8B,GAAG,GAAG;AACpC,MAAA,2CAA2C,GAAG,IAAI;AAClD,MAAA,+BAA+B,GAAG,OAAO;;;;"}