// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
export { createKeyVault, } from "./keyVaultContext.js";
export { getKeyAttestation, getRandomBytes, updateKeyRotationPolicy, getKeyRotationPolicy, recoverDeletedKey, purgeDeletedKey, getDeletedKey, getDeletedKeys, release, unwrapKey, wrapKey, verify, sign, decrypt, encrypt, restoreKey, backupKey, getKeys, getKeyVersions, getKey, updateKey, deleteKey, importKey, rotateKey, createKey, } from "./operations.js";
//# sourceMappingURL=index.js.map