"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.terminalStates = exports.POLL_INTERVAL_IN_MS = void 0;
/**
 * The default time interval to wait before sending the next polling request.
 */
exports.POLL_INTERVAL_IN_MS = 2000;
/**
 * The closed set of terminal states.
 */
exports.terminalStates = ["succeeded", "canceled", "failed"];
//# sourceMappingURL=constants.js.map