{"version": 3, "file": "PopupClient.mjs", "sources": ["../../src/interaction_client/PopupClient.ts"], "sourcesContent": [null], "names": ["BrowserUtils.preconnect", "Authorize.getAuthCodeRequestUrl", "ResponseHandler.deserializeResponse", "Authorize.handleResponseCode", "Authorize.getEARForm", "Authorize.handleResponseEAR", "Authorize.getCodeForm", "BrowserUtils.getCurrentUri", "BrowserAuthErrorCodes.emptyNavigateUri", "BrowserAuthErrorCodes.userCancelled", "BrowserAuthErrorCodes.emptyWindowError", "BrowserAuthErrorCodes.popupWindowError"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;AAGG;AAyDG,MAAO,WAAY,SAAQ,yBAAyB,CAAA;AAItD,IAAA,WAAA,CACI,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,iBAAqC,EACrC,iBAAsC,EACtC,mBAA0C,EAC1C,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,aAAa,CAChB,CAAC;;QAEF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;AACvC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC;AAED;;;;AAIG;IACH,YAAY,CACR,OAAqB,EACrB,SAAqB,EAAA;QAErB,IAAI,WAAW,GAA4B,SAAS,CAAC;QACrD,IAAI;YACA,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CACpC,OAAO,CAAC,MAAM,IAAI,mBAAmB,EACrC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAClD,CAAC;AACF,YAAA,WAAW,GAAG;gBACV,SAAS;AACT,gBAAA,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,IAAI,EAAE;AAC1D,gBAAA,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,MAAM;aACzD,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC5B,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAChD,IAAI,CAAC,aAAa,CACrB,CAAC;;AAGF,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;;gBAEhE,OAAO,IAAI,CAAC,sBAAsB,CAC9B,OAAO,EACP,WAAW,EACX,SAAS,CACZ,CAAC;AACL,aAAA;AAAM,iBAAA;;AAEH,gBAAA,MAAM,gBAAgB,GAAiB;AACnC,oBAAA,GAAG,OAAO;AACV,oBAAA,UAAU,EAAE,qBAAqB,CAC7B,OAAO,EACP,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAChC;iBACJ,CAAC;;AAEF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,CAClE,CAAC;gBACF,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CACnC,aAAa,EACb,WAAW,CACd,CAAC;gBACF,OAAO,IAAI,CAAC,sBAAsB,CAC9B,gBAAgB,EAChB,WAAW,EACX,SAAS,CACZ,CAAC;AACL,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,MAAM,CAAC,aAAsC,EAAA;QACzC,IAAI;AACA,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC1C,MAAM,kBAAkB,GACpB,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;AAChD,YAAA,MAAM,WAAW,GAAgB;AAC7B,gBAAA,SAAS,EAAE,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC;AAC3D,gBAAA,qBAAqB,EACjB,aAAa,EAAE,qBAAqB,IAAI,EAAE;AAC9C,gBAAA,iBAAiB,EAAE,aAAa,EAAE,iBAAiB,IAAI,MAAM;aAChE,CAAC;AACF,YAAA,MAAM,SAAS,GAAG,aAAa,IAAI,aAAa,CAAC,SAAS,CAAC;AAC3D,YAAA,MAAM,qBAAqB,GACvB,aAAa,IAAI,aAAa,CAAC,qBAAqB,CAAC;;AAGzD,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;;AAE/C,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CACxB,kBAAkB,EAClB,WAAW,EACX,SAAS,EACT,qBAAqB,CACxB,CAAC;AACL,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;gBAC9D,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CACnC,aAAa,EACb,WAAW,CACd,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC,gBAAgB,CACxB,kBAAkB,EAClB,WAAW,EACX,SAAS,EACT,qBAAqB,CACxB,CAAC;AACL,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;KACJ;AAED;;;;;;;AAOG;AACO,IAAA,MAAM,sBAAsB,CAClC,OAAqB,EACrB,WAAwB,EACxB,SAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAErD,QAAA,MAAM,YAAY,GAAG,MAAM,WAAW,CAClC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;AAElC;;;AAGG;QACH,IAAI,WAAW,CAAC,KAAK,EAAE;AACnB,YAAAA,UAAuB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACnD,SAAA;QAED,MAAM,gBAAgB,GAAG,qBAAqB,CAC1C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,oBAAoB,EACzB,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACF,QAAA,YAAY,CAAC,cAAc,GAAG,gBAAgB,CAAC;QAE/C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,GAAG,EAAE;YACpD,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;YACH,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACrE,SAAA;KACJ;AAED;;;;;;AAMG;AACH,IAAA,MAAM,eAAe,CACjB,OAAsC,EACtC,WAAwB,EACxB,SAAqB,EAAA;AAErB,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,iBAAiB,CAC1B,CAAC;QAEF,MAAM,IAAI,GACN,SAAS;AACT,aAAC,MAAM,WAAW,CACd,iBAAiB,EACjB,iBAAiB,CAAC,iBAAiB,EACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;AAE3D,QAAA,MAAM,YAAY,GAAG;AACjB,YAAA,GAAG,OAAO;YACV,aAAa,EAAE,IAAI,CAAC,SAAS;SAChC,CAAC;QAEF,IAAI;;AAEA,YAAA,MAAM,UAAU,GAA4B,MAAM,WAAW,CACzD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;gBACE,sBAAsB;gBACtB,gBAAgB,EAAE,YAAY,CAAC,SAAS;gBACxC,wBAAwB,EAAE,YAAY,CAAC,iBAAiB;gBACxD,2BAA2B,EAAE,YAAY,CAAC,oBAAoB;gBAC9D,OAAO,EAAE,YAAY,CAAC,OAAO;AAChC,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,YAAY,CAAC,UAAU,KAAK,UAAU,CAAC,IAAI,EAAE;AAC7C,gBAAA,OAAO,MAAM,IAAI,CAAC,uBAAuB,CACrC,YAAY,EACZ,WAAW,EACX,UAAU,EACV,IAAI,CAAC,QAAQ,CAChB,CAAC;AACL,aAAA;AAAM,iBAAA;;AAEH,gBAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjCC,qBAA+B,EAC/B,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,IAAI,CAAC,MAAM,EACX,UAAU,CAAC,SAAS,EACpB,YAAY,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;;gBAGF,MAAM,WAAW,GAAW,IAAI,CAAC,mBAAmB,CAChD,WAAW,EACX,WAAW,CACd,CAAC;AACF,gBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,YAAY,EACtB,eAAe,CAAC,KAAK,EACrB,EAAE,WAAW,EAAE,EACf,IAAI,CACP,CAAC;;AAGF,gBAAA,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CACjD,WAAW,EACX,WAAW,CAAC,iBAAiB,CAChC,CAAC;AAEF,gBAAA,MAAM,YAAY,GAAG,MAAM,CACvBC,mBAAmC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CACG,cAAc,EACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAC/C,IAAI,CAAC,MAAM,CACd,CAAC;AAEF,gBAAA,OAAO,MAAM,WAAW,CACpBC,kBAA4B,EAC5B,iBAAiB,CAAC,kBAAkB,EACpC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,OAAO,EACP,YAAY,EACZ,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,iBAAiB,EACvB,IAAI,CAAC,MAAM,EACX,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;AACL,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;YAE3B,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,MAAM,cAAc,CAChB,OAAsC,EACtC,WAAwB,EAAA;AAExB,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;;AAE5C,QAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;YACE,gBAAgB,EAAE,OAAO,CAAC,SAAS;YACnC,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;YACnD,2BAA2B,EAAE,OAAO,CAAC,oBAAoB;YACzD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,SAAA,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,WAAW,CAC5B,cAAc,EACd,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,EAAE,CAAC;AACJ,QAAA,MAAM,YAAY,GAAG;AACjB,YAAA,GAAG,OAAO;AACV,YAAA,MAAM,EAAE,MAAM;SACjB,CAAC;AACF,QAAA,MAAM,WAAW,GACb,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEpE,MAAM,IAAI,GAAG,MAAMC,UAAoB,CACnC,WAAW,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,YAAY,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;;AAGd,QAAA,MAAM,cAAc,GAAG,MAAM,WAAW,CACpC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,iCAAiC,EACnD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,WAAW,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC;AAE9C,QAAA,MAAM,YAAY,GAAG,MAAM,CACvBF,mBAAmC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CACG,cAAc,EACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAC/C,IAAI,CAAC,MAAM,CACd,CAAC;QAEF,OAAO,WAAW,CACdG,iBAA2B,EAC3B,iBAAiB,CAAC,iBAAiB,EACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,YAAY,EACZ,YAAY,EACZ,KAAK,CAAC,iBAAiB,EACvB,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;KACL;IAED,MAAM,uBAAuB,CACzB,OAAsC,EACtC,WAAwB,EACxB,UAAmC,EACnC,YAAoB,EAAA;AAEpB,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;;AAE5C,QAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;YACE,gBAAgB,EAAE,OAAO,CAAC,SAAS;YACnC,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;YACnD,2BAA2B,EAAE,OAAO,CAAC,oBAAoB;YACzD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,WAAW,GACb,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEpE,MAAM,IAAI,GAAG,MAAMC,WAAqB,CACpC,WAAW,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,OAAO,EACP,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI,CAAC,MAAM,EAAE,CAAC;;AAGd,QAAA,MAAM,cAAc,GAAG,MAAM,WAAW,CACpC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,iCAAiC,EACnD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,WAAW,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC;AAE9C,QAAA,MAAM,YAAY,GAAG,MAAM,CACvBJ,mBAAmC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CACG,cAAc,EACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAC/C,IAAI,CAAC,MAAM,CACd,CAAC;AAEF,QAAA,OAAO,WAAW,CACdC,kBAA4B,EAC5B,iBAAiB,CAAC,kBAAkB,EACpC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,KAAK,CAAC,iBAAiB,EACvB,IAAI,CAAC,MAAM,EACX,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;KACL;AAED;;;;;;;;AAQG;IACO,MAAM,gBAAgB,CAC5B,YAAqC,EACrC,WAAwB,EACxB,gBAAyB,EACzB,qBAA8B,EAAA;AAE9B,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,YAAY,EACtB,eAAe,CAAC,KAAK,EACrB,YAAY,CACf,CAAC;QAEF,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,WAAW,CACpB,CAAC;QAEF,IAAI;;AAEA,YAAA,MAAM,IAAI,CAAC,kBAAkB,CACzB,IAAI,CAAC,aAAa,EAClB,YAAY,CAAC,OAAO,CACvB,CAAC;;AAGF,YAAA,MAAM,UAAU,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;gBACE,sBAAsB;AACtB,gBAAA,gBAAgB,EAAE,gBAAgB;AAClC,gBAAA,OAAO,EAAE,YAAY,CAAC,OAAO,IAAI,SAAS;AAC7C,aAAA,CAAC,CAAC;YAEH,IAAI;AACA,gBAAA,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC;AAC3C,aAAA;YAAC,MAAM;AACJ,gBAAA,IACI,YAAY,CAAC,OAAO,EAAE,aAAa;AACnC,oBAAA,YAAY,CAAC,qBAAqB;oBAClC,UAAU,CAAC,SAAS,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EACzD;AACE,oBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAC7B,YAAY,CAAC,OAAO,EAAE,aAAa,EACnC,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,oBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,KAAK,EACrB,YAAY,CACf,CAAC;AAEF,oBAAA,IAAI,qBAAqB,EAAE;AACvB,wBAAA,MAAM,iBAAiB,GAAsB;4BACzC,KAAK,EAAE,KAAK,CAAC,WAAW;AACxB,4BAAA,OAAO,EACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AAChD,4BAAA,SAAS,EAAE,KAAK;yBACnB,CAAC;AACF,wBAAA,MAAM,WAAW,GAAG,SAAS,CAAC,cAAc,CACxC,qBAAqB,EACrBI,aAA0B,EAAE,CAC/B,CAAC;wBACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,WAAW,EACX,iBAAiB,CACpB,CAAC;AACL,qBAAA;AAED,oBAAA,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;oBAE3B,OAAO;AACV,iBAAA;AACJ,aAAA;;YAGD,MAAM,SAAS,GAAW,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAEhE,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,KAAK,EACrB,YAAY,CACf,CAAC;;YAGF,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC3D,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,YAAY,EACtB,eAAe,CAAC,KAAK,EACrB,EAAE,WAAW,EAAE,EACf,IAAI,CACP,CAAC;AAEF,YAAA,MAAM,IAAI,CAAC,mBAAmB,CAC1B,WAAW,EACX,WAAW,CAAC,iBAAiB,CAChC,CAAC,KAAK,CAAC,MAAK;;AAEb,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,qBAAqB,EAAE;AACvB,gBAAA,MAAM,iBAAiB,GAAsB;oBACzC,KAAK,EAAE,KAAK,CAAC,WAAW;AACxB,oBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,oBAAA,SAAS,EAAE,KAAK;iBACnB,CAAC;AACF,gBAAA,MAAM,WAAW,GAAG,SAAS,CAAC,cAAc,CACxC,qBAAqB,EACrBA,aAA0B,EAAE,CAC/B,CAAC;AAEF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yDAAyD,CAC5D,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CAA+B,4BAAA,EAAA,WAAW,CAAE,CAAA,CAC/C,CAAC;gBACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,WAAW,EACX,iBAAiB,CACpB,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;AAC9D,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;YAE3B,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,KAAK,EACrB,IAAI,EACJ,CAAe,CAClB,CAAC;AACF,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,UAAU,EACpB,eAAe,CAAC,KAAK,CACxB,CAAC;AACF,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,UAAU,EACpB,eAAe,CAAC,KAAK,CACxB,CAAC;KACL;AAED;;;AAGG;IACH,mBAAmB,CAAC,UAAkB,EAAE,MAAmB,EAAA;;AAEvD,QAAA,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAgB,aAAA,EAAA,UAAU,CAAE,CAAA,CAAC,CAAC;;YAElD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAC7C,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC3C,YAAA,MAAM,sBAAsB,CACxBC,gBAAsC,CACzC,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;IACH,mBAAmB,CACf,WAAmB,EACnB,iBAAyB,EAAA;QAEzB,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;AAC3C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;AAEF,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,MAAK;;gBAEhC,IAAI,WAAW,CAAC,MAAM,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,kDAAkD,CACrD,CAAC;oBACF,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC1B,MAAM,CACF,sBAAsB,CAClBC,aAAmC,CACtC,CACJ,CAAC;oBACF,OAAO;AACV,iBAAA;gBAED,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI;AACA;;;;AAIG;AACH,oBAAA,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;AACpC,iBAAA;gBAAC,OAAO,CAAC,EAAE,GAAE;;AAGd,gBAAA,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,aAAa,EAAE;oBACjC,OAAO;AACV,iBAAA;gBACD,aAAa,CAAC,UAAU,CAAC,CAAC;gBAE1B,IAAI,cAAc,GAAG,EAAE,CAAC;gBACxB,MAAM,YAAY,GACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;AACpD,gBAAA,IAAI,WAAW,EAAE;AACb,oBAAA,IAAI,YAAY,KAAK,kBAAkB,CAAC,KAAK,EAAE;AAC3C,wBAAA,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;AAChD,qBAAA;AAAM,yBAAA;AACH,wBAAA,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC9C,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,6EAA6E,CAChF,CAAC;gBAEF,OAAO,CAAC,cAAc,CAAC,CAAC;aAC3B,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;AACpD,SAAC,CAAC,CAAC,OAAO,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACpD,SAAC,CAAC,CAAC;KACN;AAED;;;;;;;;;;;;AAYG;IACH,SAAS,CAAC,WAAmB,EAAE,WAAwB,EAAA;QACnD,IAAI;AACA,YAAA,IAAI,WAAW,CAAC;;YAEhB,IAAI,WAAW,CAAC,KAAK,EAAE;AACnB,gBAAA,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CAA+B,4BAAA,EAAA,WAAW,CAAE,CAAA,CAC/C,CAAC;AACF,gBAAA,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC5C,aAAA;AAAM,iBAAA,IAAI,OAAO,WAAW,CAAC,KAAK,KAAK,WAAW,EAAE;;gBAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAClB,CAA4B,yBAAA,EAAA,WAAW,CAAE,CAAA,CAC5C,CAAC;gBACF,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC/D,aAAA;;YAGD,IAAI,CAAC,WAAW,EAAE;AACd,gBAAA,MAAM,sBAAsB,CACxBC,gBAAsC,CACzC,CAAC;AACL,aAAA;YACD,IAAI,WAAW,CAAC,KAAK,EAAE;gBACnB,WAAW,CAAC,KAAK,EAAE,CAAC;AACvB,aAAA;AACD,YAAA,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;YACjC,WAAW,CAAC,iBAAiB,CAAC,gBAAgB,CAC1C,cAAc,EACd,IAAI,CAAC,YAAY,CACpB,CAAC;AAEF,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,sBAAsB,GAAI,CAAe,CAAC,OAAO,CACpD,CAAC;AACF,YAAA,MAAM,sBAAsB,CACxBC,gBAAsC,CACzC,CAAC;AACL,SAAA;KACJ;AAED;;;;;;AAMG;IACH,cAAc,CACV,WAAmB,EACnB,EAAE,SAAS,EAAE,qBAAqB,EAAE,iBAAiB,EAAe,EAAA;AAEpE;;;AAGG;AACH,QAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,UAAU;cACtC,iBAAiB,CAAC,UAAU;AAC9B,cAAE,iBAAiB,CAAC,OAAO,CAAC;AAChC,QAAA,MAAM,MAAM,GAAG,iBAAiB,CAAC,SAAS;cACpC,iBAAiB,CAAC,SAAS;AAC7B,cAAE,iBAAiB,CAAC,OAAO,CAAC;AAChC;;;AAGG;AACH,QAAA,MAAM,QAAQ,GACV,iBAAiB,CAAC,UAAU;YAC5B,QAAQ,CAAC,eAAe,CAAC,WAAW;AACpC,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;AAC9B,QAAA,MAAM,SAAS,GACX,iBAAiB,CAAC,WAAW;YAC7B,QAAQ,CAAC,eAAe,CAAC,YAAY;AACrC,YAAA,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;AAE/B,QAAA,IAAI,KAAK,GAAG,qBAAqB,CAAC,SAAS,EAAE,KAAK,CAAC;AACnD,QAAA,IAAI,MAAM,GAAG,qBAAqB,CAAC,SAAS,EAAE,MAAM,CAAC;AACrD,QAAA,IAAI,GAAG,GAAG,qBAAqB,CAAC,aAAa,EAAE,GAAG,CAAC;AACnD,QAAA,IAAI,IAAI,GAAG,qBAAqB,CAAC,aAAa,EAAE,IAAI,CAAC;QAErD,IAAI,CAAC,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,QAAQ,EAAE;AACzC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0EAA0E,CAC7E,CAAC;AACF,YAAA,KAAK,GAAG,gBAAgB,CAAC,WAAW,CAAC;AACxC,SAAA;QAED,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,SAAS,EAAE;AAC7C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4EAA4E,CAC/E,CAAC;AACF,YAAA,MAAM,GAAG,gBAAgB,CAAC,YAAY,CAAC;AAC1C,SAAA;QAED,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,SAAS,EAAE;AACpC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+EAA+E,CAClF,CAAC;AACF,YAAA,GAAG,GAAG,IAAI,CAAC,GAAG,CACV,CAAC,EACD,SAAS,GAAG,CAAC,GAAG,gBAAgB,CAAC,YAAY,GAAG,CAAC,GAAG,MAAM,CAC7D,CAAC;AACL,SAAA;QAED,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iFAAiF,CACpF,CAAC;AACF,YAAA,IAAI,GAAG,IAAI,CAAC,GAAG,CACX,CAAC,EACD,QAAQ,GAAG,CAAC,GAAG,gBAAgB,CAAC,WAAW,GAAG,CAAC,GAAG,OAAO,CAC5D,CAAC;AACL,SAAA;AAED,QAAA,OAAO,iBAAiB,CAAC,IAAI,CACzB,WAAW,EACX,SAAS,EACT,CAAA,MAAA,EAAS,KAAK,CAAA,SAAA,EAAY,MAAM,CAAS,MAAA,EAAA,GAAG,UAAU,IAAI,CAAA,gBAAA,CAAkB,CAC/E,CAAC;KACL;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,CAAQ,EAAA;QACjB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC9B,SAAA;;QAED,CAAC,CAAC,cAAc,EAAE,CAAC;KACtB;AAED;;;AAGG;IACH,UAAU,CAAC,WAAmB,EAAE,iBAAyB,EAAA;;QAErD,WAAW,CAAC,KAAK,EAAE,CAAC;;QAGpB,iBAAiB,CAAC,mBAAmB,CACjC,cAAc,EACd,IAAI,CAAC,YAAY,CACpB,CAAC;KACL;AAED;;;;AAIG;IACH,iBAAiB,CAAC,MAAqB,EAAE,SAAiB,EAAA;QACtD,OAAO,CAAA,EAAG,gBAAgB,CAAC,iBAAiB,CAAA,CAAA,EACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QACrB,CAAA,CAAA,EAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,IAAI,CAAC,aAAa,CAAA,CAAE,CAAC;KAC7D;AAED;;;;AAIG;AACH,IAAA,uBAAuB,CAAC,OAAgC,EAAA;QACpD,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;AACvE,QAAA,OAAO,GAAG,gBAAgB,CAAC,iBAAiB,CAAI,CAAA,EAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAA,CAAA,EAAI,IAAI,CAAC,aAAa,EAAE,CAAC;KACtH;AACJ;;;;"}