{"version": 3, "file": "tracing.js", "sourceRoot": "", "sources": ["../../src/tracing.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,MAAM,CAAC,MAAM,aAAa,GAAG,mBAAmB,CAAC;IAC/C,SAAS,EAAE,oBAAoB;IAC/B,WAAW,EAAE,sBAAsB;IACnC,cAAc,EAAE,WAAW;CAC5B,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createTracingClient } from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"./constants.js\";\n\nexport const tracingClient = createTracingClient({\n  namespace: \"Microsoft.KeyVault\",\n  packageName: \"@azure/keyvault-keys\",\n  packageVersion: SDK_VERSION,\n});\n"]}