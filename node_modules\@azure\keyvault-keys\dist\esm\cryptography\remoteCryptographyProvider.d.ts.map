{"version": 3, "file": "remoteCryptographyProvider.d.ts", "sourceRoot": "", "sources": ["../../../src/cryptography/remoteCryptographyProvider.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAExD,OAAO,KAAK,EACV,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,UAAU,EACX,MAAM,gCAAgC,CAAC;AAExC,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAInE,OAAO,KAAK,EAAE,yBAAyB,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAI9F,OAAO,KAAK,EAAE,oBAAoB,EAAE,6BAA6B,EAAE,MAAM,aAAa,CAAC;AAMvF;;;GAGG;AACH,qBAAa,0BAA2B,YAAW,oBAAoB;gBAEnE,GAAG,EAAE,MAAM,GAAG,WAAW,EACzB,UAAU,EAAE,eAAe,EAC3B,eAAe,GAAE,yBAA8B;IAkCjD,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,6BAA6B,GAAG,OAAO;IAInF,OAAO,CACL,iBAAiB,EAAE,iBAAiB,EACpC,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,aAAa,CAAC;IAmCzB,OAAO,CACL,iBAAiB,EAAE,iBAAiB,EACpC,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,aAAa,CAAC;IAmCzB,OAAO,CACL,SAAS,EAAE,gBAAgB,EAC3B,SAAS,EAAE,UAAU,EACrB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,UAAU,CAAC;IAwBtB,SAAS,CACP,SAAS,EAAE,gBAAgB,EAC3B,YAAY,EAAE,UAAU,EACxB,OAAO,GAAE,gBAAqB,GAC7B,OAAO,CAAC,YAAY,CAAC;IAwBxB,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,GAAE,WAAgB,GAAG,OAAO,CAAC,UAAU,CAAC;IAoB3F,UAAU,CACR,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,UAAU,EAChB,SAAS,EAAE,UAAU,EACrB,OAAO,GAAE,aAAkB,GAC1B,OAAO,CAAC,YAAY,CAAC;IAWxB,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,UAAU,EAClB,SAAS,EAAE,UAAU,EACrB,OAAO,GAAE,aAAkB,GAC1B,OAAO,CAAC,YAAY,CAAC;IAuBxB,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,GAAE,WAAgB,GAAG,OAAO,CAAC,UAAU,CAAC;IAoB7F;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,IAAI,KAAK,IAAI,MAAM,GAAG,SAAS,CAE9B;IAED;;;;OAIG;IACH,MAAM,CAAC,OAAO,GAAE,aAAkB,GAAG,OAAO,CAAC,WAAW,CAAC;IAqBzD;;OAEG;IACH,OAAO,CAAC,MAAM,CAAiB;IAE/B;;;;OAIG;IACH,OAAO,CAAC,GAAG,CAAuB;IAElC;;OAEG;IACH,OAAO,CAAC,IAAI,CAAS;IAErB;;OAEG;IACH,OAAO,CAAC,OAAO,CAAS;IAExB;;OAEG;IACH,OAAO,CAAC,QAAQ;CAUjB"}