{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../../src/lro/delete/operation.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAMlC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,wBAAwB,EAAE,MAAM,yBAAyB,CAAC;AAOnE,MAAM,OAAO,sBAAuB,SAAQ,wBAG3C;IACC,YACS,KAAkC,EACjC,MAAsB,EACtB,mBAAqC,EAAE;QAE/C,KAAK,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,mDAAmD,EAAE,CAAC,CAAC;QAJ9E,UAAK,GAAL,KAAK,CAA6B;QACjC,WAAM,GAAN,MAAM,CAAgB;QACtB,qBAAgB,GAAhB,gBAAgB,CAAuB;IAGjD,CAAC;IAED;;;OAGG;IACK,SAAS,CAAC,IAAY,EAAE,UAA4B,EAAE;QAC5D,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACnE,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,aAAa,CAAC,IAAY,EAAE,UAAgC,EAAE;QACpE,OAAO,aAAa,CAAC,QAAQ,CAC3B,+BAA+B,EAC/B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACvE,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM,CACjB,UAGI,EAAE;QAEN,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QAEvB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;YACvB,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;YAC1B,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBACtC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACrE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC7B,6EAA6E;oBAC7E,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC3B,CAAC;qBAAM,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACpC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;oBACpB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;oBACzB,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { OperationOptions } from \"@azure-rest/core-client\";\nimport type { KeyVaultClient } from \"../../generated/keyVaultClient.js\";\nimport type { DeleteKeyOptions, DeletedKey, GetDeletedKeyOptions } from \"../../keysModels.js\";\nimport { tracingClient } from \"../../tracing.js\";\nimport { getKeyFromKeyBundle } from \"../../transformations.js\";\nimport type { KeyVaultKeyPollOperationState } from \"../keyVaultKeyPoller.js\";\nimport { KeyVaultKeyPollOperation } from \"../keyVaultKeyPoller.js\";\n\n/**\n * An interface representing the state of a delete key's poll operation\n */\nexport interface DeleteKeyPollOperationState extends KeyVaultKeyPollOperationState<DeletedKey> {}\n\nexport class DeleteKeyPollOperation extends KeyVaultKeyPollOperation<\n  DeleteKeyPollOperationState,\n  DeletedKey\n> {\n  constructor(\n    public state: DeleteKeyPollOperationState,\n    private client: KeyVaultClient,\n    private operationOptions: OperationOptions = {},\n  ) {\n    super(state, { cancelMessage: \"Canceling the deletion of a key is not supported.\" });\n  }\n\n  /**\n   * Sends a delete request for the given Key Vault Key's name to the Key Vault service.\n   * Since the Key Vault Key won't be immediately deleted, we have {@link beginDeleteKey}.\n   */\n  private deleteKey(name: string, options: DeleteKeyOptions = {}): Promise<DeletedKey> {\n    return tracingClient.withSpan(\"DeleteKeyPoller.deleteKey\", options, async (updatedOptions) => {\n      const response = await this.client.deleteKey(name, updatedOptions);\n      return getKeyFromKeyBundle(response);\n    });\n  }\n\n  /**\n   * The getDeletedKey method returns the specified deleted key along with its properties.\n   * This operation requires the keys/get permission.\n   */\n  private getDeletedKey(name: string, options: GetDeletedKeyOptions = {}): Promise<DeletedKey> {\n    return tracingClient.withSpan(\n      \"DeleteKeyPoller.getDeletedKey\",\n      options,\n      async (updatedOptions) => {\n        const response = await this.client.getDeletedKey(name, updatedOptions);\n        return getKeyFromKeyBundle(response);\n      },\n    );\n  }\n\n  /**\n   * Reaches to the service and updates the delete key's poll operation.\n   */\n  public async update(\n    options: {\n      abortSignal?: AbortSignalLike;\n      fireProgress?: (state: DeleteKeyPollOperationState) => void;\n    } = {},\n  ): Promise<DeleteKeyPollOperation> {\n    const state = this.state;\n    const { name } = state;\n\n    if (options.abortSignal) {\n      this.operationOptions.abortSignal = options.abortSignal;\n    }\n\n    if (!state.isStarted) {\n      const deletedKey = await this.deleteKey(name, this.operationOptions);\n      state.isStarted = true;\n      state.result = deletedKey;\n      if (!deletedKey.properties.recoveryId) {\n        state.isCompleted = true;\n      }\n    }\n\n    if (!state.isCompleted) {\n      try {\n        state.result = await this.getDeletedKey(name, this.operationOptions);\n        state.isCompleted = true;\n      } catch (error: any) {\n        if (error.statusCode === 403) {\n          // At this point, the resource exists but the user doesn't have access to it.\n          state.isCompleted = true;\n        } else if (error.statusCode !== 404) {\n          state.error = error;\n          state.isCompleted = true;\n          throw error;\n        }\n      }\n    }\n\n    return this;\n  }\n}\n"]}