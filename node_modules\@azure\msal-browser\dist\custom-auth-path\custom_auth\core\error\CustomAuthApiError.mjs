/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
import { CustomAuthError } from './CustomAuthError.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Error when no required authentication method by Microsoft Entra is supported
 */
class RedirectError extends CustomAuthError {
    constructor(correlationId) {
        super("redirect", "No required authentication method by Microsoft Entra is supported, a fallback to the web-based authentication flow is needed.", correlationId);
        Object.setPrototypeOf(this, RedirectError.prototype);
    }
}
/**
 * Custom Auth API error.
 */
class CustomAuthApiError extends CustomAuthError {
    constructor(error, errorDescription, correlationId, errorCodes, subError, attributes, continuationToken, traceId, timestamp) {
        super(error, errorDescription, correlationId, errorCodes, subError);
        this.attributes = attributes;
        this.continuationToken = continuationToken;
        this.traceId = traceId;
        this.timestamp = timestamp;
        Object.setPrototypeOf(this, CustomAuthApiError.prototype);
    }
}

export { CustomAuthApiError, RedirectError };
//# sourceMappingURL=CustomAuthApiError.mjs.map
