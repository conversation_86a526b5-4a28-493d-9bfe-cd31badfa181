{"version": 3, "file": "cryptographyClient.js", "sourceRoot": "", "sources": ["../../src/cryptographyClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAWlC,OAAO,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAuBrD,OAAO,EAAE,0BAA0B,EAAE,MAAM,8CAA8C,CAAC;AAC1F,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAEvD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2CAA2C,CAAC;AACpF,OAAO,EAAE,uBAAuB,EAAE,MAAM,2CAA2C,CAAC;AACpF,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAoE7B;;;;OAIG;IACH,YACE,GAAsC,EACtC,UAA4B,EAC5B,kBAA6C,EAAE;QAE/C,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,uCAAuC;YACvC,IAAI,CAAC,GAAG,GAAG;gBACT,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,GAAG;aACX,CAAC;YACF,IAAI,CAAC,cAAc,GAAG,IAAI,0BAA0B,CAAC,GAAG,EAAE,UAAW,EAAE,eAAe,CAAC,CAAC;QAC1F,CAAC;aAAM,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;YACzB,4CAA4C;YAC5C,IAAI,CAAC,GAAG,GAAG;gBACT,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,GAAG;aACX,CAAC;YACF,IAAI,CAAC,cAAc,GAAG,IAAI,0BAA0B,CAAC,GAAG,EAAE,UAAW,EAAE,eAAe,CAAC,CAAC;QAC1F,CAAC;aAAM,CAAC;YACN,wCAAwC;YACxC,IAAI,CAAC,GAAG,GAAG;gBACT,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,GAAG;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;;QACV,OAAO,CAAA,MAAA,IAAI,CAAC,cAAc,0CAAE,QAAQ,KAAI,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAsB,EAAE,CAAC;YAC/E,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QACxB,CAAC;aAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,CAAC;IACH,CAAC;IAoEM,OAAO,CACZ,GAAG,IAEmD;QAEtD,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QACtE,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAClF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACzF,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;gBACjE,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,UAA6B;QAChD,uFAAuF;QACvF,MAAM,qBAAqB,GAAgC;YACzD,SAAS;YACT,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,SAAS;YACT,YAAY;SACb,CAAC;QAEF,IAAI,UAAU,CAAC,SAAS,IAAI,qBAAqB,EAAE,CAAC;YAClD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,UAAqC,CAAC;gBACxD,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;oBAClB,SAAS,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CACb,yCAAyC,UAAU,CAAC,SAAS,yDAAyD,CAAC,CAAC,OAAO,EAAE,CAClI,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,4BAA4B,CAClC,IAAkF;QAElF,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YAChC,4CAA4C;YAC5C,OAAO;gBACL;oBACE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;oBAClB,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;iBACE;gBACtB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;aACd,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,sEAAsE;YACtE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAmB,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAqFM,OAAO,CACZ,GAAG,IAEmD;QAEtD,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QAEtE,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAClF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACzF,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;gBACjE,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,4BAA4B,CAClC,IAAkF;QAElF,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YAChC,qDAAqD;YACrD,OAAO;gBACL;oBACE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;oBAClB,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;iBACC;gBACtB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;aACd,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,gFAAgF;YAChF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAmB,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,OAAO,CACZ,SAA2B,EAC3B,GAAe,EACf,UAA0B,EAAE;QAE5B,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAClF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAC9E,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;gBAC9D,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACI,SAAS,CACd,SAA2B,EAC3B,YAAwB,EACxB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACpF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAChF,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;YACrE,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;gBACzE,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACI,IAAI,CACT,SAA6B,EAC7B,MAAkB,EAClB,UAAuB,EAAE;QAEzB,OAAO,aAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACzF,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC/E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAC3E,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;gBACrE,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACI,MAAM,CACX,SAA6B,EAC7B,MAAkB,EAClB,SAAqB,EACrB,UAAyB,EAAE;QAE3B,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAC7E,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACvE,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBAClF,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,QAAQ,CACb,SAA6B,EAC7B,IAAgB;IAChB,8DAA8D;IAC9D,UAAuB,EAAE;QAEzB,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC/E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAC/E,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAChE,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACI,UAAU,CACf,SAA6B,EAC7B,IAAgB,EAChB,SAAqB;IACrB,8DAA8D;IAC9D,UAAyB,EAAE;QAE3B,OAAO,aAAa,CAAC,QAAQ,CAC3B,+BAA+B,EAC/B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACjF,IAAI,CAAC;gBACH,OAAO,QAAQ,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;gBACpF,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,cAAc,CAAC,OAAsB;QACjD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEzC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;YACjB,KAAK,YAAY;gBACf,OAAO,GAAG,CAAC,KAAK,CAAC;YACnB,KAAK,aAAa;gBAChB,OAAO,GAAG,CAAC,KAAK,CAAC,GAAI,CAAC;YACxB;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,QAAQ,CAA6B,OAAU;QAC3D,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACnC,0DAA0D;YAC1D,IAAI,GAA4B,CAAC;YACjC,IAAI,CAAC;gBACH,GAAG,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,CAAU,EAAE,CAAC;gBACpB,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC3C,4FAA4F;oBAC5F,sGAAsG;oBACtG,MAAM,CAAC,OAAO,CACZ,gCAAgC,IAAI,CAAC,GAAG,CAAC,KAAK,qCAAqC,CACpF,CAAC;oBACF,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACrE,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,CAAC;gBACV,CAAC;YACH,CAAC;YAED,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAGD;;;;;;OAMG;IACK,KAAK,CAAC,WAAW,CACvB,SAAwC,EACxC,SAAiB,EACjB,OAAU;QAEV,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YAEpB,uCAAuC;YACvC,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CACjB,IAAI,uBAAuB,CAAC,WAAW,CAAC,EACxC,IAAI,uBAAuB,CAAC,WAAW,CAAC,CACzC,CAAC;YACJ,CAAC;YAED,2FAA2F;YAC3F,qGAAqG;YACrG,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;QAEpF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CACb,iCAAiC,SAAS,sBAAsB,SAAS,KACvE,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAChE,EAAE,CACH,CAAC;QACJ,CAAC;QAED,uDAAuD;QACvD,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IAEO,WAAW,CAAC,GAA0B,EAAE,SAAwB;;QACtE,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;YACvC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC;YACtD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvB,gCAAgC;YAChC,IAAI,SAAS,IAAI,GAAG,GAAG,SAAS,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,yBAAyB,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,SAAS,IAAI,GAAG,GAAG,SAAS,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,eAAe,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAC/E,CAAC;YAED,uBAAuB;YACvB,IAAI,SAAS,IAAI,MAAM,IAAI,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,CAAC,SAAS,CAAC,CAAA,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CAAC,aAAa,SAAS,4BAA4B,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;aAAM,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACrC,kCAAkC;YAClC,IAAI,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAA,MAAA,GAAG,CAAC,KAAK,CAAC,MAAM,0CAAE,QAAQ,CAAC,SAAS,CAAC,CAAA,EAAE,CAAC;gBAC5E,MAAM,IAAI,KAAK,CAAC,aAAa,SAAS,4BAA4B,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions } from \"@azure-rest/core-client\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport type {\n  CryptographyClientOptions,\n  GetKeyOptions,\n  JsonWebKey,\n  KeyOperation,\n  KeyVaultKey,\n} from \"./keysModels.js\";\nimport { KnownKeyOperations } from \"./keysModels.js\";\nimport type {\n  AesCbcEncryptParameters,\n  AesCbcEncryptionAlgorithm,\n  CryptographyClientKey,\n  DecryptOptions,\n  DecryptParameters,\n  DecryptResult,\n  EncryptOptions,\n  EncryptParameters,\n  EncryptResult,\n  EncryptionAlgorithm,\n  KeyWrapAlgorithm,\n  SignOptions,\n  SignResult,\n  SignatureAlgorithm,\n  UnwrapKeyOptions,\n  UnwrapResult,\n  VerifyOptions,\n  VerifyResult,\n  WrapKeyOptions,\n  WrapResult,\n} from \"./cryptographyClientModels.js\";\nimport { RemoteCryptographyProvider } from \"./cryptography/remoteCryptographyProvider.js\";\nimport { randomBytes } from \"./cryptography/crypto.js\";\nimport type { CryptographyProvider, CryptographyProviderOperation } from \"./cryptography/models.js\";\nimport { RsaCryptographyProvider } from \"./cryptography/rsaCryptographyProvider.js\";\nimport { AesCryptographyProvider } from \"./cryptography/aesCryptographyProvider.js\";\nimport { tracingClient } from \"./tracing.js\";\nimport { isRestError } from \"@azure/core-rest-pipeline\";\nimport { logger } from \"./log.js\";\n\n/**\n * A client used to perform cryptographic operations on an Azure Key vault key\n * or a local {@link JsonWebKey}.\n */\nexport class CryptographyClient {\n  /**\n   * The key the CryptographyClient currently holds.\n   */\n  private key: CryptographyClientKey;\n\n  /**\n   * The remote provider, which would be undefined if used in local mode.\n   */\n  private remoteProvider?: RemoteCryptographyProvider;\n\n  /**\n   * Constructs a new instance of the Cryptography client for the given key\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateCryptographyClient\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * // Create or retrieve a key from the keyvault\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   *\n   * // Lastly, create our cryptography client and connect to the service\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   * ```\n   * @param key - The key to use during cryptography tasks. You can also pass the identifier of the key i.e its url here.\n   * @param credential - An object that implements the `TokenCredential` interface used to authenticate requests to the service. Use the \\@azure/identity package to create a credential that suits your needs.\n   * @param pipelineOptions - Pipeline options used to configure Key Vault API requests.\n   *                          Omit this parameter to use the default pipeline configuration.\n   */\n  constructor(\n    key: string | KeyVaultKey,\n    credential: TokenCredential,\n    pipelineOptions?: CryptographyClientOptions,\n  );\n  /**\n   * Constructs a new instance of the Cryptography client for the given key in local mode.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleCreateCryptographyClientLocal\n   * import { CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const jsonWebKey = {\n   *   kty: \"RSA\",\n   *   kid: \"test-key-123\",\n   *   use: \"sig\",\n   *   alg: \"RS256\",\n   *   n: new Uint8Array([112, 34, 56, 98, 123, 244, 200, 99]),\n   *   e: new Uint8Array([1, 0, 1]),\n   *   d: new Uint8Array([45, 67, 89, 23, 144, 200, 76, 233]),\n   *   p: new Uint8Array([34, 89, 100, 77, 204, 56, 29, 77]),\n   *   q: new Uint8Array([78, 99, 201, 45, 188, 34, 67, 90]),\n   *   dp: new Uint8Array([23, 45, 78, 56, 200, 144, 32, 67]),\n   *   dq: new Uint8Array([12, 67, 89, 144, 99, 56, 23, 45]),\n   *   qi: new Uint8Array([78, 90, 45, 201, 34, 67, 120, 55]),\n   * };\n   * const client = new CryptographyClient(jsonWebKey);\n   * ```\n   * @param key - The JsonWebKey to use during cryptography operations.\n   */\n  constructor(key: JsonWebKey);\n  /**\n   * Internal constructor implementation for either local or Key Vault backed keys.\n   * @param key - The key to use during cryptography tasks.\n   * @param credential - Teh credential to use when constructing a Key Vault Cryptography client.\n   */\n  constructor(\n    key: string | KeyVaultKey | JsonWebKey,\n    credential?: TokenCredential,\n    pipelineOptions: CryptographyClientOptions = {},\n  ) {\n    if (typeof key === \"string\") {\n      // Key URL for remote-local operations.\n      this.key = {\n        kind: \"identifier\",\n        value: key,\n      };\n      this.remoteProvider = new RemoteCryptographyProvider(key, credential!, pipelineOptions);\n    } else if (\"name\" in key) {\n      // KeyVault key for remote-local operations.\n      this.key = {\n        kind: \"KeyVaultKey\",\n        value: key,\n      };\n      this.remoteProvider = new RemoteCryptographyProvider(key, credential!, pipelineOptions);\n    } else {\n      // JsonWebKey for local-only operations.\n      this.key = {\n        kind: \"JsonWebKey\",\n        value: key,\n      };\n    }\n  }\n\n  /**\n   * The base URL to the vault. If a local {@link JsonWebKey} is used vaultUrl will be empty.\n   */\n  get vaultUrl(): string {\n    return this.remoteProvider?.vaultUrl || \"\";\n  }\n\n  /**\n   * The ID of the key used to perform cryptographic operations for the client.\n   */\n  get keyID(): string | undefined {\n    if (this.key.kind === \"identifier\" || this.key.kind === \"remoteOnlyIdentifier\") {\n      return this.key.value;\n    } else if (this.key.kind === \"KeyVaultKey\") {\n      return this.key.value.id;\n    } else {\n      return this.key.value.kid;\n    }\n  }\n\n  /**\n   * Encrypts the given plaintext with the specified encryption parameters.\n   * Depending on the algorithm set in the encryption parameters, the set of possible encryption parameters will change.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleEncrypt\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey.id, credential);\n   *\n   * const encryptResult = await cryptographyClient.encrypt({\n   *   algorithm: \"RSA1_5\",\n   *   plaintext: Buffer.from(\"My Message\"),\n   * });\n   * console.log(\"encrypt result: \", encryptResult.result);\n   * ```\n   * @param encryptParameters - The encryption parameters, keyed on the encryption algorithm chosen.\n   * @param options - Additional options.\n   */\n  public encrypt(\n    encryptParameters: EncryptParameters,\n    options?: EncryptOptions,\n  ): Promise<EncryptResult>;\n  /**\n   * Encrypts the given plaintext with the specified cryptography algorithm\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleEncrypt\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey.id, credential);\n   *\n   * const encryptResult = await cryptographyClient.encrypt({\n   *   algorithm: \"RSA1_5\",\n   *   plaintext: Buffer.from(\"My Message\"),\n   * });\n   * console.log(\"encrypt result: \", encryptResult.result);\n   * ```\n   * @param algorithm - The algorithm to use.\n   * @param plaintext - The text to encrypt.\n   * @param options - Additional options.\n   * @deprecated Use `encrypt({ algorithm, plaintext }, options)` instead.\n   */\n  public encrypt(\n    algorithm: EncryptionAlgorithm,\n    plaintext: Uint8Array,\n    options?: EncryptOptions,\n  ): Promise<EncryptResult>;\n  public encrypt(\n    ...args:\n      | [EncryptParameters, EncryptOptions?]\n      | [EncryptionAlgorithm, Uint8Array, EncryptOptions?]\n  ): Promise<EncryptResult> {\n    const [parameters, options] = this.disambiguateEncryptArguments(args);\n    return tracingClient.withSpan(\"CryptographyClient.encrypt\", options, async (updatedOptions) => {\n      this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.Encrypt);\n      this.initializeIV(parameters);\n      const provider = await this.getProvider(\"encrypt\", parameters.algorithm, updatedOptions);\n      try {\n        return provider.encrypt(parameters, updatedOptions);\n      } catch (error: any) {\n        if (this.remoteProvider) {\n          return this.remoteProvider.encrypt(parameters, updatedOptions);\n        }\n        throw error;\n      }\n    });\n  }\n\n  private initializeIV(parameters: EncryptParameters): void {\n    // For AES-GCM the service **must** generate the IV, so we only populate it for AES-CBC\n    const algorithmsRequiringIV: AesCbcEncryptionAlgorithm[] = [\n      \"A128CBC\",\n      \"A128CBCPAD\",\n      \"A192CBC\",\n      \"A192CBCPAD\",\n      \"A256CBC\",\n      \"A256CBCPAD\",\n    ];\n\n    if (parameters.algorithm in algorithmsRequiringIV) {\n      try {\n        const cbcParams = parameters as AesCbcEncryptParameters;\n        if (!cbcParams.iv) {\n          cbcParams.iv = randomBytes(16);\n        }\n      } catch (e: any) {\n        throw new Error(\n          `Unable to initialize IV for algorithm ${parameters.algorithm}. You may pass a valid IV to avoid this error. Error: ${e.message}`,\n        );\n      }\n    }\n  }\n\n  /**\n   * Standardizes the arguments of multiple overloads into a single shape.\n   * @param args - The encrypt arguments\n   */\n  private disambiguateEncryptArguments(\n    args: [EncryptParameters, EncryptOptions?] | [string, Uint8Array, EncryptOptions?],\n  ): [EncryptParameters, EncryptOptions] {\n    if (typeof args[0] === \"string\") {\n      // Sample shape: [\"RSA1_5\", buffer, options]\n      return [\n        {\n          algorithm: args[0],\n          plaintext: args[1],\n        } as EncryptParameters,\n        args[2] || {},\n      ];\n    } else {\n      // Sample shape: [{ algorithm: \"RSA1_5\", plaintext: buffer }, options]\n      return [args[0], (args[1] || {}) as EncryptOptions];\n    }\n  }\n\n  /**\n   * Decrypts the given ciphertext with the specified decryption parameters.\n   * Depending on the algorithm used in the decryption parameters, the set of possible decryption parameters will change.\n   *\n   * Microsoft recommends you not use CBC without first ensuring the integrity of the ciphertext using, for example, an HMAC. See https://learn.microsoft.com/dotnet/standard/security/vulnerabilities-cbc-mode for more information.\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleDecrypt\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey.id, credential);\n   *\n   * const encryptResult = await cryptographyClient.encrypt({\n   *   algorithm: \"RSA1_5\",\n   *   plaintext: Buffer.from(\"My Message\"),\n   * });\n   * console.log(\"encrypt result: \", encryptResult.result);\n   *\n   * const decryptResult = await cryptographyClient.decrypt({\n   *   algorithm: \"RSA1_5\",\n   *   ciphertext: encryptResult.result,\n   * });\n   * console.log(\"decrypt result: \", decryptResult.result.toString());\n   * ```\n   * @param decryptParameters - The decryption parameters.\n   * @param options - Additional options.\n   */\n  public async decrypt(\n    decryptParameters: DecryptParameters,\n    options?: DecryptOptions,\n  ): Promise<DecryptResult>;\n  /**\n   * Decrypts the given ciphertext with the specified cryptography algorithm\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleDecrypt\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey.id, credential);\n   *\n   * const encryptResult = await cryptographyClient.encrypt({\n   *   algorithm: \"RSA1_5\",\n   *   plaintext: Buffer.from(\"My Message\"),\n   * });\n   * console.log(\"encrypt result: \", encryptResult.result);\n   *\n   * const decryptResult = await cryptographyClient.decrypt({\n   *   algorithm: \"RSA1_5\",\n   *   ciphertext: encryptResult.result,\n   * });\n   * console.log(\"decrypt result: \", decryptResult.result.toString());\n   * ```\n   *\n   * Microsoft recommends you not use CBC without first ensuring the integrity of the ciphertext using, for example, an HMAC. See https://learn.microsoft.com/dotnet/standard/security/vulnerabilities-cbc-mode for more information.\n   *\n   * @param algorithm - The algorithm to use.\n   * @param ciphertext - The text to decrypt.\n   * @param options - Additional options.\n   * @deprecated Use `decrypt({ algorithm, ciphertext }, options)` instead.\n   */\n  public decrypt(\n    algorithm: EncryptionAlgorithm,\n    ciphertext: Uint8Array,\n    options?: DecryptOptions,\n  ): Promise<DecryptResult>;\n  public decrypt(\n    ...args:\n      | [DecryptParameters, DecryptOptions?]\n      | [EncryptionAlgorithm, Uint8Array, DecryptOptions?]\n  ): Promise<DecryptResult> {\n    const [parameters, options] = this.disambiguateDecryptArguments(args);\n\n    return tracingClient.withSpan(\"CryptographyClient.decrypt\", options, async (updatedOptions) => {\n      this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.Decrypt);\n      const provider = await this.getProvider(\"decrypt\", parameters.algorithm, updatedOptions);\n      try {\n        return provider.decrypt(parameters, updatedOptions);\n      } catch (error: any) {\n        if (this.remoteProvider) {\n          return this.remoteProvider.decrypt(parameters, updatedOptions);\n        }\n        throw error;\n      }\n    });\n  }\n\n  /**\n   * Standardizes the arguments of multiple overloads into a single shape.\n   * @param args - The decrypt arguments\n   */\n  private disambiguateDecryptArguments(\n    args: [DecryptParameters, DecryptOptions?] | [string, Uint8Array, DecryptOptions?],\n  ): [DecryptParameters, DecryptOptions] {\n    if (typeof args[0] === \"string\") {\n      // Sample shape: [\"RSA1_5\", encryptedBuffer, options]\n      return [\n        {\n          algorithm: args[0],\n          ciphertext: args[1],\n        } as DecryptParameters,\n        args[2] || {},\n      ];\n    } else {\n      // Sample shape: [{ algorithm: \"RSA1_5\", ciphertext: encryptedBuffer }, options]\n      return [args[0], (args[1] || {}) as DecryptOptions];\n    }\n  }\n\n  /**\n   * Wraps the given key using the specified cryptography algorithm\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleWrapKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   *\n   * const wrapResult = await cryptographyClient.wrapKey(\"RSA-OAEP\", Buffer.from(\"My Key\"));\n   * console.log(\"wrap result:\", wrapResult.result);\n   * ```\n   * @param algorithm - The encryption algorithm to use to wrap the given key.\n   * @param key - The key to wrap.\n   * @param options - Additional options.\n   */\n  public wrapKey(\n    algorithm: KeyWrapAlgorithm,\n    key: Uint8Array,\n    options: WrapKeyOptions = {},\n  ): Promise<WrapResult> {\n    return tracingClient.withSpan(\"CryptographyClient.wrapKey\", options, async (updatedOptions) => {\n      this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.WrapKey);\n      const provider = await this.getProvider(\"wrapKey\", algorithm, updatedOptions);\n      try {\n        return provider.wrapKey(algorithm, key, updatedOptions);\n      } catch (err: any) {\n        if (this.remoteProvider) {\n          return this.remoteProvider.wrapKey(algorithm, key, options);\n        }\n        throw err;\n      }\n    });\n  }\n\n  /**\n   * Unwraps the given wrapped key using the specified cryptography algorithm\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleUnwrapKey\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   *\n   * const wrapResult = await cryptographyClient.wrapKey(\"RSA-OAEP\", Buffer.from(\"My Key\"));\n   * console.log(\"wrap result:\", wrapResult.result);\n   *\n   * const unwrapResult = await cryptographyClient.unwrapKey(\"RSA-OAEP\", wrapResult.result);\n   * console.log(\"unwrap result: \", unwrapResult.result);\n   * ```\n   * @param algorithm - The decryption algorithm to use to unwrap the key.\n   * @param encryptedKey - The encrypted key to unwrap.\n   * @param options - Additional options.\n   */\n  public unwrapKey(\n    algorithm: KeyWrapAlgorithm,\n    encryptedKey: Uint8Array,\n    options: UnwrapKeyOptions = {},\n  ): Promise<UnwrapResult> {\n    return tracingClient.withSpan(\n      \"CryptographyClient.unwrapKey\",\n      options,\n      async (updatedOptions) => {\n        this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.UnwrapKey);\n        const provider = await this.getProvider(\"unwrapKey\", algorithm, updatedOptions);\n        try {\n          return provider.unwrapKey(algorithm, encryptedKey, updatedOptions);\n        } catch (err: any) {\n          if (this.remoteProvider) {\n            return this.remoteProvider.unwrapKey(algorithm, encryptedKey, options);\n          }\n          throw err;\n        }\n      },\n    );\n  }\n\n  /**\n   * Cryptographically sign the digest of a message\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleSign\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   * import { createHash } from \"node:crypto\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * let myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   *\n   * const signatureValue = \"MySignature\";\n   * const hash = createHash(\"sha256\");\n   *\n   * const digest = hash.update(signatureValue).digest();\n   * console.log(\"digest: \", digest);\n   *\n   * const signResult = await cryptographyClient.sign(\"RS256\", digest);\n   * console.log(\"sign result: \", signResult.result);\n   * ```\n   * @param algorithm - The signing algorithm to use.\n   * @param digest - The digest of the data to sign.\n   * @param options - Additional options.\n   */\n  public sign(\n    algorithm: SignatureAlgorithm,\n    digest: Uint8Array,\n    options: SignOptions = {},\n  ): Promise<SignResult> {\n    return tracingClient.withSpan(\"CryptographyClient.sign\", options, async (updatedOptions) => {\n      this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.Sign);\n      const provider = await this.getProvider(\"sign\", algorithm, updatedOptions);\n      try {\n        return provider.sign(algorithm, digest, updatedOptions);\n      } catch (err: any) {\n        if (this.remoteProvider) {\n          return this.remoteProvider.sign(algorithm, digest, updatedOptions);\n        }\n        throw err;\n      }\n    });\n  }\n\n  /**\n   * Verify the signed message digest\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleVerify\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   * import { createHash } from \"node:crypto\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   *\n   * const hash = createHash(\"sha256\");\n   * hash.update(\"My Message\");\n   * const digest = hash.digest();\n   *\n   * const signResult = await cryptographyClient.sign(\"RS256\", digest);\n   * console.log(\"sign result: \", signResult.result);\n   *\n   * const verifyResult = await cryptographyClient.verify(\"RS256\", digest, signResult.result);\n   * console.log(\"verify result: \", verifyResult.result);\n   * ```\n   * @param algorithm - The signing algorithm to use to verify with.\n   * @param digest - The digest to verify.\n   * @param signature - The signature to verify the digest against.\n   * @param options - Additional options.\n   */\n  public verify(\n    algorithm: SignatureAlgorithm,\n    digest: Uint8Array,\n    signature: Uint8Array,\n    options: VerifyOptions = {},\n  ): Promise<VerifyResult> {\n    return tracingClient.withSpan(\"CryptographyClient.verify\", options, async (updatedOptions) => {\n      this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.Verify);\n      const provider = await this.getProvider(\"verify\", algorithm, updatedOptions);\n      try {\n        return provider.verify(algorithm, digest, signature, updatedOptions);\n      } catch (err: any) {\n        if (this.remoteProvider) {\n          return this.remoteProvider.verify(algorithm, digest, signature, updatedOptions);\n        }\n        throw err;\n      }\n    });\n  }\n\n  /**\n   * Cryptographically sign a block of data\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleSignData\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   *\n   * const signResult = await cryptographyClient.signData(\"RS256\", Buffer.from(\"My Message\"));\n   * console.log(\"sign result: \", signResult.result);\n   * ```\n   * @param algorithm - The signing algorithm to use.\n   * @param data - The data to sign.\n   * @param options - Additional options.\n   */\n  public signData(\n    algorithm: SignatureAlgorithm,\n    data: Uint8Array,\n    // eslint-disable-next-line @azure/azure-sdk/ts-naming-options\n    options: SignOptions = {},\n  ): Promise<SignResult> {\n    return tracingClient.withSpan(\n      \"CryptographyClient.signData\",\n      options,\n      async (updatedOptions) => {\n        this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.Sign);\n        const provider = await this.getProvider(\"signData\", algorithm, updatedOptions);\n        try {\n          return provider.signData(algorithm, data, updatedOptions);\n        } catch (err: any) {\n          if (this.remoteProvider) {\n            return this.remoteProvider.signData(algorithm, data, options);\n          }\n          throw err;\n        }\n      },\n    );\n  }\n\n  /**\n   * Verify the signed block of data\n   *\n   * Example usage:\n   * ```ts snippet:ReadmeSampleVerifyData\n   * import { DefaultAzureCredential } from \"@azure/identity\";\n   * import { KeyClient, CryptographyClient } from \"@azure/keyvault-keys\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const vaultName = \"<YOUR KEYVAULT NAME>\";\n   * const url = `https://${vaultName}.vault.azure.net`;\n   *\n   * const client = new KeyClient(url, credential);\n   *\n   * const myKey = await client.createKey(\"MyKey\", \"RSA\");\n   * const cryptographyClient = new CryptographyClient(myKey, credential);\n   *\n   * const buffer = Buffer.from(\"My Message\");\n   *\n   * const signResult = await cryptographyClient.signData(\"RS256\", buffer);\n   * console.log(\"sign result: \", signResult.result);\n   *\n   * const verifyResult = await cryptographyClient.verifyData(\"RS256\", buffer, signResult.result);\n   * console.log(\"verify result: \", verifyResult.result);\n   * ```\n   * @param algorithm - The algorithm to use to verify with.\n   * @param data - The signed block of data to verify.\n   * @param signature - The signature to verify the block against.\n   * @param options - Additional options.\n   */\n  public verifyData(\n    algorithm: SignatureAlgorithm,\n    data: Uint8Array,\n    signature: Uint8Array,\n    // eslint-disable-next-line @azure/azure-sdk/ts-naming-options\n    options: VerifyOptions = {},\n  ): Promise<VerifyResult> {\n    return tracingClient.withSpan(\n      \"CryptographyClient.verifyData\",\n      options,\n      async (updatedOptions) => {\n        this.ensureValid(await this.fetchKey(updatedOptions), KnownKeyOperations.Verify);\n        const provider = await this.getProvider(\"verifyData\", algorithm, updatedOptions);\n        try {\n          return provider.verifyData(algorithm, data, signature, updatedOptions);\n        } catch (err: any) {\n          if (this.remoteProvider) {\n            return this.remoteProvider.verifyData(algorithm, data, signature, updatedOptions);\n          }\n          throw err;\n        }\n      },\n    );\n  }\n\n  /**\n   * Retrieves the {@link JsonWebKey} from the Key Vault, if possible. Returns undefined if the key could not be retrieved due to insufficient permissions.\n   * @param options - The additional options.\n   */\n  private async getKeyMaterial(options: GetKeyOptions): Promise<JsonWebKey | undefined> {\n    const key = await this.fetchKey(options);\n\n    switch (key.kind) {\n      case \"JsonWebKey\":\n        return key.value;\n      case \"KeyVaultKey\":\n        return key.value.key!;\n      default:\n        return undefined;\n    }\n  }\n\n  /**\n   * Returns the underlying key used for cryptographic operations.\n   * If needed, attempts to fetch the key from KeyVault and exchanges the ID for the actual key.\n   * @param options - The additional options.\n   */\n  private async fetchKey<T extends OperationOptions>(options: T): Promise<CryptographyClientKey> {\n    if (this.key.kind === \"identifier\") {\n      // Exchange the identifier with the actual key when needed\n      let key: KeyVaultKey | undefined;\n      try {\n        key = await this.remoteProvider!.getKey(options);\n      } catch (e: unknown) {\n        if (isRestError(e) && e.statusCode === 403) {\n          // If we don't have permission to get the key, we'll fall back to using the remote provider.\n          // Marking the key as a remoteOnlyIdentifier will ensure that we don't attempt to fetch the key again.\n          logger.verbose(\n            `Permission denied to get key ${this.key.value}. Falling back to remote operation.`,\n          );\n          this.key = { kind: \"remoteOnlyIdentifier\", value: this.key.value };\n        } else {\n          throw e;\n        }\n      }\n\n      if (key) {\n        this.key = { kind: \"KeyVaultKey\", value: key };\n      }\n    }\n\n    return this.key;\n  }\n\n  private providers?: CryptographyProvider[];\n  /**\n   * Gets the provider that support this algorithm and operation.\n   * The available providers are ordered by priority such that the first provider that supports this\n   * operation is the one we should use.\n   * @param operation - The {@link KeyOperation}.\n   * @param algorithm - The algorithm to use.\n   */\n  private async getProvider<T extends OperationOptions>(\n    operation: CryptographyProviderOperation,\n    algorithm: string,\n    options: T,\n  ): Promise<CryptographyProvider> {\n    if (!this.providers) {\n      const keyMaterial = await this.getKeyMaterial(options);\n      this.providers = [];\n\n      // Add local crypto providers as needed\n      if (keyMaterial) {\n        this.providers.push(\n          new RsaCryptographyProvider(keyMaterial),\n          new AesCryptographyProvider(keyMaterial),\n        );\n      }\n\n      // If the remote provider exists, we're in hybrid-mode. Otherwise we're in local-only mode.\n      // If we're in hybrid mode the remote provider is used as a catch-all and should be last in the list.\n      if (this.remoteProvider) {\n        this.providers.push(this.remoteProvider);\n      }\n    }\n\n    const providers = this.providers.filter((p) => p.isSupported(algorithm, operation));\n\n    if (providers.length === 0) {\n      throw new Error(\n        `Unable to support operation: \"${operation}\" with algorithm: \"${algorithm}\" ${\n          this.key.kind === \"JsonWebKey\" ? \"using a local JsonWebKey\" : \"\"\n        }`,\n      );\n    }\n\n    // Return the first provider that supports this request\n    return providers[0];\n  }\n\n  private ensureValid(key: CryptographyClientKey, operation?: KeyOperation): void {\n    if (key.kind === \"KeyVaultKey\") {\n      const keyOps = key.value.keyOperations;\n      const { notBefore, expiresOn } = key.value.properties;\n      const now = new Date();\n\n      // Check KeyVault Key Expiration\n      if (notBefore && now < notBefore) {\n        throw new Error(`Key ${key.value.id} can't be used before ${notBefore.toISOString()}`);\n      }\n\n      if (expiresOn && now > expiresOn) {\n        throw new Error(`Key ${key.value.id} expired at ${expiresOn.toISOString()}`);\n      }\n\n      // Check Key operations\n      if (operation && keyOps && !keyOps?.includes(operation)) {\n        throw new Error(`Operation ${operation} is not supported on key ${key.value.id}`);\n      }\n    } else if (key.kind === \"JsonWebKey\") {\n      // Check JsonWebKey Key operations\n      if (operation && key.value.keyOps && !key.value.keyOps?.includes(operation)) {\n        throw new Error(`Operation ${operation} is not supported on key ${key.value.kid}`);\n      }\n    }\n  }\n}\n"]}