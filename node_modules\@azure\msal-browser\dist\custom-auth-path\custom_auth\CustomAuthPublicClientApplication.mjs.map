{"version": 3, "file": "CustomAuthPublicClientApplication.mjs", "sources": ["../../../../src/custom_auth/CustomAuthPublicClientApplication.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAGG;AA0BG,MAAO,iCACT,SAAQ,uBAAuB,CAAA;AAK/B;;;;AAIG;AACH,IAAA,aAAa,MAAM,CACf,MAA+B,EAAA;AAE/B,QAAA,iCAAiC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEzD,MAAM,oBAAoB,GAAG,IAAI,4BAA4B,CACzD,IAAI,0BAA0B,CAAC,MAAM,CAAC,CACzC,CAAC;AAEF,QAAA,MAAM,oBAAoB,CAAC,UAAU,EAAE,CAAC;QAExC,MAAM,GAAG,GAAG,IAAI,iCAAiC,CAC7C,MAAM,EACN,oBAAoB,CACvB,CAAC;AAEF,QAAA,OAAO,GAAG,CAAC;KACd;IAED,WACI,CAAA,MAA+B,EAC/B,UAAyC,EAAA;AAEzC,QAAA,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAE1B,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC;KAC1C;AAED;;;;AAIG;AACH,IAAA,iBAAiB,CACb,sBAA+C,EAAA;QAE/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAC9C,sBAAsB,CACzB,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,YAA0B,EAAA;QAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KACzD;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,YAA0B,EAAA;QAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KACzD;AAED;;;;;;AAMG;AACH,IAAA,aAAa,CACT,mBAAwC,EAAA;QAExC,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;KACvE;AAED;;;;AAIG;IACK,OAAO,cAAc,CAAC,MAA+B,EAAA;;QAEzD,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,MAAM,IAAI,yBAAyB,CAC/B,oBAAoB,EACpB,+BAA+B,CAClC,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE;AACzB,YAAA,MAAM,IAAI,yBAAyB,CAC/B,gBAAgB,EAChB,CAAA,mBAAA,EAAsB,MAAM,CAAC,IAAI,EAAE,SAAS,CAAA,aAAA,CAAe,CAC9D,CAAC;AACL,SAAA;AAED,QAAA,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC;QAExD,IAAI,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/C,YAAA,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,KAAI;AACrC,gBAAA,MAAM,sBAAsB,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;AAC3D,gBAAA,IACI,sBAAsB,KAAK,aAAa,CAAC,QAAQ;oBACjD,sBAAsB,KAAK,aAAa,CAAC,GAAG;AAC5C,oBAAA,sBAAsB,KAAK,aAAa,CAAC,QAAQ,EACnD;AACE,oBAAA,MAAM,IAAI,yBAAyB,CAC/B,oBAAoB,EACpB,kBAAkB,aAAa,CAAA,mEAAA,EAAsE,MAAM,CAAC,MAAM,CAC9G,aAAa,CAChB,CAAA,CAAE,CACN,CAAC;AACL,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AACJ;;;;"}