/*! @azure/msal-browser v4.16.0 2025-07-23 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const SIGN_UP_COMPLETED_RESULT_TYPE = "SignUpCompletedResult";
const SIGN_UP_PASSWORD_REQUIRED_RESULT_TYPE = "SignUpPasswordRequiredResult";
const SIGN_UP_CODE_REQUIRED_RESULT_TYPE = "SignUpCodeRequiredResult";
const SIGN_UP_ATTRIBUTES_REQUIRED_RESULT_TYPE = "SignUpAttributesRequiredResult";
function createSignUpCompletedResult(input) {
    return {
        type: SIGN_UP_COMPLETED_RESULT_TYPE,
        ...input,
    };
}
function createSignUpPasswordRequiredResult(input) {
    return {
        type: SIGN_UP_PASSWORD_REQUIRED_RESULT_TYPE,
        ...input,
    };
}
function createSignUpCodeRequiredResult(input) {
    return {
        type: SIGN_UP_CODE_REQUIRED_RESULT_TYPE,
        ...input,
    };
}
function createSignUpAttributesRequiredResult(input) {
    return {
        type: SIGN_UP_ATTRIBUTES_REQUIRED_RESULT_TYPE,
        ...input,
    };
}

export { SIGN_UP_ATTRIBUTES_REQUIRED_RESULT_TYPE, SIGN_UP_CODE_REQUIRED_RESULT_TYPE, SIGN_UP_COMPLETED_RESULT_TYPE, SIGN_UP_PASSWORD_REQUIRED_RESULT_TYPE, createSignUpAttributesRequiredResult, createSignUpCodeRequiredResult, createSignUpCompletedResult, createSignUpPasswordRequiredResult };
//# sourceMappingURL=SignUpActionResult.mjs.map
