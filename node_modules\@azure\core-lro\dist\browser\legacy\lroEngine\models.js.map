{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../../src/legacy/lroEngine/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroResourceLocationConfig, RawResponse } from \"../../http/models.js\";\n\n/**\n * Options for the LRO poller.\n */\nexport interface LroEngineOptions<TResult, TState> {\n  /**\n   * Defines how much time the poller is going to wait before making a new request to the service.\n   */\n  intervalInMs?: number;\n  /**\n   * A serialized poller which can be used to resume an existing paused Long-Running-Operation.\n   */\n  resumeFrom?: string;\n  /**\n   * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n   */\n  lroResourceLocationConfig?: LroResourceLocationConfig;\n  /**\n   * A function to process the result of the LRO.\n   */\n  processResult?: (result: unknown, state: TState) => TResult;\n  /**\n   * A function to process the state of the LRO.\n   */\n  updateState?: (state: TState, lastResponse: RawResponse) => void;\n  /**\n   * A predicate to determine whether the LRO finished processing.\n   */\n  isDone?: (lastResponse: unknown, state: TState) => boolean;\n  /**\n   * Control whether to throw an exception if the operation failed or was canceled.\n   */\n  resolveOnUnsuccessful?: boolean;\n}\n\nexport interface PollerConfig {\n  intervalInMs: number;\n}\n"]}