{"version": 3, "file": "RequestParameterBuilder.mjs", "sources": ["../../src/request/RequestParameterBuilder.ts"], "sourcesContent": [null], "names": ["AADServerParamKeys.CLIENT_ID", "AADServerParamKeys.BROKER_CLIENT_ID", "AADServerParamKeys.REDIRECT_URI", "AADServerParamKeys.RESPONSE_TYPE", "AADServerParamKeys.RESPONSE_MODE", "AADServerParamKeys.NATIVE_BROKER", "AADServerParamKeys.SCOPE", "AADServerParamKeys.POST_LOGOUT_URI", "AADServerParamKeys.ID_TOKEN_HINT", "AADServerParamKeys.DOMAIN_HINT", "AADServerParamKeys.LOGIN_HINT", "AADServerParamKeys.SID", "ClientConfigurationErrorCodes.invalidClaims", "AADServerParamKeys.CLAIMS", "AADServerParamKeys.CLIENT_REQUEST_ID", "AADServerParamKeys.X_CLIENT_SKU", "AADServerParamKeys.X_CLIENT_VER", "AADServerParamKeys.X_CLIENT_OS", "AADServerParamKeys.X_CLIENT_CPU", "AADServerParamKeys.X_APP_NAME", "AADServerParamKeys.X_APP_VER", "AADServerParamKeys.PROMPT", "AADServerParamKeys.STATE", "AADServerParamKeys.NONCE", "AADServerParamKeys.CODE_CHALLENGE", "AADServerParamKeys.CODE_CHALLENGE_METHOD", "ClientConfigurationErrorCodes.pkceParamsMissing", "AADServerParamKeys.CODE", "AADServerParamKeys.DEVICE_CODE", "AADServerParamKeys.REFRESH_TOKEN", "AADServerParamKeys.CODE_VERIFIER", "AADServerParamKeys.CLIENT_SECRET", "AADServerParamKeys.CLIENT_ASSERTION", "AADServerParamKeys.CLIENT_ASSERTION_TYPE", "AADServerParamKeys.OBO_ASSERTION", "AADServerParamKeys.REQUESTED_TOKEN_USE", "AADServerParamKeys.GRANT_TYPE", "AADServerParamKeys.INSTANCE_AWARE", "AADServerParamKeys.TOKEN_TYPE", "AADServerParamKeys.REQ_CNF", "AADServerParamKeys.X_CLIENT_CURR_TELEM", "AADServerParamKeys.X_CLIENT_LAST_TELEM", "AADServerParamKeys.X_MS_LIB_CAPABILITY", "AADServerParamKeys.LOGOUT_HINT", "AADServerParamKeys.BROKER_REDIRECT_URI", "AADServerParamKeys.EAR_JWK", "AADServerParamKeys.EAR_JWE_CRYPTO"], "mappings": ";;;;;;;;AAAA;;;AAGG;SA4Ba,sBAAsB,CAClC,UAA+B,EAC/B,aAAsB,EACtB,iBAAsC,EAAA;IAEtC,IAAI,CAAC,aAAa,EAAE;QAChB,OAAO;AACV,KAAA;IAED,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAACA,SAA4B,CAAC,CAAC;IAC9D,IAAI,QAAQ,IAAI,UAAU,CAAC,GAAG,CAACC,gBAAmC,CAAC,EAAE;QACjE,iBAAiB,EAAE,SAAS,CACxB;AACI,YAAA,gBAAgB,EAAE,QAAQ;YAC1B,mBAAmB,EAAE,UAAU,CAAC,GAAG,CAC/BC,YAA+B,CAClC;SACJ,EACD,aAAa,CAChB,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;AAIG;AACa,SAAA,eAAe,CAC3B,UAA+B,EAC/B,YAA+B,EAAA;IAE/B,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC3B,UAA+B,EAC/B,YAA2B,EAAA;AAE3B,IAAA,UAAU,CAAC,GAAG,CACVC,aAAgC,EAChC,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC,KAAK,CACnD,CAAC;AACN,CAAC;AAED;;AAEG;AACG,SAAU,eAAe,CAAC,UAA+B,EAAA;IAC3D,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,GAAG,CAAC,CAAC;AAC1D,CAAC;AAED;;;;AAIG;AACG,SAAU,SAAS,CACrB,UAA+B,EAC/B,MAAgB,EAChB,aAAyB,GAAA,IAAI,EAC7B,aAAA,GAA+B,mBAAmB,EAAA;;AAGlD,IAAA,IACI,aAAa;AACb,QAAA,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACjC,QAAA,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAC5B;AACE,QAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,KAAA;IACD,MAAM,aAAa,GAAG,aAAa;UAC7B,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC;AACvC,UAAE,MAAM,IAAI,EAAE,CAAC;AACnB,IAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC7C,IAAA,UAAU,CAAC,GAAG,CAACC,KAAwB,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;AACrE,CAAC;AAED;;;AAGG;AACa,SAAA,WAAW,CACvB,UAA+B,EAC/B,QAAgB,EAAA;IAEhB,UAAU,CAAC,GAAG,CAACN,SAA4B,EAAE,QAAQ,CAAC,CAAC;AAC3D,CAAC;AAED;;;AAGG;AACa,SAAA,cAAc,CAC1B,UAA+B,EAC/B,WAAmB,EAAA;IAEnB,UAAU,CAAC,GAAG,CAACE,YAA+B,EAAE,WAAW,CAAC,CAAC;AACjE,CAAC;AAED;;;AAGG;AACa,SAAA,wBAAwB,CACpC,UAA+B,EAC/B,WAAmB,EAAA;IAEnB,UAAU,CAAC,GAAG,CAACK,eAAkC,EAAE,WAAW,CAAC,CAAC;AACpE,CAAC;AAED;;;AAGG;AACa,SAAA,cAAc,CAC1B,UAA+B,EAC/B,WAAmB,EAAA;IAEnB,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,WAAW,CAAC,CAAC;AAClE,CAAC;AAED;;;AAGG;AACa,SAAA,aAAa,CACzB,UAA+B,EAC/B,UAAkB,EAAA;IAElB,UAAU,CAAC,GAAG,CAACC,WAA8B,EAAE,UAAU,CAAC,CAAC;AAC/D,CAAC;AAED;;;AAGG;AACa,SAAA,YAAY,CACxB,UAA+B,EAC/B,SAAiB,EAAA;IAEjB,UAAU,CAAC,GAAG,CAACC,UAA6B,EAAE,SAAS,CAAC,CAAC;AAC7D,CAAC;AAED;;;AAGG;AACa,SAAA,SAAS,CACrB,UAA+B,EAC/B,SAAiB,EAAA;IAEjB,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,CAAO,IAAA,EAAA,SAAS,CAAE,CAAA,CAAC,CAAC;AAC/D,CAAC;AAED;;;AAGG;AACa,SAAA,SAAS,CACrB,UAA+B,EAC/B,UAAsB,EAAA;AAEtB,IAAA,UAAU,CAAC,GAAG,CACV,WAAW,CAAC,UAAU,EACtB,CAAA,IAAA,EAAO,UAAU,CAAC,GAAG,CAAI,CAAA,EAAA,UAAU,CAAC,IAAI,CAAA,CAAE,CAC7C,CAAC;AACN,CAAC;AAED;;;AAGG;AACa,SAAA,MAAM,CAAC,UAA+B,EAAE,GAAW,EAAA;IAC/D,UAAU,CAAC,GAAG,CAACC,GAAsB,EAAE,GAAG,CAAC,CAAC;AAChD,CAAC;AAED;;;AAGG;SACa,SAAS,CACrB,UAA+B,EAC/B,MAAe,EACf,kBAAkC,EAAA;IAElC,MAAM,YAAY,GAAG,6BAA6B,CAC9C,MAAM,EACN,kBAAkB,CACrB,CAAC;IACF,IAAI;AACA,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC5B,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,KAAA;IACD,UAAU,CAAC,GAAG,CAACC,MAAyB,EAAE,YAAY,CAAC,CAAC;AAC5D,CAAC;AAED;;;AAGG;AACa,SAAA,gBAAgB,CAC5B,UAA+B,EAC/B,aAAqB,EAAA;IAErB,UAAU,CAAC,GAAG,CAACC,iBAAoC,EAAE,aAAa,CAAC,CAAC;AACxE,CAAC;AAED;;;AAGG;AACa,SAAA,cAAc,CAC1B,UAA+B,EAC/B,WAAwB,EAAA;;IAGxB,UAAU,CAAC,GAAG,CAACC,YAA+B,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IACjE,UAAU,CAAC,GAAG,CAACC,YAA+B,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;IACrE,IAAI,WAAW,CAAC,EAAE,EAAE;QAChB,UAAU,CAAC,GAAG,CAACC,WAA8B,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;AAClE,KAAA;IACD,IAAI,WAAW,CAAC,GAAG,EAAE;QACjB,UAAU,CAAC,GAAG,CAACC,YAA+B,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;AACpE,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,uBAAuB,CACnC,UAA+B,EAC/B,YAAkC,EAAA;IAElC,IAAI,YAAY,EAAE,OAAO,EAAE;QACvB,UAAU,CAAC,GAAG,CAACC,UAA6B,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;AACvE,KAAA;IAED,IAAI,YAAY,EAAE,UAAU,EAAE;QAC1B,UAAU,CAAC,GAAG,CAACC,SAA4B,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;AACzE,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,SAAS,CACrB,UAA+B,EAC/B,MAAc,EAAA;IAEd,UAAU,CAAC,GAAG,CAACC,MAAyB,EAAE,MAAM,CAAC,CAAC;AACtD,CAAC;AAED;;;AAGG;AACa,SAAA,QAAQ,CAAC,UAA+B,EAAE,KAAa,EAAA;AACnE,IAAA,IAAI,KAAK,EAAE;QACP,UAAU,CAAC,GAAG,CAACC,KAAwB,EAAE,KAAK,CAAC,CAAC;AACnD,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,QAAQ,CAAC,UAA+B,EAAE,KAAa,EAAA;IACnE,UAAU,CAAC,GAAG,CAACC,KAAwB,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC;AAED;;;;;AAKG;SACa,sBAAsB,CAClC,UAA+B,EAC/B,aAAsB,EACtB,mBAA4B,EAAA;IAE5B,IAAI,aAAa,IAAI,mBAAmB,EAAE;QACtC,UAAU,CAAC,GAAG,CAACC,cAAiC,EAAE,aAAa,CAAC,CAAC;QACjE,UAAU,CAAC,GAAG,CACVC,qBAAwC,EACxC,mBAAmB,CACtB,CAAC;AACL,KAAA;AAAM,SAAA;AACH,QAAA,MAAM,8BAA8B,CAChCC,iBAA+C,CAClD,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,oBAAoB,CAChC,UAA+B,EAC/B,IAAY,EAAA;IAEZ,UAAU,CAAC,GAAG,CAACC,IAAuB,EAAE,IAAI,CAAC,CAAC;AAClD,CAAC;AAED;;;AAGG;AACa,SAAA,aAAa,CACzB,UAA+B,EAC/B,IAAY,EAAA;IAEZ,UAAU,CAAC,GAAG,CAACC,WAA8B,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC3B,UAA+B,EAC/B,YAAoB,EAAA;IAEpB,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC3B,UAA+B,EAC/B,YAAoB,EAAA;IAEpB,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC3B,UAA+B,EAC/B,YAAoB,EAAA;IAEpB,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED;;;AAGG;AACa,SAAA,kBAAkB,CAC9B,UAA+B,EAC/B,eAAuB,EAAA;AAEvB,IAAA,IAAI,eAAe,EAAE;QACjB,UAAU,CAAC,GAAG,CAACC,gBAAmC,EAAE,eAAe,CAAC,CAAC;AACxE,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,sBAAsB,CAClC,UAA+B,EAC/B,mBAA2B,EAAA;AAE3B,IAAA,IAAI,mBAAmB,EAAE;QACrB,UAAU,CAAC,GAAG,CACVC,qBAAwC,EACxC,mBAAmB,CACtB,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC3B,UAA+B,EAC/B,YAAoB,EAAA;IAEpB,UAAU,CAAC,GAAG,CAACC,aAAgC,EAAE,YAAY,CAAC,CAAC;AACnE,CAAC;AAED;;;AAGG;AACa,SAAA,kBAAkB,CAC9B,UAA+B,EAC/B,QAAgB,EAAA;IAEhB,UAAU,CAAC,GAAG,CAACC,mBAAsC,EAAE,QAAQ,CAAC,CAAC;AACrE,CAAC;AAED;;;AAGG;AACa,SAAA,YAAY,CACxB,UAA+B,EAC/B,SAAiB,EAAA;IAEjB,UAAU,CAAC,GAAG,CAACC,UAA6B,EAAE,SAAS,CAAC,CAAC;AAC7D,CAAC;AAED;;;AAGG;AACG,SAAU,aAAa,CAAC,UAA+B,EAAA;AACzD,IAAA,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACrC,CAAC;AAEK,SAAU,gBAAgB,CAAC,UAA+B,EAAA;IAC5D,IAAI,CAAC,UAAU,CAAC,GAAG,CAACC,cAAiC,CAAC,EAAE;QACpD,UAAU,CAAC,GAAG,CAACA,cAAiC,EAAE,MAAM,CAAC,CAAC;AAC7D,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,uBAAuB,CACnC,UAA+B,EAC/B,QAAoB,EAAA;AAEpB,IAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;QAC9C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE;AAC/B,YAAA,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9B,SAAA;AACL,KAAC,CAAC,CAAC;AACP,CAAC;AAEe,SAAA,6BAA6B,CACzC,MAAe,EACf,kBAAkC,EAAA;AAElC,IAAA,IAAI,YAAoB,CAAC;;IAGzB,IAAI,CAAC,MAAM,EAAE;QACT,YAAY,GAAG,EAAE,CAAC;AACrB,KAAA;AAAM,SAAA;QACH,IAAI;AACA,YAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACrC,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,8BAA8B,CAChCzB,aAA2C,CAC9C,CAAC;AACL,SAAA;AACJ,KAAA;AAED,IAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;QACrD,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;;AAE9D,YAAA,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACrD,SAAA;;QAGD,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC;AAClE,YAAA;AACI,gBAAA,MAAM,EAAE,kBAAkB;aAC7B,CAAC;AACT,KAAA;AAED,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;AACxC,CAAC;AAED;;;AAGG;AACa,SAAA,WAAW,CACvB,UAA+B,EAC/B,QAAgB,EAAA;IAEhB,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED;;;AAGG;AACa,SAAA,WAAW,CACvB,UAA+B,EAC/B,QAAgB,EAAA;IAEhB,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED;;;AAGG;AACa,SAAA,WAAW,CACvB,UAA+B,EAC/B,SAAiB,EAAA;AAEjB,IAAA,IAAI,SAAS,EAAE;QACX,UAAU,CAAC,GAAG,CAAC0B,UAA6B,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACxE,UAAU,CAAC,GAAG,CAACC,OAA0B,EAAE,SAAS,CAAC,CAAC;AACzD,KAAA;AACL,CAAC;AAED;;AAEG;AACa,SAAA,SAAS,CACrB,UAA+B,EAC/B,YAAoB,EAAA;AAEpB,IAAA,IAAI,YAAY,EAAE;QACd,UAAU,CAAC,GAAG,CAACD,UAA6B,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACxE,UAAU,CAAC,GAAG,CAACC,OAA0B,EAAE,YAAY,CAAC,CAAC;AAC5D,KAAA;AACL,CAAC;AAED;;;AAGG;AACa,SAAA,kBAAkB,CAC9B,UAA+B,EAC/B,sBAA8C,EAAA;AAE9C,IAAA,UAAU,CAAC,GAAG,CACVC,mBAAsC,EACtC,sBAAsB,CAAC,iCAAiC,EAAE,CAC7D,CAAC;AACF,IAAA,UAAU,CAAC,GAAG,CACVC,mBAAsC,EACtC,sBAAsB,CAAC,8BAA8B,EAAE,CAC1D,CAAC;AACN,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAC,UAA+B,EAAA;IACzD,UAAU,CAAC,GAAG,CACVC,mBAAsC,EACtC,mBAAmB,CAAC,yBAAyB,CAChD,CAAC;AACN,CAAC;AAED;;AAEG;AACa,SAAA,aAAa,CACzB,UAA+B,EAC/B,UAAkB,EAAA;IAElB,UAAU,CAAC,GAAG,CAACC,WAA8B,EAAE,UAAU,CAAC,CAAC;AAC/D,CAAC;SAEe,mBAAmB,CAC/B,UAA+B,EAC/B,cAAsB,EACtB,iBAAyB,EAAA;IAEzB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC1C,gBAAmC,CAAC,EAAE;QACtD,UAAU,CAAC,GAAG,CAACA,gBAAmC,EAAE,cAAc,CAAC,CAAC;AACvE,KAAA;IACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC2C,mBAAsC,CAAC,EAAE;QACzD,UAAU,CAAC,GAAG,CACVA,mBAAsC,EACtC,iBAAiB,CACpB,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;AAIG;AACa,SAAA,gBAAgB,CAC5B,UAA+B,EAC/B,GAAW,EAAA;AAEX,IAAA,UAAU,CAAC,GAAG,CAACC,OAA0B,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;;IAGpE,MAAM,mBAAmB,GAAG,yCAAyC,CAAC;IACtE,UAAU,CAAC,GAAG,CAACC,cAAiC,EAAE,mBAAmB,CAAC,CAAC;AAC3E,CAAC;AAED;;;;AAIG;AACa,SAAA,qBAAqB,CACjC,UAA+B,EAC/B,cAA0B,EAAA;AAE1B,IAAA,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;AACpD,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9B,SAAA;AACL,KAAC,CAAC,CAAC;AACP;;;;"}