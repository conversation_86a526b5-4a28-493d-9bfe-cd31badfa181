{"version": 3, "file": "ServiceFabric.mjs", "sources": ["../../../src/client/ManagedIdentitySources/ServiceFabric.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAiBH,MAAM,8BAA8B,GAAW,oBAAoB,CAAC;AAEpE;;AAEG;AACG,MAAO,aAAc,SAAQ,yBAAyB,CAAA;AAIxD;;;;;;;;;AASG;AACH,IAAA,WAAA,CACI,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,gBAAwB,EACxB,cAAsB,EAAA;QAEtB,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,CACzB,CAAC;AAEF,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;KACxC;AAED;;;AAGG;AACI,IAAA,OAAO,uBAAuB,GAAA;QACjC,MAAM,gBAAgB,GAClB,OAAO,CAAC,GAAG,CACP,uCAAuC,CAAC,iBAAiB,CAC5D,CAAC;QACN,MAAM,cAAc,GAChB,OAAO,CAAC,GAAG,CACP,uCAAuC,CAAC,eAAe,CAC1D,CAAC;AACN,QAAA,MAAM,wBAAwB,GAC1B,OAAO,CAAC,GAAG,CACP,uCAAuC;AAClC,aAAA,0BAA0B,CAClC,CAAC;AAEN,QAAA,OAAO,CAAC,gBAAgB,EAAE,cAAc,EAAE,wBAAwB,CAAC,CAAC;KACvE;AAED;;;;;;;;;AASG;AACI,IAAA,OAAO,SAAS,CACnB,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,iBAAoC,EAAA;AAEpC,QAAA,MAAM,CAAC,gBAAgB,EAAE,cAAc,EAAE,wBAAwB,CAAC,GAC9D,aAAa,CAAC,uBAAuB,EAAE,CAAC;QAE5C,IAAI,CAAC,gBAAgB,IAAI,CAAC,cAAc,IAAI,CAAC,wBAAwB,EAAE;YACnE,MAAM,CAAC,IAAI,CACP,CAAA,mBAAA,EAAsB,0BAA0B,CAAC,cAAc,+DAA+D,uCAAuC,CAAC,eAAe,CAAO,IAAA,EAAA,uCAAuC,CAAC,iBAAiB,CAAA,MAAA,EAAS,uCAAuC,CAAC,0BAA0B,CAA0C,wCAAA,CAAA,CAC7W,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,MAAM,yBAAyB,GAC3B,aAAa,CAAC,gCAAgC,CAC1C,uCAAuC,CAAC,iBAAiB,EACzD,gBAAgB,EAChB,0BAA0B,CAAC,cAAc,EACzC,MAAM,CACT,CAAC;AAEN,QAAA,MAAM,CAAC,IAAI,CACP,CAAA,+DAAA,EAAkE,0BAA0B,CAAC,cAAc,CAAoC,iCAAA,EAAA,yBAAyB,cAAc,0BAA0B,CAAC,cAAc,CAAA,kBAAA,CAAoB,CACtP,CAAC;AAEF,QAAA,IACI,iBAAiB,CAAC,MAAM,KAAK,qBAAqB,CAAC,eAAe,EACpE;YACE,MAAM,CAAC,OAAO,CACV,CAAA,mBAAA,EAAsB,0BAA0B,CAAC,cAAc,CAAsN,oNAAA,CAAA,CACxR,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,aAAa,CACpB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,gBAAgB,EAChB,cAAc,CACjB,CAAC;KACL;AAED;;;;;AAKG;IACI,aAAa,CAChB,QAAgB,EAChB,iBAAoC,EAAA;AAEpC,QAAA,MAAM,OAAO,GACT,IAAI,gCAAgC,CAChC,UAAU,CAAC,GAAG,EACd,IAAI,CAAC,gBAAgB,CACxB,CAAC;AAEN,QAAA,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,4BAA4B,CAAC;YAChE,IAAI,CAAC,cAAc,CAAC;AAExB,QAAA,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,WAAW,CAAC;AAC/D,YAAA,8BAA8B,CAAC;AACnC,QAAA,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,QAAQ,CAAC;AAC5D,YAAA,QAAQ,CAAC;AAEb,QAAA,IACI,iBAAiB,CAAC,MAAM,KAAK,qBAAqB,CAAC,eAAe,EACpE;AACE,YAAA,OAAO,CAAC,eAAe,CACnB,IAAI,CAAC,iDAAiD,CAClD,iBAAiB,CAAC,MAAM,CAC3B,CACJ,GAAG,iBAAiB,CAAC,EAAE,CAAC;AAC5B,SAAA;;AAID,QAAA,OAAO,OAAO,CAAC;KAClB;AACJ;;;;"}