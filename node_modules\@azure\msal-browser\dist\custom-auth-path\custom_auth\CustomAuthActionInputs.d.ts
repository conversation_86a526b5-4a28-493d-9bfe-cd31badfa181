import { UserAccountAttributes } from "./UserAccountAttributes.js";
export type CustomAuthActionInputs = {
    correlationId?: string;
};
export type AccountRetrievalInputs = CustomAuthActionInputs;
export type SignInInputs = CustomAuthActionInputs & {
    username: string;
    password?: string;
    scopes?: Array<string>;
};
export type SignUpInputs = CustomAuthActionInputs & {
    username: string;
    password?: string;
    attributes?: UserAccountAttributes;
};
export type ResetPasswordInputs = CustomAuthActionInputs & {
    username: string;
};
export type AccessTokenRetrievalInputs = {
    forceRefresh: boolean;
    scopes?: Array<string>;
};
export type SignInWithContinuationTokenInputs = {
    scopes?: Array<string>;
};
//# sourceMappingURL=CustomAuthActionInputs.d.ts.map