{"version": 3, "file": "CustomAuthPublicClientApplication.d.ts", "sourceRoot": "", "sources": ["../../../src/custom_auth/CustomAuthPublicClientApplication.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oDAAoD,CAAC;AACtF,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAG1E,OAAO,EAAE,kCAAkC,EAAE,MAAM,yCAAyC,CAAC;AAC7F,OAAO,EACH,sBAAsB,EACtB,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACtB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,uBAAuB,EAAE,MAAM,4CAA4C,CAAC;AAErF,OAAO,EAAE,wBAAwB,EAAE,MAAM,+DAA+D,CAAC;AAGzG,OAAO,EAAE,uBAAuB,EAAE,MAAM,mCAAmC,CAAC;AAO5E,qBAAa,iCACT,SAAQ,uBACR,YAAW,kCAAkC;IAE7C,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAgC;IAErE;;;;OAIG;WACU,MAAM,CACf,MAAM,EAAE,uBAAuB,GAChC,OAAO,CAAC,kCAAkC,CAAC;IAiB9C,OAAO;IASP;;;;OAIG;IACH,iBAAiB,CACb,sBAAsB,CAAC,EAAE,sBAAsB,GAChD,gBAAgB;IAMnB;;;;;;OAMG;IACH,MAAM,CAAC,YAAY,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAIzD;;;;;;OAMG;IACH,MAAM,CAAC,YAAY,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;IAIzD;;;;;;OAMG;IACH,aAAa,CACT,mBAAmB,EAAE,mBAAmB,GACzC,OAAO,CAAC,wBAAwB,CAAC;IAIpC;;;;OAIG;IACH,OAAO,CAAC,MAAM,CAAC,cAAc;CAoChC"}