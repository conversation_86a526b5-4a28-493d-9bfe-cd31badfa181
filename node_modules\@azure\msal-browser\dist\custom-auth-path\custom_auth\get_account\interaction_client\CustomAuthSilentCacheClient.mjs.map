{"version": 3, "file": "CustomAuthSilentCacheClient.mjs", "sources": ["../../../../../../src/custom_auth/get_account/interaction_client/CustomAuthSilentCacheClient.ts"], "sourcesContent": [null], "names": ["PublicApiId.ACCOUNT_GET_ACCESS_TOKEN"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAsBG,MAAO,2BAA4B,SAAQ,+BAA+B,CAAA;AAC5E;;;;;;;;;AASG;IACM,MAAM,YAAY,CACvB,aAAsC,EAAA;QAEtC,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAC1DA,wBAAoC,CACvC,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,gCAAgC,CACtD,gBAAgB,EAChB,IAAI,CAAC,mBAAmB,CAC3B,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CACzC,YAAY,EACZ,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI;YACA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD,EAClD,IAAI,CAAC,aAAa,CACrB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,kBAAkB,CACpD,aAAa,CAChB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yEAAyE,EACzE,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,YAAA,OAAO,MAAM,CAAC,CAAC,CAAyB,CAAC;AAC5C,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IACI,KAAK,YAAY,eAAe;AAChC,gBAAA,KAAK,CAAC,SAAS,KAAK,oBAAoB,CAAC,oBAAoB,EAC/D;gBACE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qDAAqD,EACrD,IAAI,CAAC,aAAa,CACrB,CAAC;gBAEF,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAC7C,YAAY,EACZ,IAAI,CAAC,iBAAiB,CACzB,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wCAAwC,EACxC,IAAI,CAAC,aAAa,CACrB,CAAC;gBAEF,MAAM,kBAAkB,GACpB,MAAM,kBAAkB,CAAC,0BAA0B,CAC/C,aAAa,CAChB,CAAC;gBAEN,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4CAA4C,EAC5C,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,gBAAA,OAAO,kBAA0C,CAAC;AACrD,aAAA;AAED,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ;IAEQ,MAAM,MAAM,CAAC,aAAiC,EAAA;QACnD,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;;QAGvE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0BAA0B,EAC1B,aAAa,EAAE,aAAa,CAC/B,CAAC;AACF,QAAA,MAAM,IAAI,CAAC,kBAAkB,CACzB,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,EAAE,OAAO,CAC9B,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAEnE,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAErE,QAAA,IAAI,qBAAqB,EAAE;YACvB,MAAM,mBAAmB,GAAG,SAAS,CAAC,cAAc,CAChD,qBAAqB,EACrB,aAAa,EAAE,CAClB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qDAAqD,EACrD,aAAa,EAAE,aAAa,CAC/B,CAAC;;AAGF,YAAA,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;gBAC9D,KAAK,EAAE,KAAK,CAAC,MAAM;AACnB,gBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,gBAAA,SAAS,EAAE,KAAK;AACnB,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,aAAqB,EAAA;QACnC,IAAI,OAAO,GAAuB,IAAI,CAAC;QAEvC,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,uCAAuC,EACvC,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAClD,EAAE,EACF,aAAa,CAChB,CAAC;AAEF,QAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,YAAA,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sFAAsF,EACtF,aAAa,CAChB,CAAC;AACL,aAAA;AAED,YAAA,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC;AAChE,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;IAEO,gCAAgC,CACpC,sBAA8C,EAC9C,mBAAwC,EAAA;QAExC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;QAEhD,OAAO;AACH,YAAA,WAAW,EAAE;AACT,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,gBAAA,SAAS,EAAE,mBAAmB;AAC9B,gBAAA,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;AACvD,gBAAA,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW;AAC5C,aAAA;AACD,YAAA,aAAa,EAAE;AACX,gBAAA,yBAAyB,EACrB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AAChD,gBAAA,oBAAoB,EAAE,IAAI;AAC7B,aAAA;AACD,YAAA,aAAa,EAAE;gBACX,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;gBAC3C,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;AACpC,aAAA;AACD,YAAA,YAAY,EAAE;AACV,gBAAA,yBAAyB,EACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB;AAClD,aAAA;YACD,eAAe,EAAE,IAAI,CAAC,aAAa;YACnC,gBAAgB,EAAE,IAAI,CAAC,aAAa;YACpC,gBAAgB,EAAE,IAAI,CAAC,cAAc;AACrC,YAAA,sBAAsB,EAAE,sBAAsB;AAC9C,YAAA,WAAW,EAAE;gBACT,GAAG,EAAE,kBAAkB,CAAC,GAAG;gBAC3B,OAAO,EAAE,kBAAkB,CAAC,OAAO;gBACnC,GAAG,EAAE,kBAAkB,CAAC,GAAG;gBAC3B,EAAE,EAAE,kBAAkB,CAAC,EAAE;AAC5B,aAAA;AACD,YAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;SACnC,CAAC;KACL;AACJ;;;;"}